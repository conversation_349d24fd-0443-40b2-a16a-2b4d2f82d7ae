/* Home Page Styles */

/* CSS Variables - 内联定义 */
:root {
  /* 主色调 */
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 状态颜色 */
  --success-color: #27ae60;
  --error-color: #e74c3c;
  --warning-color: #f57c00;
  --info-color: #1976d2;
  --info-bg: #e3f2fd;
  
  /* 中性色 */
  --text-primary: #333;
  --text-secondary: #666;
  --border-color: #e1e5e9;
  --bg-white: #ffffff;
  
  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* 圆角和阴影 */
  --border-radius-lg: 1rem;
  --shadow-md: 0 4px 25px rgba(0, 0, 0, 0.05);
  
  /* 字体 */
  --font-size-xs: 0.75rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 过渡动画 */
  --transition-normal: 0.2s ease;
  
  /* 功能模块特定颜色 */
  --autodock-color: #2e7d32;
  --autodock-bg: #e8f5e8;
  --diffdock-color: #f57c00;
  --diffdock-bg: #fff3e0;
  --protenix-color: #c2185b;
  --protenix-bg: #fce4ec;
  --quantum-color: #7b1fa2;
  --quantum-bg: #f3e5f5;
  --molmap-color: #2e7d32;
  --molmap-bg: #e8f5e8;
  --pocketvina-color: #f9a825;
  --pocketvina-bg: #fff9c4;
  --raman-color: #0277bd;
  --raman-bg: #e1f5fe;
}

.home-container {
  /* 移除不必要的样式，让Layout组件处理整体布局 */
}

.welcome-section {
  background: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-xl);
}

.welcome-title {
  font-size: var(--font-size-3xl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.welcome-text {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  white-space: pre-line; /* 保持换行格式 */
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.feature-card {
  background: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.feature-badge {
  display: inline-block;
  padding: var(--spacing-xs) 0.75rem;
  background: var(--info-bg);
  color: var(--info-color);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.autodock .feature-badge {
  background: var(--autodock-bg);
  color: var(--autodock-color);
}

.diffdock .feature-badge {
  background: var(--diffdock-bg);
  color: var(--diffdock-color);
}

.protenix .feature-badge {
  background: var(--protenix-bg);
  color: var(--protenix-color);
}

.quantum .feature-badge {
  background: var(--quantum-bg);
  color: var(--quantum-color);
}

.molmap .feature-badge {
  background: var(--molmap-bg);
  color: var(--molmap-color);
}

.pocketvina .feature-badge {
  background: var(--pocketvina-bg);
  color: var(--pocketvina-color);
}

.raman .feature-badge {
  background: var(--raman-bg);
  color: var(--raman-color);
}

@media (max-width: 768px) {
  .feature-cards {
    grid-template-columns: 1fr;
  }
  
  .welcome-section {
    padding: var(--spacing-lg);
  }
  
  .welcome-title {
    font-size: var(--font-size-2xl);
  }
  
  .feature-card {
    padding: var(--spacing-lg);
  }
}