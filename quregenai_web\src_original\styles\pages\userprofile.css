.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* 个人中心特有样式 */
.profile-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.profile-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.profile-subtitle {
    color: #666;
    font-size: 1rem;
}

/* 用户信息卡片样式 */
.user-profile-card {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 2rem;
}

.large-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    font-weight: 600;
}

.user-profile-info {
    flex: 1;
}

.user-profile-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.user-profile-email {
    color: #666;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.qau-balance-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1.5rem;
}

.qau-balance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    display: inline-block;
    flex: 0 0 auto;
    min-width: fit-content;
}

.qau-description {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    margin-top: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.qau-info-left {
    margin-bottom: 1.5rem;
}

.qau-billing-rules {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.billing-rules-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.25rem;
    margin: 0;
}

.billing-rules-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.billing-rules-title::before {
    content: "💰";
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.billing-rules-content {
    padding: 1.25rem;
}

.billing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.billing-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.billing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--card-color, #667eea);
}

.billing-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.billing-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.billing-card-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 1rem;
    background: var(--card-color, #667eea);
    color: white;
}

.billing-card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.billing-card-content {
    font-size: 0.85rem;
    color: #555;
    line-height: 1.5;
    flex: 1;
}

.billing-card-content ul {
    margin: 0;
    padding-left: 1rem;
    list-style: none;
}

.billing-card-content li {
    margin-bottom: 0.4rem;
    position: relative;
}

.billing-card-content li::before {
    content: "•";
    color: var(--card-color, #667eea);
    font-weight: bold;
    position: absolute;
    left: -0.8rem;
}

.billing-card-content strong {
    color: var(--card-color, #667eea);
    font-weight: 600;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.15rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9rem;
}

.config-card {
    --card-color: #10b981;
}

.pricing-card {
    --card-color: #f59e0b;
}

.rules-card {
    --card-color: #8b5cf6;
}

.price-highlight {
    background: linear-gradient(135deg, #f59e0b15 0%, #f59e0b08 100%);
    border: 1px solid #f59e0b30;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    margin: 0.5rem 0;
    display: inline-block;
}

.price-highlight strong {
    background: none;
    color: #f59e0b;
    font-size: 1rem;
    font-weight: 700;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .qau-description {
        flex-direction: column;
        gap: 1rem;
    }

    .qau-billing-rules {
        margin-top: 0;
    }

    .billing-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .billing-card {
        padding: 0.75rem;
    }

    .billing-card-icon {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.9rem;
    }

    .billing-card-title {
        font-size: 0.85rem;
    }

    .billing-card-content {
        font-size: 0.8rem;
    }
}

/* 中等屏幕适配 */
@media (max-width: 1024px) and (min-width: 769px) {
    .billing-grid {
        gap: 0.75rem;
    }

    .billing-card {
        padding: 0.875rem;
    }
}

.qau-info-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.qau-info-title::before {
    content: "💡";
    margin-right: 0.5rem;
    font-size: 1rem;
}

.qau-info-text {
    font-size: 0.85rem;
    color: #718096;
    line-height: 1.5;
}

.qau-info-text strong {
    color: #667eea;
    font-weight: 600;
}

.recharge-btn {
    background: linear-gradient(135deg, #00c48d 0%, #00a173 100%);
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.recharge-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* API Key管理样式 */
.section-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e1e5e9;
}

.api-key-section {
    margin-bottom: 2rem;
}

.api-key-container {
    background: #f8fafc;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.api-key-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.api-key-name {
    font-weight: 600;
    color: #333;
}

.api-key-value {
    font-family: monospace;
    background: #f1f5f9;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    border: 1px dashed #cbd5e1;
    color: #555;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0.5rem 0;
}

.api-key-value span {
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.copy-btn {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    color: #764ba2;
}

.generate-key-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.generate-key-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* 充值记录样式 */
.recharge-history {
    margin-bottom: 2rem;
}

.recharge-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.recharge-table th {
    text-align: left;
    padding: 0.75rem 1rem;
    background: #f1f5f9;
    border-bottom: 1px solid #e1e5e9;
    color: #475569;
    font-weight: 600;
}

.recharge-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e1e5e9;
    color: #333;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
    gap: 0.5rem;
}

.page-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e1e5e9;
    background: white;
    color: #666;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.page-btn:hover {
    background: #f1f5f9;
    border-color: #667eea;
    color: #667eea;
}

.page-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

/* 任务列表样式 */
.tasks-section {
    margin-bottom: 2rem;
}

.recent-tasks-table-container {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.recent-tasks-table {
    width: 100%;
    border-collapse: collapse;
}

.recent-tasks-table th {
    text-align: left;
    padding: 1rem;
    background: #f8fafc;
    border-bottom: 1px solid #e1e5e9;
    color: #475569;
    font-weight: 600;
    font-size: 0.9rem;
}

.recent-tasks-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    color: #333;
    font-size: 0.9rem;
}

.recent-tasks-table tbody tr:hover {
    background: #f8fafc;
}

.recent-tasks-table tbody tr:last-child td {
    border-bottom: none;
}

.task-id-cell {
    font-family: monospace;
    background: #f1f5f9;
    padding: 0.35rem 0.7rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    color: #555;
    letter-spacing: 0.5px;
    display: inline-block;
    min-width: 6rem;
    text-align: center;
}

.task-type-cell {
    font-weight: 500;
    color: #667eea;
}

.task-status-cell {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
}

.status-completed {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-pending {
    background: #fff3e0;
    color: #f57c00;
}

.status-running {
    background: #e3f2fd;
    color: #1976d2;
}

.status-failed {
    background: #ffebee;
    color: #c62828;
}

.qau-cost-cell {
    font-weight: 500;
    color: #333;
}

.qau-cost-pending {
    color: #666;
    font-style: italic;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

/* Toast通知样式 */
.toast-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 3000;
}

.toast {
    min-width: 250px;
    max-width: 350px;
    margin-top: 10px;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: toast-in-right 0.7s;
    transition: transform 0.3s ease;
}

.toast-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.toast-success {
    background: linear-gradient(135deg, #00c48d 0%, #00a173 100%);
    color: white;
}

.toast-error {
    background: linear-gradient(135deg, #f5365c 0%, #f56036 100%);
    color: white;
}

.toast-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    color: white;
}

.toast-message {
    flex: 1;
    padding-right: 15px;
}

.toast-close {
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.toast-close:hover {
    opacity: 1;
}

@keyframes toast-in-right {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-out {
    animation: toast-out-right 0.7s forwards;
}

@keyframes toast-out-right {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.modal-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    width: 450px;
    max-width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e1e5e9;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
}

.modal-body {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 0.375rem;
    font-size: 0.9rem;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.warning-text {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 1rem;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.modal-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
}

.btn-cancel {
    background: #f1f5f9;
    color: #475569;
}

.btn-confirm {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-cancel:hover {
    background: #e2e8f0;
}

.btn-confirm:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.payment-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.payment-option:hover {
    border-color: #667eea;
    background: #f8fafc;
}

.payment-amount {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.payment-qau {
    color: #667eea;
    font-weight: 500;
}

.payment-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
}

.payment-discount {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.original-price {
    font-size: 0.75rem;
    color: #dc3545;
    text-decoration: line-through;
}

.discount-badge {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    box-shadow: 0 1px 3px rgba(220, 53, 69, 0.3);
}

@media (max-width: 768px) {

    .content {
        padding: 1rem;
    }

    .language-switcher {
        margin-right: 0.5rem;
    }

    .language-btn {
        padding: 0.2rem 0.6rem;
        font-size: 0.7rem;
    }

    .user-profile-card {
        flex-direction: column;
        align-items: center;
    }

    .qau-balance-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .qau-balance {
        text-align: center;
    }

    .recharge-table {
        font-size: 0.85rem;
    }
}

/* 将原有的活动充值样式替换为以下更美观的版本 */
/* 限制活动容器的最大宽度 */
.activity-recharge-container {
background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
border: 2px solid transparent;
background-clip: padding-box;
border-radius: 1rem;
padding: 1.5rem;
margin-top: 1.5rem;
position: relative;
overflow: hidden;
max-width: 600px; /* 添加最大宽度限制 */
}

.activity-recharge-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1rem;
    padding: 2px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: subtract;
    mask-composite: subtract;
    z-index: -1;
}

.activity-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.activity-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
}

.activity-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.activity-subtitle {
    font-size: 0.85rem;
    color: #667eea;
    margin: 0;
    font-weight: 500;
}

.activity-input-group {
    display: flex;
    gap: 0.75rem;
    align-items: stretch;
    position: relative;
}

.activity-input-wrapper {
    flex: 1;
    position: relative;
}

.activity-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e1e5e9;
    border-radius: 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    font-family: inherit;
}

.activity-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.activity-input::placeholder {
    color: #a0aec0;
    font-style: italic;
}

.activity-btn {
    background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 120px;
    box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.activity-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.activity-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 149, 0, 0.4);
    background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
}

.activity-btn:hover::before {
    left: 100%;
}

.activity-btn:active {
    transform: translateY(-1px);
}

.activity-btn:disabled {
    background: linear-gradient(135deg, #cbd5e0 0%, #a0aec0 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.activity-btn:disabled::before {
    display: none;
}

.activity-hint {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0.75rem 1rem;
    background: rgba(102, 126, 234, 0.08);
    border-radius: 0.5rem;
    font-size: 0.85rem;
    color: #4a5568;
    border-left: 4px solid #667eea;
}

.activity-hint::before {
    content: '💡';
    font-size: 1rem;
}

.activity-success-state {
    background: linear-gradient(135deg, #48bb7815 0%, #38a16915 100%);
    border: 2px solid #48bb78;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    color: #2d7d32;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    animation: successPulse 2s ease-in-out;
}

.activity-success-state::before {
    content: '🎉';
    font-size: 1.5rem;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .activity-recharge-container {
        padding: 1.25rem;
        margin-top: 1rem;
    }
    
    .activity-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .activity-icon {
        width: 2rem;
        height: 2rem;
        font-size: 1rem;
    }
    
    .activity-input-group {
        flex-direction: column;
        gap: 1rem;
    }
    
    .activity-input {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }
    
    .activity-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
        width: 100%;
    }
    
    .activity-hint {
        padding: 0.625rem 0.875rem;
        font-size: 0.8rem;
    }
}