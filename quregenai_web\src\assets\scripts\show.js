// show.js - 分子结构查看器核心功能
console.log("show.js: Script start");

// 全局变量
let viewer = null;
let uploadedFilesData = [];
let fileIdCounter = 0;
let addedSpheres = [];
let addedLabels = [];
let clickedAtoms = [];
let originalData = '';
let selectedAtomsData = []; // 新增：存储选中原子的详细信息
let atomInfoList = null; // 新增：原子信息列表容器
let clearAtomsBtn = null; // 新增：清除按钮
let modelToFileMap = new Map(); // 新增：模型ID到文件信息的映射
let residuesWithLinesStyle = new Map(); // 新增：跟踪被设置为lines样式的残基

// DOM 元素引用 - 将在初始化时设置
let fileInput = null;
let fileListContainer = null;
let viewerContainer = null;

// ==================== 查看器初始化和管理 ====================

/**
 * 初始化3D查看器
 */
function initViewer() {
    console.log("show.js: initViewer called.");
    
    if (!viewerContainer) {
        console.error('show.js: CRITICAL - viewerContainer element not found.');
        return null;
    }
    
    console.log('show.js: viewerContainer found:', viewerContainer);
    console.log('show.js: viewerContainer dimensions:', viewerContainer.offsetWidth, 'x', viewerContainer.offsetHeight);

    try {
        // 清除现有查看器
        if (viewer) {
            console.log("show.js: Clearing existing viewer");
            viewer.removeAllModels();
            viewer = null;
        }

        // 清空容器内容，确保没有残留元素
        viewerContainer.innerHTML = '';
        console.log("show.js: Cleared viewerContainer content");

        // 检查3Dmol是否可用
        if (typeof $3Dmol === 'undefined') {
            throw new Error("3Dmol.js library not available");
        }

        console.log("show.js: Creating 3Dmol viewer...");
        
        // 创建新查看器，使用原生DOM元素而不是jQuery选择器
        viewer = $3Dmol.createViewer(viewerContainer, {
            defaultcolors: $3Dmol.rasmolElementColors,
            backgroundColor: 'black'
        });
        
        if (!viewer) {
            throw new Error("Failed to create viewer object");
        }

        console.log("show.js: 3Dmol viewer created successfully:", viewer);

        viewer.setBackgroundColor(0x000000);
        
        // 使用回调确保渲染完成
        viewer.render(function() {
            console.log("show.js: Initial viewer render completed");
        });
        
        console.log("show.js: Viewer initialized successfully.");
        return viewer;
    } catch (e) {
        console.error("show.js: Error creating 3Dmol viewer:", e);
        if (viewerContainer) {
            viewerContainer.innerHTML = `<div style="color: red; padding: 20px; text-align: center;">Failed to initialize 3D viewer: ${e.message}</div>`;
        }
        return null;
    }
}

/**
 * 清理当前模型和所有标记
 */
function cleanupCurrentModel() {
    console.log("show.js: Cleaning up current model and markers");
    
    try {
        // 清除所有标记和球体
        if (addedSpheres && addedSpheres.length > 0) {
            addedSpheres.forEach(sphere => viewer.removeShape(sphere));
            addedSpheres = [];
        }
        
        if (addedLabels && addedLabels.length > 0) {
            addedLabels.forEach(label => viewer.removeLabel(label));
            addedLabels = [];
        }
        
        // 清除点击记录和原子信息
        clickedAtoms = [];
        selectedAtomsData = [];
        
        // 清除模型到文件的映射关系
        modelToFileMap.clear();
        
        // 清除残基样式跟踪
        residuesWithLinesStyle.clear();
        
        // 更新原子信息列表显示
        updateAtomInfoList();
        
        // 清除所有模型
        viewer.clear();
        
        // 重新渲染视图
        viewer.render();
        console.log("show.js: Model cleanup completed");
        
    } catch (error) {
        console.error("show.js: Error during model cleanup:", error);
        throw error;
    }
}

// ==================== 文件验证功能 ====================

/**
 * 验证PDB文件格式
 */
function validatePDBData(data) {
    const lines = data.trim().split('\n');
    console.log("验证PDB数据: 总行数:", lines.length);

    if (lines.length < 2) {
        console.error("PDB数据行数不足");
        return false;
    }

    // 计算原子数量
    const atomLines = lines.filter(line => 
        line.startsWith('ATOM') || line.startsWith('HETATM')
    );
    const atomCount = atomLines.length;
    const standardAtoms = atomLines.filter(line => line.startsWith('ATOM')).length;
    const heteroAtoms = atomLines.filter(line => line.startsWith('HETATM')).length;
    
    console.log("找到的原子行:", atomCount);
    console.log("标准原子(ATOM)数量:", standardAtoms);
    console.log("杂原子(HETATM)数量:", heteroAtoms);
    
    if (atomCount > 0) {
        console.log("第一个原子行示例:", atomLines[0]);
    }

    return atomCount > 0;
}

/**
 * 验证SDF文件格式
 */
function validateSDFData(data) {
    const lines = data.trim().split('\n');
    console.log("验证SDF数据: 总行数:", lines.length);

    if (lines.length < 4) {
        console.error("SDF数据行数不足");
        return false;
    }

    // 检查原子和键的数量行
    const countsLine = lines[3].trim();
    if (countsLine.length < 6) {
        console.error("无效的原子/键计数行");
        return false;
    }

    const atomCount = parseInt(countsLine.substring(0, 3));
    console.log("SDF声明的原子数:", atomCount);

    return atomCount > 0;
}

/**
 * 验证PDBQT文件格式
 */
function validatePDBQTData(data) {
    const lines = data.trim().split('\n');
    console.log("验证PDBQT数据: 总行数:", lines.length);

    if (lines.length < 1) {
        console.error("PDBQT数据为空");
        return false;
    }

    // 检查是否包含PDBQT特有的关键字
    let hasAtomRecords = false;
    let hasPDBQTKeywords = false;
    
    for (const line of lines) {
        const trimmedLine = line.trim();
        
        // 检查原子记录
        if (trimmedLine.startsWith('ATOM') || trimmedLine.startsWith('HETATM')) {
            hasAtomRecords = true;
        }
        
        // 检查PDBQT特有的关键字
        if (trimmedLine.startsWith('MODEL') || 
            trimmedLine.startsWith('ENDMDL') || 
            trimmedLine.startsWith('TORSDOF') ||
            trimmedLine.startsWith('REMARK') ||
            trimmedLine.includes('VINA RESULT')) {
            hasPDBQTKeywords = true;
        }
    }

    console.log("PDBQT验证结果: hasAtomRecords:", hasAtomRecords, "hasPDBQTKeywords:", hasPDBQTKeywords);
    
    // PDBQT文件应该至少包含原子记录，最好还有PDBQT特有的关键字
    return hasAtomRecords;
}

// ==================== 文件加载和处理 ====================

/**
 * 处理文件上传
 */
function handleFileUpload(files) {
    console.log("show.js: handleFileUpload called with files:", files);
    console.log("show.js: files type:", typeof files);
    console.log("show.js: files length:", files ? files.length : 'undefined');
    console.log("show.js: files constructor:", files ? files.constructor.name : 'undefined');
    
    if (!files || files.length === 0) {
        console.log("show.js: No files selected");
        return;
    }

    // 清除测试分子和现有模型
    console.log("show.js: Clearing test molecule and existing models");
    cleanupCurrentModel();
    
    // 不再清空现有文件数据，而是保留之前上传的文件
    // uploadedFilesData = [];  // 注释掉这行
    // fileIdCounter = 0;       // 注释掉这行
    
    console.log("show.js: Current uploadedFilesData before adding new files:", uploadedFilesData.length);

    console.log("show.js: Processing", files.length, "files");
    let filesProcessed = 0;
    
    // 添加更多调试信息
    console.log("show.js: Converting files to array...");
    const filesArray = Array.from(files);
    console.log("show.js: Files array:", filesArray);
    console.log("show.js: Files array length:", filesArray.length);
    
    filesArray.forEach((file, index) => {
        console.log(`show.js: Processing file ${index + 1}:`, file.name, file.size, "bytes");
        
        const reader = new FileReader();
        
        reader.onload = (e) => {
            try {
                console.log(`show.js: File ${file.name} read successfully, content length:`, e.target.result.length);
                
                const content = e.target.result;
                const fileName = file.name.toLowerCase();
                let type = fileName.endsWith('.pdb') ? 'pdb' : 
                          fileName.endsWith('.sdf') ? 'sdf' : 
                          fileName.endsWith('.pdbqt') ? 'pdbqt' : null;

                console.log(`show.js: File type detected: ${type} for file: ${file.name}`);

                if (type) {
                    // 验证文件格式
                    let isValid = false;
                    if (type === 'pdb') {
                        isValid = validatePDBData(String(content));
                    } else if (type === 'sdf') {
                        isValid = validateSDFData(String(content));
                    } else if (type === 'pdbqt') {
                        isValid = validatePDBQTData(String(content));
                    }
                    
                    console.log(`show.js: File validation result for ${file.name}: ${isValid}`);
                    
                    if (!isValid) {
                        throw new Error(`无效的${type.toUpperCase()}文件格式`);
                    }

                    const newFile = {
                        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,  // 使用时间戳和随机字符串生成唯一ID
                        name: file.name,
                        content: content,
                        type: type,
                        size: file.size, // 添加文件大小属性
                        selected: true
                    };
                    
                    uploadedFilesData.push(newFile);
                    console.log(`show.js: Added file to uploadedFilesData: ${file.name}, total files:`, uploadedFilesData.length);
                    
                    // 只在第一次上传文件时保存原始数据
                    if (!originalData && uploadedFilesData.length === 1) {
                        originalData = content;
                        console.log("show.js: Set as original data");
                    }
                } else {
                    console.error(`show.js: Unsupported file type for: ${file.name}`);
                }

                filesProcessed++;
                console.log(`show.js: Files processed: ${filesProcessed}/${filesArray.length}`);
                
                if (filesProcessed === filesArray.length) {
                    console.log("show.js: All files processed, updating display");
                    console.log("show.js: About to call renderFileList with uploadedFilesData:", uploadedFilesData);
                    console.log("show.js: fileListContainer before renderFileList:", fileListContainer);
                    renderFileList();
                    displaySelectedFiles();
                }
            } catch (error) {
                console.error(`处理文件 ${file.name} 时出错:`, error);
                filesProcessed++;
                if (filesProcessed === filesArray.length) {
                    console.log("show.js: All files processed (with read errors), updating display");
                    console.log("show.js: About to call renderFileList with uploadedFilesData:", uploadedFilesData);
                    console.log("show.js: fileListContainer before renderFileList:", fileListContainer);
                    renderFileList();
                    displaySelectedFiles();
                }
            }
        };

        reader.onerror = (error) => {
            console.error(`show.js: Error reading file ${file.name}:`, error);
            filesProcessed++;
            if (filesProcessed === filesArray.length) {
                console.log("show.js: All files processed (with read errors), updating display");
                console.log("show.js: About to call renderFileList with uploadedFilesData:", uploadedFilesData);
                console.log("show.js: fileListContainer before renderFileList:", fileListContainer);
                renderFileList();
                displaySelectedFiles();
            }
        };

        reader.readAsText(file);
    });
}

// ==================== 文件列表渲染 ====================

/**
 * 渲染文件列表
 */
function renderFileList() {
    console.log("show.js: renderFileList called with files:", uploadedFilesData);
    console.log("show.js: uploadedFilesData length:", uploadedFilesData.length);
    console.log("show.js: fileListContainer element:", fileListContainer);
    console.log("show.js: fileListContainer exists:", !!fileListContainer);
    
    // 尝试重新获取元素，以防初始化时没有找到
    if (!fileListContainer) {
        console.log("show.js: fileListContainer not found, trying to re-acquire...");
        fileListContainer = document.getElementById('file-list-container');
        console.log("show.js: Re-acquired fileListContainer:", fileListContainer);
    }
    
    if (!fileListContainer) {
        console.error('show.js: CRITICAL - fileListContainer element not found.');
        console.error('show.js: Available elements with similar IDs:');
        // 查找可能的相似元素
        const allElements = document.querySelectorAll('[id*="file"], [id*="list"], [id*="container"]');
        allElements.forEach(el => {
            console.error(`show.js: Found element: id="${el.id}", class="${el.className}"`);
        });
        return;
    }

    // // 清除之前的内容
    // fileListContainer.innerHTML = '';
    // console.log("show.js: Cleared fileListContainer content");
    //
    // // 添加标题
    // const titleElement = document.createElement('h3');
    // titleElement.textContent = '已上传文件';
    // titleElement.style.marginBottom = '10px';
    // fileListContainer.appendChild(titleElement);
    // console.log("show.js: Added title element");
    //
    // if (uploadedFilesData.length === 0) {
    //     const p = document.createElement('p');
    //     p.textContent = '暂无文件';
    //     p.style.padding = '10px';
    //     p.style.color = '#666';
    //     fileListContainer.appendChild(p);
    //     console.log("show.js: Added 'no files' message");
    //     return;
    // }
    //
    // console.log("show.js: Creating file list with", uploadedFilesData.length, "files");
    //
    // // 创建文件列表
    // const ul = document.createElement('ul');
    // ul.style.listStyle = 'none';
    // ul.style.padding = '0';
    // ul.style.margin = '0';
    //
    // uploadedFilesData.forEach((fileData, index) => {
    //     console.log(`show.js: Creating list item for file ${index + 1}:`, fileData.name);
    //
    //     const li = document.createElement('li');
    //     li.style.padding = '8px';
    //     li.style.margin = '4px 0';
    //     li.style.backgroundColor = '#f5f5f5';
    //     li.style.borderRadius = '4px';
    //     li.style.border = '1px solid #ddd';
    //
    //     const checkbox = document.createElement('input');
    //     checkbox.type = 'checkbox';
    //     checkbox.id = fileData.id;
    //     checkbox.checked = fileData.selected;
    //     checkbox.style.marginRight = '8px';
    //
    //     checkbox.addEventListener('change', () => {
    //         console.log(`show.js: Checkbox changed for ${fileData.name}, selected:`, checkbox.checked);
    //         fileData.selected = checkbox.checked;
    //         displaySelectedFiles();
    //     });
    //
    //     const label = document.createElement('label');
    //     label.htmlFor = fileData.id;
    //     label.textContent = `${fileData.name} (${fileData.type.toUpperCase()})`;
    //     label.style.cursor = 'pointer';
    //
    //     li.appendChild(checkbox);
    //     li.appendChild(label);
    //     ul.appendChild(li);
    // });
    //
    // fileListContainer.appendChild(ul);
    // console.log("show.js: File list rendered with", uploadedFilesData.length, "items");
}

// ==================== 分子显示和样式设置 ====================

/**
 * 显示选中的文件
 */
function displaySelectedFiles() {
    console.log("show.js: displaySelectedFiles called");
    console.log("show.js: viewer object:", viewer);
    console.log("show.js: uploadedFilesData:", uploadedFilesData);
    
    if (!viewer) {
        console.error("show.js: Viewer not initialized");
        return;
    }

    try {
        // 清除现有模型
        cleanupCurrentModel();
        
        // 获取选中的文件
        const selectedFiles = uploadedFilesData.filter(file => file.selected);
        console.log("show.js: Selected files to display:", selectedFiles.length);
        console.log("show.js: Selected files details:", selectedFiles.map(f => ({name: f.name, type: f.type})));

        if (selectedFiles.length === 0) {
            console.log("show.js: No files selected, rendering empty viewer");
            viewer.render();
            return;
        }

        selectedFiles.forEach((fileData, index) => {
            console.log(`show.js: Processing file ${index + 1}: ${fileData.name}`);
            
            // 对于PDBQT文件，使用PDB格式解析，因为它们格式相似
            const parseFormat = fileData.type === 'pdbqt' ? 'pdb' : fileData.type;
            
            const model = viewer.addModel(fileData.content, parseFormat, {
                keepH: true,
                assignBonds: fileData.type === 'pdb' || fileData.type === 'pdbqt',
                bonds: true
            });
            
            console.log(`show.js: Model added for ${fileData.name} (parsed as ${parseFormat}):`, model);
            
            if (model) {
                // 记录模型ID和文件的映射关系
                const modelId = model.getID();
                modelToFileMap.set(modelId, {
                    name: fileData.name,
                    type: fileData.type,
                    id: fileData.id
                });
                console.log(`show.js: Recorded model ${modelId} -> file ${fileData.name}`);
                
                setMoleculeStyle(fileData.type, model);
                console.log(`show.js: Style set for ${fileData.name}`);
            } else {
                console.error(`show.js: Failed to add model for ${fileData.name}`);
            }
        });

        // 更新视图
        console.log("show.js: Updating viewer - zoomTo, setClickable, render");
        viewer.zoomTo();
        setClickable();
        viewer.render();
        console.log("show.js: Display completed");
    } catch (error) {
        console.error("show.js: Error displaying files:", error);
    }
}

/**
 * 根据分子类型设置样式
 */
function setMoleculeStyle(type, model) {
    if (type === 'pdb') {
        // 对于PDB文件，需要判断是蛋白质还是小分子
        const atomCount = model.selectedAtoms({}).length;
        console.log(`PDB文件原子数量: ${atomCount}`);
        
        // 如果原子数量较少（小于100个原子），认为是小分子，使用球棍模型
        if (atomCount < 100) {
            console.log("检测到小分子PDB，使用球棍模型");
            viewer.setStyle({model: model}, {
                stick: {
                    radius: 0.15,
                    opacity: 0.9,
                    colorscheme: 'Jmol'
                },
                sphere: {
                    radius: 0.35,
                    colorscheme: 'Jmol'
                }
            });
        } else {
            console.log("检测到蛋白质PDB，使用卡通模型");
            // 蛋白质样式
            viewer.setStyle({model: model}, {
                cartoon: {
                    color: 'spectrum'
                }
            });
        }
    } else if (type === 'sdf') {
        // 小分子样式
        viewer.setStyle({model: model}, {
            stick: {
                radius: 0.15,
                opacity: 0.9,
                colorscheme: 'Jmol'
            },
            sphere: {
                radius: 0.35,
                colorscheme: 'Jmol'
            }
        });
    } else if (type === 'pdbqt') {
        // PDBQT文件样式 - 通常是对接结果，使用卡通模型
        console.log("检测到PDBQT文件，使用卡通模型");
        viewer.setStyle({model: model}, {
            cartoon: {
                color: 'spectrum'
            }
        });
    }
}

/**
 * 设置残基为lines样式
 */
function setResidueToLinesStyle(atom) {
    if (!atom.resn || !atom.resi || !atom.chain) {
        console.log("原子缺少残基信息，跳过样式设置");
        return;
    }
    
    const residueKey = `${atom.model}_${atom.chain}_${atom.resi}_${atom.resn}`;
    console.log(`设置残基 ${atom.resn}${atom.resi} (链 ${atom.chain}) 为lines样式`);
    
    // 记录这个残基被设置为lines样式
    residuesWithLinesStyle.set(residueKey, {
        model: atom.model,
        chain: atom.chain,
        resi: atom.resi,
        resn: atom.resn
    });
    
    // 设置该残基为lines样式
    const residueSelection = {
        model: atom.model,
        chain: atom.chain,
        resi: atom.resi
    };
    
    try {
        viewer.setStyle(residueSelection, {
            stick: {
                colorscheme: 'Jmol',
                radius: 0.2,
                opacity: 0.9
            }
        });
        
        console.log(`残基 ${residueKey} 已设置为stick样式`);
        viewer.render(); // 确保渲染更新
    } catch (error) {
        console.error(`设置残基 ${residueKey} 样式时出错:`, error);
    }
}

/**
 * 恢复残基为卡通样式
 */
function restoreResidueToCartoonStyle(atom) {
    if (!atom.resn || !atom.resi || !atom.chain) {
        console.log("原子缺少残基信息，跳过样式恢复");
        return;
    }
    
    const residueKey = `${atom.model}_${atom.chain}_${atom.resi}_${atom.resn}`;
    console.log(`恢复残基 ${atom.resn}${atom.resi} (链 ${atom.chain}) 为卡通样式`);
    
    // 检查这个残基是否还有其他被选中的原子
    const hasOtherSelectedAtoms = selectedAtomsData.some(atomData => {
        // 构建当前原子数据的残基key进行精确比较
        const otherResidueKey = `${atomData.model}_${atomData.chain}_${atomData.resi}_${atomData.residue}`;
        // 如果残基key相同但原子index不同，说明同一残基还有其他被选中的原子
        return otherResidueKey === residueKey && atomData.index !== atom.index;
    });
    
    // 如果这个残基没有其他被选中的原子，则恢复为卡通样式
    if (!hasOtherSelectedAtoms) {
        residuesWithLinesStyle.delete(residueKey);
        
        // 恢复该残基为卡通样式
        const residueSelection = {
            model: atom.model,
            chain: atom.chain,
            resi: atom.resi
        };
        
        try {
            viewer.setStyle(residueSelection, {
                cartoon: {
                    color: 'spectrum'
                }
            });
            
            console.log(`残基 ${residueKey} 已恢复为卡通样式`);
            viewer.render(); // 确保渲染更新
        } catch (error) {
            console.error(`恢复残基 ${residueKey} 样式时出错:`, error);
        }
    } else {
        console.log(`残基 ${residueKey} 仍有其他被选中的原子，保持lines样式`);
    }
}

// ==================== 交互功能 ====================

/**
 * 设置原子点击功能
 */
function setClickable() {
    if (!viewer) {
        console.error("Viewer未初始化");
        return;
    }
    
    console.log("设置点击处理程序...");
    
    // 先移除旧的点击事件
    viewer.setClickable({}, false);
    
    // 添加新的点击事件
    viewer.setClickable({}, true, function(atom, viewer, event, container) {
        if (!atom) {
            console.log("点击了空白区域，不做任何操作");
            return; // 点击空白区域时不做任何操作
        }
        
        console.log("点击事件触发，原子:", atom);
        
        const atomIndex = atom.serial;
        const existingIndex = clickedAtoms.indexOf(atom.index);
        
        if (existingIndex !== -1) {
            // 取消选择原子
            handleAtomDeselection(existingIndex, atomIndex);
        } else {
            // 选择新原子
            handleAtomSelection(atom);
        }
        
        viewer.render();
    });
    
    console.log("点击处理程序设置完成");
}

/**
 * 处理原子选择
 */
function handleAtomSelection(atom) {
    console.log("handleAtomSelection called with atom:", atom);
    
    const atomIndex = atom.serial;
    const atomName = atom.atom;
    const resName = atom.resn;
    const atomPosition = `(${atom.x.toFixed(2)}, ${atom.y.toFixed(2)}, ${atom.z.toFixed(2)})`;
    
    console.log("Atom details:", { atomIndex, atomName, resName, atomPosition });
    
    // 添加标签
    const label = viewer.addLabel(`${atomIndex}`, {
        position: { x: atom.x, y: atom.y, z: atom.z },
        backgroundColor: 'transparent',
        fontColor: 'white',
        fontSize: 12,
        showBackground: false
    });
    addedLabels.push(label);
    console.log("Label added, total labels:", addedLabels.length);
    
    // 添加高亮球体
    const sphere = viewer.addSphere({
        center: { x: atom.x, y: atom.y, z: atom.z },
        radius: 0.5,
        color: 'green',
        opacity: 0.7
    });
    addedSpheres.push(sphere);
    console.log("Sphere added, total spheres:", addedSpheres.length);
    
    clickedAtoms.push(atom.index);
    console.log("Atom index added to clickedAtoms:", atom.index, "Total clicked atoms:", clickedAtoms.length);
    
    // 获取原子所属的文件名
    const fileName = getAtomFileName(atom);
    console.log("File name for atom:", fileName);
    
    // 创建原子信息对象
    const atomInfo = {
        index: atom.index,
        serial: atomIndex,
        name: atomName || 'N/A',
        residue: resName || 'N/A',
        chain: atom.chain || 'N/A',
        fileName: fileName,
        coordinates: {
            x: atom.x,  // 保持为数字类型
            y: atom.y,  // 保持为数字类型
            z: atom.z   // 保持为数字类型
        },
        sphereIndex: addedSpheres.length - 1,
        labelIndex: addedLabels.length - 1,
        model: atom.model, // 添加模型信息
        resi: atom.resi    // 添加残基编号
    };
    
    console.log("Created atomInfo:", atomInfo);
    
    // 添加到选中原子数据数组
    selectedAtomsData.push(atomInfo);
    console.log("Added to selectedAtomsData, total selected atoms:", selectedAtomsData.length);
    
    // 检测是否为小分子：通过原子数量判断
    const modelAtomCount = viewer.getModel(atom.model).selectedAtoms({}).length;
    const isSmallMolecule = modelAtomCount < 100;
    
    console.log(`Model ${atom.model} atom count: ${modelAtomCount}, is small molecule: ${isSmallMolecule}`);
    
    // 只有在不是小分子的情况下才改变残基显示样式
    if (!isSmallMolecule && resName && resName !== 'N/A' && atom.resi && atom.chain) {
        console.log("Setting residue to lines style for protein residue:", resName, atom.resi, atom.chain);
        setResidueToLinesStyle(atom);
    } else if (isSmallMolecule) {
        console.log("Skipping residue style setting - detected small molecule");
    } else {
        console.log("Skipping residue style setting - no residue info");
    }
    
    // 更新原子信息列表显示
    console.log("Calling updateAtomInfoList...");
    updateAtomInfoList();
    
    // 更新信息显示
    updateAtomInfo(`
        点击的原子:
        编号: ${atomIndex}
        名称: ${atomName}
        残基: ${resName}
        坐标: ${atomPosition}
    `);
    
    console.log("handleAtomSelection completed");
}

/**
 * 处理原子取消选择
 */
function handleAtomDeselection(existingIndex, atomIndex) {
    // 获取要删除的原子信息，用于恢复残基样式
    const atomDataIndex = selectedAtomsData.findIndex(atomData => 
        atomData.sphereIndex === existingIndex && atomData.labelIndex === existingIndex
    );
    
    let atomToRestore = null;
    if (atomDataIndex !== -1) {
        const atomData = selectedAtomsData[atomDataIndex];
        // 重构原子对象用于样式恢复
        atomToRestore = {
            model: atomData.model,
            chain: atomData.chain,
            resi: atomData.resi,
            resn: atomData.residue,
            index: atomData.index
        };
    }
    
    viewer.removeShape(addedSpheres[existingIndex]);
    viewer.removeLabel(addedLabels[existingIndex]);
    
    addedSpheres.splice(existingIndex, 1);
    addedLabels.splice(existingIndex, 1);
    clickedAtoms.splice(existingIndex, 1);
    
    // 从选中原子数据中移除对应的原子
    if (atomDataIndex !== -1) {
        selectedAtomsData.splice(atomDataIndex, 1);
        
        // 更新剩余原子的索引
        selectedAtomsData.forEach((atomData, index) => {
            if (atomData.sphereIndex > existingIndex) {
                atomData.sphereIndex--;
            }
            if (atomData.labelIndex > existingIndex) {
                atomData.labelIndex--;
            }
        });
    }
    
    // 检测是否为小分子，只有在不是小分子的情况下才尝试恢复残基样式
    if (atomToRestore && atomToRestore.resn && atomToRestore.resn !== 'N/A' && 
        atomToRestore.resi && atomToRestore.chain) {
        
        // 检测是否为小分子：通过原子数量判断
        const modelAtomCount = viewer.getModel(atomToRestore.model).selectedAtoms({}).length;
        const isSmallMolecule = modelAtomCount < 100;
        
        console.log(`Model ${atomToRestore.model} atom count: ${modelAtomCount}, is small molecule: ${isSmallMolecule}`);
        
        if (!isSmallMolecule) {
            console.log("Restoring residue style for protein residue:", atomToRestore.resn, atomToRestore.resi, atomToRestore.chain);
            restoreResidueToCartoonStyle(atomToRestore);
        } else {
            console.log("Skipping residue style restoration - detected small molecule");
        }
    }
    
    // 更新原子信息列表显示
    updateAtomInfoList();
    
    updateAtomInfo(`已取消选择原子 ${atomIndex}`);
}

/**
 * 更新原子信息显示
 */
function updateAtomInfo(info) {
    // 可以在这里添加信息显示逻辑
    console.log(info);
}

/**
 * 获取原子所属的文件名
 */
function getAtomFileName(atom) {
    try {
        // 获取原子所属的模型ID
        const modelId = atom.model;
        console.log(`show.js: Getting file name for atom with model ID: ${modelId}`);
        console.log(`show.js: Current modelToFileMap:`, Array.from(modelToFileMap.entries()));
        
        // 从映射表中查找对应的文件信息
        if (modelToFileMap.has(modelId)) {
            const fileInfo = modelToFileMap.get(modelId);
            console.log(`show.js: Found file info for model ${modelId}:`, fileInfo);
            return fileInfo.name;
        }
        
        // 如果找不到映射关系，尝试其他方法
        console.warn(`show.js: No file mapping found for model ${modelId}`);
        
        // 备用方法：如果只有一个文件，返回该文件名
        const selectedFiles = uploadedFilesData.filter(file => file.selected);
        if (selectedFiles.length === 1) {
            console.log(`show.js: Using single selected file: ${selectedFiles[0].name}`);
            return selectedFiles[0].name;
        }
        
        // 如果有多个文件但找不到映射，返回带模型ID的标识
        if (selectedFiles.length > 1) {
            return `模型${modelId}`;
        }
        
        return '未知文件';
    } catch (error) {
        console.error('show.js: Error getting atom file name:', error);
        return '未知文件';
    }
}

/**
 * 更新原子信息列表显示
 */
function updateAtomInfoList() {
    console.log("updateAtomInfoList called");
    console.log("atomInfoList element:", atomInfoList);
    console.log("selectedAtomsData length:", selectedAtomsData.length);
    console.log("selectedAtomsData:", selectedAtomsData);
    
    if (!atomInfoList) {
        console.warn('原子信息列表容器未找到');
        return;
    }
    
    // 清空现有内容
    atomInfoList.innerHTML = '';
    console.log("Cleared atomInfoList content");
    
    if (selectedAtomsData.length === 0) {
        // 显示无原子消息
        const noAtomsMsg = document.createElement('p');
        noAtomsMsg.className = 'no-atoms-message';
        noAtomsMsg.textContent = '暂无选择的原子';
        atomInfoList.appendChild(noAtomsMsg);
        console.log("Added no atoms message");
        
        // 禁用清除按钮
        if (clearAtomsBtn) {
            clearAtomsBtn.disabled = true;
        }
        return;
    }
    
    // 启用清除按钮
    if (clearAtomsBtn) {
        clearAtomsBtn.disabled = false;
        console.log("Enabled clear atoms button");
    }
    
    // 如果有多个原子，添加口袋中心选择控制
    if (selectedAtomsData.length > 1) {
        // 创建口袋中心选择容器
        const pocketCenterContainer = document.createElement('div');
        pocketCenterContainer.className = 'pocket-center-container';
        pocketCenterContainer.style.cssText = `
            width: 100%;
            margin-bottom: 0.75rem;
            padding: 0.75rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        `;
        
        // 添加标题
        const title = document.createElement('div');
        title.className = 'pocket-center-title';
        title.textContent = '🎯 对接口袋中心选择模式';
        title.style.cssText = `
            font-size: 0.875rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            text-align: center;
        `;
        pocketCenterContainer.appendChild(title);
        
        // 创建按钮组
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'pocket-center-buttons';
        buttonGroup.style.cssText = `
            display: flex;
            gap: 0.5rem;
            width: 100%;
        `;
        
        // 单个原子模式按钮（默认激活）
        const singleAtomBtn = document.createElement('button');
        singleAtomBtn.className = 'pocket-mode-btn active';
        singleAtomBtn.innerHTML = `
            <div class="btn-icon" style="font-size: 0.9rem;">👆</div>
            <div class="btn-text" style="font-weight: 600; font-size: 0.65rem;">单个原子</div>
            <div class="btn-desc" style="font-size: 0.6rem; opacity: 0.8;">点击原子设置</div>
        `;
        singleAtomBtn.dataset.mode = 'single';
        
        // 几何中心模式按钮
        const centerBtn = document.createElement('button');
        centerBtn.className = 'pocket-mode-btn';
        centerBtn.innerHTML = `
            <div class="btn-icon" style="font-size: 0.9rem;">📍</div>
            <div class="btn-text" style="font-weight: 600; font-size: 0.65rem;">几何中心</div>
            <div class="btn-desc" style="font-size: 0.6rem; opacity: 0.8;">自动计算</div>
        `;
        centerBtn.dataset.mode = 'center';
        
        // 按钮样式
        const buttonStyle = `
            flex: 1;
            padding: 0.375rem 0.25rem;
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.7rem;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.125rem;
            min-height: 60px;
        `;
        
        const activeButtonStyle = `
            border-color: #007bff;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
            transform: translateY(-1px);
        `;
        
        singleAtomBtn.style.cssText = buttonStyle;
        centerBtn.style.cssText = buttonStyle;
        
        // 默认激活单个原子模式
        singleAtomBtn.style.cssText += activeButtonStyle;
        
        // 添加状态变量
        window.pocketCenterMode = 'single';
        
        // 按钮点击事件
        singleAtomBtn.addEventListener('click', function() {
            if (window.pocketCenterMode === 'single') return;
            
            window.pocketCenterMode = 'single';
            
            // 更新按钮样式
            singleAtomBtn.style.cssText = buttonStyle + activeButtonStyle;
            centerBtn.style.cssText = buttonStyle;
            
            // 清除几何中心模式下的坐标设置
            updateModeDescription('single');
            
            console.log('切换到单个原子模式');
        });
        
        centerBtn.addEventListener('click', function() {
            if (window.pocketCenterMode === 'center') return;
            
            window.pocketCenterMode = 'center';
            
            // 更新按钮样式
            centerBtn.style.cssText = buttonStyle + activeButtonStyle;
            singleAtomBtn.style.cssText = buttonStyle;
            
            // 立即计算并设置几何中心
            calculateAndSetGeometricCenter();
            updateModeDescription('center');
            
            console.log('切换到几何中心模式');
        });
        
        // 添加模式说明
        const modeDescription = document.createElement('div');
        modeDescription.className = 'mode-description';
        modeDescription.style.cssText = `
            font-size: 0.75rem;
            color: #6c757d;
            text-align: center;
            margin-top: 0.5rem;
            line-height: 1.3;
        `;
        
        function updateModeDescription(mode) {
            if (mode === 'single') {
                modeDescription.innerHTML = '💡 <strong>单个原子模式</strong>：点击下方任意原子将其坐标设为口袋中心';
            } else {
                modeDescription.innerHTML = '💡 <strong>几何中心模式</strong>：使用所有选中原子的几何中心作为口袋中心';
            }
        }
        
        updateModeDescription('single');
        
        buttonGroup.appendChild(singleAtomBtn);
        buttonGroup.appendChild(centerBtn);
        pocketCenterContainer.appendChild(buttonGroup);
        pocketCenterContainer.appendChild(modeDescription);
        
        atomInfoList.appendChild(pocketCenterContainer);
        
    } else {
        window.pocketCenterMode = 'single';
    }
    
    // 为每个选中的原子创建信息项
    selectedAtomsData.forEach((atomData, index) => {
        console.log(`Creating atom item for index ${index}:`, atomData);
        const atomItem = createAtomInfoItem(atomData, index);
        atomInfoList.appendChild(atomItem);
        console.log(`Added atom item ${index} to list`);
    });
    
    console.log("updateAtomInfoList completed");
}

/**
 * 创建原子信息项
 */
function createAtomInfoItem(atomData, index) {
    const atomItem = document.createElement('div');
    atomItem.className = 'atom-item';
    atomItem.dataset.atomIndex = index;
    atomItem.style.position = 'relative'; // 确保相对定位
    
    // 安全地处理坐标显示，确保是数字格式
    const x = typeof atomData.coordinates.x === 'string' ? parseFloat(atomData.coordinates.x) : atomData.coordinates.x;
    const y = typeof atomData.coordinates.y === 'string' ? parseFloat(atomData.coordinates.y) : atomData.coordinates.y;
    const z = typeof atomData.coordinates.z === 'string' ? parseFloat(atomData.coordinates.z) : atomData.coordinates.z;
    
    atomItem.innerHTML = `
        <button class="remove-atom-btn" onclick="removeAtomFromList(${index})" title="移除此原子">×</button>
        <div class="atom-item-header">
            <span class="atom-serial">原子 #${atomData.serial}</span>
            <span class="atom-file">${atomData.fileName}</span>
        </div>
        <div class="atom-details">
            <span class="atom-detail">
                <span class="atom-detail-label">名称:</span><span class="atom-detail-value">${atomData.name}</span>
            </span>
            <span class="atom-detail">
                <span class="atom-detail-label">残基:</span><span class="atom-detail-value">${atomData.residue}</span>
            </span>
            <span class="atom-detail">
                <span class="atom-detail-label">链:</span><span class="atom-detail-value">${atomData.chain}</span>
            </span>
        </div>
        <div class="atom-coordinates">
            (${x.toFixed(2)}, ${y.toFixed(2)}, ${z.toFixed(2)})
        </div>
    `;
    
    // 添加点击事件处理
    atomItem.addEventListener('click', function(event) {
        // 阻止事件冒泡，避免触发移除按钮
        if (event.target.classList.contains('remove-atom-btn')) {
            return;
        }
        
        // 设置口袋中心坐标
        setAtomAsPocketCenter(atomData, index);
    });
    
    return atomItem;
}

/**
 * 将选中的原子设置为对接口袋中心点
 */
function setAtomAsPocketCenter(atomData, atomIndex) {
    try {
        // 获取坐标输入框
        const pocketXInput = document.getElementById('pocket-x');
        const pocketYInput = document.getElementById('pocket-y');
        const pocketZInput = document.getElementById('pocket-z');
        
        if (!pocketXInput || !pocketYInput || !pocketZInput) {
            console.warn('对接口袋坐标输入框未找到');
            return;
        }
        
        // 设置坐标值
        const x = typeof atomData.coordinates.x === 'string' ? parseFloat(atomData.coordinates.x) : atomData.coordinates.x;
        const y = typeof atomData.coordinates.y === 'string' ? parseFloat(atomData.coordinates.y) : atomData.coordinates.y;
        const z = typeof atomData.coordinates.z === 'string' ? parseFloat(atomData.coordinates.z) : atomData.coordinates.z;
        
        pocketXInput.value = x.toFixed(2);
        pocketYInput.value = y.toFixed(2);
        pocketZInput.value = z.toFixed(2);
        
        // 更新所有原子项的选中状态样式
        updateAtomSelectionStyles(atomIndex);
        
        // 显示成功消息
        const atomName = atomData.name || 'Unknown';
        const residueName = atomData.residue || 'N/A';
        const message = `已将原子 ${atomName} (${residueName}) 的坐标设为对接口袋中心点: (${x.toFixed(2)}, ${y.toFixed(2)}, ${z.toFixed(2)})`;
        
        // 更新原子信息显示
        updateAtomInfo(message);
        
        console.log('口袋中心坐标已更新:', { x: x.toFixed(2), y: y.toFixed(2), z: z.toFixed(2) });
        
    } catch (error) {
        console.error('设置口袋中心坐标时出错:', error);
        updateAtomInfo('设置口袋中心坐标失败，请重试。');
    }
}

/**
 * 更新原子选择样式
 */
function updateAtomSelectionStyles(selectedIndex) {
    const atomItems = document.querySelectorAll('.atom-item');
    
    atomItems.forEach((item, index) => {
        if (index === selectedIndex) {
            item.classList.add('selected-for-autodock');
        } else {
            item.classList.remove('selected-for-autodock');
        }
    });
}

/**
 * 从列表中移除指定的原子
 */
function removeAtomFromList(atomIndex) {
    if (atomIndex < 0 || atomIndex >= selectedAtomsData.length) {
        console.error('无效的原子索引:', atomIndex);
        return;
    }
    
    const atomData = selectedAtomsData[atomIndex];
    
    // 准备用于样式恢复的原子对象
    const atomToRestore = {
        model: atomData.model,
        chain: atomData.chain,
        resi: atomData.resi,
        resn: atomData.residue,
        index: atomData.index
    };
    
    // 移除3D视图中的球体和标签
    if (atomData.sphereIndex < addedSpheres.length) {
        viewer.removeShape(addedSpheres[atomData.sphereIndex]);
        addedSpheres.splice(atomData.sphereIndex, 1);
    }
    
    if (atomData.labelIndex < addedLabels.length) {
        viewer.removeLabel(addedLabels[atomData.labelIndex]);
        addedLabels.splice(atomData.labelIndex, 1);
    }
    
    // 从clickedAtoms中移除
    const clickedIndex = clickedAtoms.indexOf(atomData.index);
    if (clickedIndex !== -1) {
        clickedAtoms.splice(clickedIndex, 1);
    }
    
    // 从selectedAtomsData中移除
    selectedAtomsData.splice(atomIndex, 1);
    
    // 更新剩余原子的索引
    selectedAtomsData.forEach((data, index) => {
        if (data.sphereIndex > atomData.sphereIndex) {
            data.sphereIndex--;
        }
        if (data.labelIndex > atomData.labelIndex) {
            data.labelIndex--;
        }
    });
    
    // 检测是否为小分子，只有在不是小分子的情况下才尝试恢复残基样式
    if (atomToRestore.resn && atomToRestore.resn !== 'N/A' && 
        atomToRestore.resi && atomToRestore.chain) {
        
        // 检测是否为小分子：通过原子数量判断
        const modelAtomCount = viewer.getModel(atomToRestore.model).selectedAtoms({}).length;
        const isSmallMolecule = modelAtomCount < 100;
        
        console.log(`Model ${atomToRestore.model} atom count: ${modelAtomCount}, is small molecule: ${isSmallMolecule}`);
        
        if (!isSmallMolecule) {
            console.log("Restoring residue style for protein residue:", atomToRestore.resn, atomToRestore.resi, atomToRestore.chain);
            restoreResidueToCartoonStyle(atomToRestore);
        } else {
            console.log("Skipping residue style restoration - detected small molecule");
        }
    }
    
    // 重新渲染3D视图
    viewer.render();
    
    // 更新原子信息列表显示
    updateAtomInfoList();
}

/**
 * 清除所有选中的原子
 */
function clearAllSelectedAtoms() {
    // 在清除原子之前，恢复所有残基的卡通样式
    for (let [residueKey, residueInfo] of residuesWithLinesStyle.entries()) {
        const residueSelection = {
            model: residueInfo.model,
            chain: residueInfo.chain,
            resi: residueInfo.resi
        };
        
        viewer.setStyle(residueSelection, {
            cartoon: {
                color: 'spectrum'
            }
        });
        
        console.log(`恢复残基 ${residueInfo.resn}${residueInfo.resi} (链 ${residueInfo.chain}) 为卡通样式`);
    }
    
    // 清除残基样式跟踪
    residuesWithLinesStyle.clear();
    
    // 移除所有球体和标签
    addedSpheres.forEach(sphere => viewer.removeShape(sphere));
    addedLabels.forEach(label => viewer.removeLabel(label));
    
    // 清空数组
    addedSpheres = [];
    addedLabels = [];
    clickedAtoms = [];
    selectedAtomsData = [];
    
    // 清空autodock.html中的坐标输入框
    const pocketXInput = document.getElementById('pocket-x');
    const pocketYInput = document.getElementById('pocket-y');
    const pocketZInput = document.getElementById('pocket-z');
    
    if (pocketXInput) pocketXInput.value = '0.0';
    if (pocketYInput) pocketYInput.value = '0.0';
    if (pocketZInput) pocketZInput.value = '0.0';
    
    console.log("已清空坐标输入框");
    
    // 重新渲染3D视图
    viewer.render();
    
    // 更新原子信息列表显示
    updateAtomInfoList();
    
    updateAtomInfo('所有原子标记已清除。');
}

// ==================== 文件保存功能 ====================

/**
 * 保存当前结构
 */
function saveCurrentStructure() {
    try {
        if (!uploadedFilesData.length) {
            throw new Error("没有可保存的数据");
        }
        
        // 获取第一个文件的数据作为保存内容
        const fileData = uploadedFilesData[0];
        const content = fileData.content;
        const fileExt = fileData.type;
        const mimeType = fileExt === 'pdb' ? 'chemical/x-pdb' : 'chemical/x-mdl-sdfile';
        
        // 创建 Blob 对象
        const blob = new Blob([content], { type: mimeType });
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        a.download = `molecule_${timestamp}.${fileExt}`;
        
        // 触发下载
        document.body.appendChild(a);
        a.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        console.log(`${fileExt.toUpperCase()}文件保存成功`);
        
    } catch (error) {
        console.error("保存文件时出错:", error);
    }
}

// ==================== 键盘事件处理 ====================

/**
 * 设置键盘事件监听
 */
function setupKeyboardEvents() {
    document.addEventListener('keydown', function(event) {
        if (event.key === 'r') {
            // 移除所有标记
            if (addedSpheres.length === 0 && addedLabels.length === 0) {
                return;
            }
            console.log("按键 'r' 被按下，移除所有球体");
            
            clearAllSelectedAtoms();
        }
    });
}

// ==================== 初始化和事件绑定 ====================

/**
 * 初始化所有功能
 */
function initialize() {
    console.log("show.js: Initializing...");
    
    // 检查依赖库是否已加载
    if (typeof $ === 'undefined') {
        console.error('show.js: jQuery not loaded');
        setTimeout(initialize, 100); // 重试
        return;
    }
    
    if (typeof $3Dmol === 'undefined') {
        console.error('show.js: 3Dmol.js not loaded');
        setTimeout(initialize, 100); // 重试
        return;
    }
    
    console.log('show.js: Dependencies check passed - jQuery:', typeof $, '3Dmol:', typeof $3Dmol);
    
    // 获取DOM元素引用
    fileInput = document.getElementById('file-input');
    fileListContainer = document.getElementById('file-list-container');
    viewerContainer = document.getElementById('viewer-container');
    atomInfoList = document.getElementById('atom-info-list');
    clearAtomsBtn = document.getElementById('clear-atoms-btn');
    
    console.log("show.js: Element references - fileInput:", fileInput, "fileListContainer:", fileListContainer, "viewerContainer:", viewerContainer, "atomInfoList:", atomInfoList, "clearAtomsBtn:", clearAtomsBtn);
    
    // 检查必要的DOM元素是否存在
    if (!fileInput || !fileListContainer || !viewerContainer) {
        console.error('show.js: CRITICAL - Required DOM elements not found.');
        console.error('fileInput:', fileInput);
        console.error('fileListContainer:', fileListContainer);
        console.error('viewerContainer:', viewerContainer);
        
        // 尝试重新获取元素
        setTimeout(function() {
            console.log('show.js: Retrying element lookup...');
            initialize();
        }, 500);
        return;
    }
    
    // 初始化查看器
    console.log('show.js: Initializing viewer...');
    if (!initViewer()) {
        console.error("查看器初始化失败");
        return;
    }
    
    // 设置文件上传事件
    console.log('show.js: Setting up file input event listener...');
    fileInput.addEventListener('change', (event) => {
        console.log("show.js: File input change event triggered");
        handleFileUpload(event.target.files);
        // 重置文件输入
        event.target.value = null;
    });
    
    // 设置清除原子按钮事件
    if (clearAtomsBtn) {
        console.log('show.js: Setting up clear atoms button event listener...');
        clearAtomsBtn.addEventListener('click', clearAllSelectedAtoms);
        clearAtomsBtn.disabled = true; // 初始状态为禁用
    }
    
    // 初始化原子信息列表
    updateAtomInfoList();
    
    // 设置键盘事件
    setupKeyboardEvents();
    
    // 初始渲染空列表
    renderFileList();
    
    console.log('show.js: Initialization completed successfully');
}

// ==================== 公共API ====================

// 创建全局数据管理对象
window.MoleculeViewerData = {
    get selectedAtomsData() {
        return selectedAtomsData;
    },
    get uploadedFilesData() {
        return uploadedFilesData;
    },
    get viewer() {
        return viewer;
    },
    // 添加一些有用的方法
    getSelectedAtomCount() {
        return selectedAtomsData.length;
    },
    getSelectedAtomByIndex(index) {
        return selectedAtomsData[index] || null;
    }
};

// 导出主要功能供外部调用
window.MoleculeViewer = {
    initialize,
    handleFileUpload,
    displaySelectedFiles,
    saveCurrentStructure,
    cleanupCurrentModel,
    setMoleculeStyle
};

// 为了向后兼容，也直接暴露数组引用
window.selectedAtomsData = selectedAtomsData;
window.uploadedFilesData = uploadedFilesData;

// 启动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}

// 将函数暴露到全局作用域，以便HTML中的onclick可以调用
window.removeAtomFromList = removeAtomFromList;

/**
 * 计算并设置所有选中原子的几何中心作为口袋中心
 */
function calculateAndSetGeometricCenter() {
    if (selectedAtomsData.length === 0) {
        console.warn('没有选中的原子');
        return;
    }
    
    try {
        // 获取坐标输入框
        const pocketXInput = document.getElementById('pocket-x');
        const pocketYInput = document.getElementById('pocket-y');
        const pocketZInput = document.getElementById('pocket-z');
        
        if (!pocketXInput || !pocketYInput || !pocketZInput) {
            console.warn('对接口袋坐标输入框未找到');
            return;
        }
        
        // 计算几何中心
        let sumX = 0, sumY = 0, sumZ = 0;
        
        selectedAtomsData.forEach(atomData => {
            const x = typeof atomData.coordinates.x === 'string' ? parseFloat(atomData.coordinates.x) : atomData.coordinates.x;
            const y = typeof atomData.coordinates.y === 'string' ? parseFloat(atomData.coordinates.y) : atomData.coordinates.y;
            const z = typeof atomData.coordinates.z === 'string' ? parseFloat(atomData.coordinates.z) : atomData.coordinates.z;
            
            sumX += x;
            sumY += y;
            sumZ += z;
        });
        
        const centerX = sumX / selectedAtomsData.length;
        const centerY = sumY / selectedAtomsData.length;
        const centerZ = sumZ / selectedAtomsData.length;
        
        // 设置坐标值
        pocketXInput.value = centerX.toFixed(2);
        pocketYInput.value = centerY.toFixed(2);
        pocketZInput.value = centerZ.toFixed(2);
        
        // 清除所有原子的选中状态样式
        const atomItems = document.querySelectorAll('.atom-item');
        atomItems.forEach(item => {
            item.classList.remove('selected-for-autodock');
        });
        
        // 显示成功消息
        const message = `已将 ${selectedAtomsData.length} 个原子的几何中心设为对接口袋中心点: (${centerX.toFixed(2)}, ${centerY.toFixed(2)}, ${centerZ.toFixed(2)})`;
        updateAtomInfo(message);
        
        console.log('几何中心坐标已计算并设置:', { 
            x: centerX.toFixed(2), 
            y: centerY.toFixed(2), 
            z: centerZ.toFixed(2),
            atomCount: selectedAtomsData.length
        });
        
    } catch (error) {
        console.error('计算几何中心时出错:', error);
        updateAtomInfo('计算几何中心失败，请重试。');
    }
}
 