// 3Dmol 分子查看器 Composable
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

export function useMoleculeViewer() {
  // 状态管理
  const viewer = ref(null)
  const viewerContainer = ref(null)
  const isViewerReady = ref(false)
  const loadingVisible = ref(false)
  const errorMessage = ref('')
  const uploadedFiles = ref([])
  const selectedAtoms = ref([])
  const moleculeModels = ref(new Map())

  // 对接口袋中心选择模式状态
  const pocketCenterMode = ref('single') // 'single' 或 'center'

  // 文件ID计数器
  let fileIdCounter = 0
  
  // 加载所有必需的JS库
  const loadAllScripts = async () => {
    console.log('开始加载必需的JS库...')

    try {
      // 按顺序加载JS库
      if (typeof window.$ === 'undefined') {
        await loadScript('./src/assets/scripts/jquery.js')
        console.log('jQuery 加载完成')
      }

      if (typeof window.$3Dmol === 'undefined') {
        await loadScript('./src/assets/scripts/3dmol.js')
        console.log('3Dmol.js 加载完成')
      }

      if (typeof window.SmilesDrawer === 'undefined') {
        await loadScript('./src/assets/scripts/smiles-drawer.min.js')
        console.log('SmilesDrawer 加载完成')
      }

      return true
    } catch (error) {
      console.error('加载JS库时出错:', error)
      throw new Error('加载必需的JS库失败: ' + error.message)
    }
  }

  // 初始化查看器
  const initializeViewer = async () => {
    console.log('初始化3Dmol查看器...')

    try {
      if (!viewerContainer.value) {
        throw new Error('查看器容器未找到')
      }

      // 确保所有必需的库都已加载
      await loadAllScripts()

      // 创建3Dmol查看器
      viewer.value = window.$3Dmol.createViewer(viewerContainer.value, {
        defaultcolors: window.$3Dmol.rasmolElementColors,
        backgroundColor: 0x000000
      })

      if (!viewer.value) {
        throw new Error('无法创建3Dmol查看器')
      }

      // 设置背景颜色
      viewer.value.setBackgroundColor(0x000000)

      // 确保容器可见
      if (viewerContainer.value.style) {
        viewerContainer.value.style.width = '100%'
        viewerContainer.value.style.height = '450px'
      }

      // 初始渲染
      viewer.value.render()

      isViewerReady.value = true
      console.log('3Dmol查看器初始化成功')
      return true
    } catch (error) {
      console.error('初始化3Dmol查看器时出错:', error)
      errorMessage.value = '初始化查看器失败: ' + error.message
      return false
    }
  }
  
  // 动态加载脚本
  const loadScript = (url) => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.onload = resolve
      script.onerror = reject
      document.head.appendChild(script)
    })
  }
  
  // 根据分子类型设置样式
  const setMoleculeStyle = (type, model) => {
    if (!viewer.value || !model) return
    
    if (type === 'pdb') {
      // 对于PDB文件，需要判断是蛋白质还是小分子
      const atomCount = model.selectedAtoms({}).length
      console.log(`PDB文件原子数量: ${atomCount}`)
      
      // 如果原子数量较少（小于100个原子），认为是小分子，使用球棍模型
      if (atomCount < 100) {
        console.log("检测到小分子PDB，使用球棍模型")
        viewer.value.setStyle({model: model}, {
          stick: {
            radius: 0.15,
            opacity: 0.9,
            colorscheme: 'Jmol'
          },
          sphere: {
            radius: 0.35,
            colorscheme: 'Jmol'
          }
        })
      } else {
        console.log("检测到蛋白质PDB，使用卡通模型")
        // 蛋白质样式
        viewer.value.setStyle({model: model}, {
          cartoon: {
            color: 'spectrum'
          }
        })
      }
    } else if (type === 'sdf') {
      // 小分子样式
      viewer.value.setStyle({model: model}, {
        stick: {
          radius: 0.15,
          opacity: 0.9,
          colorscheme: 'Jmol'
        },
        sphere: {
          radius: 0.35,
          colorscheme: 'Jmol'
        }
      })
    } else if (type === 'pdbqt') {
      // PDBQT文件样式 - 通常是对接结果，使用卡通模型
      console.log("检测到PDBQT文件，使用卡通模型")
      viewer.value.setStyle({model: model}, {
        cartoon: {
          color: 'spectrum'
        }
      })
    }
  }
  
  // 验证文件格式
  const validateFileContent = (content, type) => {
    if (!content || !type) return false
    
    try {
      const lines = content.trim().split('\n')
      
      if (lines.length < 2) {
        console.error(`${type}文件内容过短`)
        return false
      }
      
      if (type === 'pdb' || type === 'pdbqt') {
        // 检查是否包含ATOM或HETATM行
        const atomLines = lines.filter(line => 
          line.startsWith('ATOM') || line.startsWith('HETATM')
        )
        return atomLines.length > 0
      } else if (type === 'sdf') {
        // 检查SDF文件格式
        if (lines.length < 4) return false
        const countsLine = lines[3].trim()
        if (countsLine.length < 6) return false
        const atomCount = parseInt(countsLine.substring(0, 3))
        return atomCount > 0
      }
      
      return true
    } catch (error) {
      console.error(`验证${type}文件时出错:`, error)
      return false
    }
  }
  
  // 添加分子到查看器
  const addMoleculeToViewer = (fileData) => {
    if (!viewer.value || !isViewerReady.value) {
      console.error('查看器未准备就绪')
      return null
    }
    
    try {
      console.log(`添加${fileData.type}文件到查看器: ${fileData.name}`)
      
      // 验证文件内容
      if (!validateFileContent(fileData.content, fileData.type)) {
        throw new Error(`无效的${fileData.type.toUpperCase()}文件格式`)
      }
      
      let model = null
      
      // 根据文件类型添加模型
      if (fileData.type === 'pdb' || fileData.type === 'pdbqt') {
        model = viewer.value.addModel(fileData.content, fileData.type)
      } else if (fileData.type === 'sdf') {
        model = viewer.value.addModel(fileData.content, 'sdf')
      }
      
      if (!model) {
        throw new Error('无法创建分子模型')
      }
      
      // 设置分子样式
      setMoleculeStyle(fileData.type, model)
      
      // 存储模型信息
      const modelId = model.getID()
      moleculeModels.value.set(modelId, {
        model: model,
        fileData: fileData,
        visible: true
      })
      
      // 设置原子点击事件
      setupAtomClickHandler(model, fileData)
      
      // 重新渲染
      viewer.value.render()
      
      console.log(`成功添加分子模型，ID: ${modelId}`)
      return model
    } catch (error) {
      console.error('添加分子到查看器时出错:', error)
      throw error
    }
  }
  
  // 设置原子点击事件处理器
  const setupAtomClickHandler = (model, fileData) => {
    if (!viewer.value || !model) return

    // 为模型中的所有原子设置点击事件
    viewer.value.setClickable({model: model}, true, (atom, viewer, event, container) => {
      console.log('原子被点击:', atom)

      // 创建原子信息对象
      const atomInfo = {
        id: `${fileData.id}_${atom.serial || atom.index}`,
        element: atom.elem || 'Unknown',
        resname: atom.resn || 'UNK',
        resno: atom.resi || 0,
        x: atom.x || 0,
        y: atom.y || 0,
        z: atom.z || 0,
        serial: atom.serial || atom.index,
        fileId: fileData.id,
        fileName: fileData.name,
        includeInCenter: true // 默认参与几何中心计算
      }

      // 检查是否已经选择了这个原子
      const existingIndex = selectedAtoms.value.findIndex(a => a.id === atomInfo.id)

      if (existingIndex === -1) {
        // 添加到选择列表
        selectedAtoms.value.push(atomInfo)

        // 在原子位置添加球体标记
        viewer.value.addSphere({
          center: { x: atom.x, y: atom.y, z: atom.z },
          radius: 0.5,
          color: 'yellow',
          alpha: 0.8
        })

        console.log('原子已添加到选择列表:', atomInfo)
      } else {
        // 从选择列表中移除
        selectedAtoms.value.splice(existingIndex, 1)

        // 移除对应的球体标记
        viewer.value.removeAllShapes()
        // 重新添加剩余原子的球体标记
        selectedAtoms.value.forEach(atom => {
          viewer.value.addSphere({
            center: { x: atom.x, y: atom.y, z: atom.z },
            radius: 0.5,
            color: 'yellow',
            alpha: 0.8
          })
        })

        console.log('原子已从选择列表中移除:', atomInfo)
      }

      // 重新渲染
      viewer.value.render()
    })
  }
  
  // 清除选中的原子
  const clearSelectedAtoms = () => {
    selectedAtoms.value = []
    
    if (viewer.value) {
      // 移除所有球体标记
      viewer.value.removeAllShapes()
      viewer.value.render()
    }
    
    console.log('已清除所有选中的原子')
  }
  
  // 计算几何中心
  const calculateGeometricCenter = () => {
    if (selectedAtoms.value.length === 0) {
      return { x: 0, y: 0, z: 0 }
    }

    // 根据模式过滤参与计算的原子
    const includedAtoms = pocketCenterMode.value === 'center'
      ? selectedAtoms.value.filter(atom => atom.includeInCenter !== false)
      : selectedAtoms.value

    if (includedAtoms.length === 0) {
      return { x: 0, y: 0, z: 0 }
    }

    const sum = includedAtoms.reduce((acc, atom) => ({
      x: acc.x + atom.x,
      y: acc.y + atom.y,
      z: acc.z + atom.z
    }), { x: 0, y: 0, z: 0 })

    const count = includedAtoms.length
    return {
      x: (sum.x / count).toFixed(2),
      y: (sum.y / count).toFixed(2),
      z: (sum.z / count).toFixed(2)
    }
  }

  // 切换对接口袋中心选择模式
  const switchPocketCenterMode = (mode) => {
    pocketCenterMode.value = mode
    console.log('切换到对接口袋中心选择模式:', mode)
  }

  // 切换原子在几何中心计算中的参与状态
  const toggleAtomInCenter = (atomId) => {
    const atom = selectedAtoms.value.find(a => a.id === atomId)
    if (atom) {
      atom.includeInCenter = !atom.includeInCenter
      console.log(`原子 ${atom.element}:${atom.resname}${atom.resno} ${atom.includeInCenter ? '参与' : '排除'} 几何中心计算`)
    }
  }

  // 设置单个原子为口袋中心
  const setAtomAsPocketCenter = (atom) => {
    return {
      x: atom.x.toFixed(2),
      y: atom.y.toFixed(2),
      z: atom.z.toFixed(2)
    }
  }
  
  // 处理文件上传
  const handleFileUpload = async (files) => {
    if (!files || files.length === 0) return
    
    loadingVisible.value = true
    errorMessage.value = ''
    
    try {
      for (const file of files) {
        await processFile(file)
      }
    } catch (error) {
      console.error('文件上传处理失败:', error)
      errorMessage.value = error.message
    } finally {
      loadingVisible.value = false
    }
  }
  
  // 处理单个文件
  const processFile = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const fileData = {
            id: `file_${++fileIdCounter}_${Date.now()}`,
            name: file.name,
            type: getFileType(file.name),
            size: file.size,
            content: e.target.result,
            uploadTime: new Date().toISOString()
          }
          
          // 添加到文件列表
          uploadedFiles.value.push(fileData)
          
          // 添加到查看器
          addMoleculeToViewer(fileData)
          
          console.log('文件处理成功:', fileData.name)
          resolve(fileData)
        } catch (error) {
          console.error('处理文件时出错:', error)
          reject(error)
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsText(file)
    })
  }
  
  // 获取文件类型
  const getFileType = (filename) => {
    const ext = filename.split('.').pop().toLowerCase()
    return ext
  }
  
  // 删除文件
  const deleteFile = (fileId) => {
    const fileIndex = uploadedFiles.value.findIndex(f => f.id === fileId)
    if (fileIndex === -1) return
    
    const file = uploadedFiles.value[fileIndex]
    
    // 从查看器中移除模型
    for (const [modelId, modelInfo] of moleculeModels.value.entries()) {
      if (modelInfo.fileData.id === fileId) {
        viewer.value.removeModel(modelInfo.model)
        moleculeModels.value.delete(modelId)
        break
      }
    }
    
    // 从文件列表中移除
    uploadedFiles.value.splice(fileIndex, 1)
    
    // 移除相关的选中原子
    selectedAtoms.value = selectedAtoms.value.filter(atom => atom.fileId !== fileId)
    
    // 重新渲染
    if (viewer.value) {
      viewer.value.render()
    }
    
    console.log('文件已删除:', file.name)
  }
  
  // 获取文件类型图标
  const getFileTypeIcon = (type) => {
    const icons = {
      'pdb': '🧬',
      'sdf': '💊',
      'pdbqt': '⚗️',
      'mol2': '🔬'
    }
    return icons[type] || '📄'
  }
  
  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  // 清理资源
  const cleanup = () => {
    if (viewer.value) {
      viewer.value.removeAllModels()
      viewer.value = null
    }
    uploadedFiles.value = []
    selectedAtoms.value = []
    moleculeModels.value.clear()
    isViewerReady.value = false
  }
  
  // 生命周期钩子
  onMounted(() => {
    nextTick(() => {
      initializeViewer()
    })
  })
  
  onUnmounted(() => {
    cleanup()
  })
  
  return {
    // 状态
    viewer,
    viewerContainer,
    isViewerReady,
    loadingVisible,
    errorMessage,
    uploadedFiles,
    selectedAtoms,
    pocketCenterMode,

    // 方法
    loadAllScripts,
    initializeViewer,
    handleFileUpload,
    deleteFile,
    clearSelectedAtoms,
    calculateGeometricCenter,
    switchPocketCenterMode,
    toggleAtomInCenter,
    setAtomAsPocketCenter,
    getFileTypeIcon,
    formatFileSize,
    cleanup
  }
}
