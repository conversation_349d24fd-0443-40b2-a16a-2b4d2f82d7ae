<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变效果用于蛋白质表面高光 -->
  <defs>
    <radialGradient id="proteinGradient" cx="0.25" cy="0.25" r="0.8">
      <stop offset="0%" style="stop-color:#ffe1f1;stop-opacity:1"/>
      <stop offset="30%" style="stop-color:#ffc1e3;stop-opacity:0.95"/>
      <stop offset="70%" style="stop-color:#f8bbd9;stop-opacity:0.9"/>
      <stop offset="100%" style="stop-color:#f48fb1;stop-opacity:0.85"/>
    </radialGradient>
    <!-- 添加额外的高光效果 -->
    <radialGradient id="highlight" cx="0.2" cy="0.2" r="0.3">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8"/>
      <stop offset="60%" style="stop-color:#ffffff;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0"/>
    </radialGradient>
  </defs>
  
  <g transform="translate(12,12) scale(1.25) translate(-12,-12)">
    <!-- 蛋白质表面形状 - 球体 -->
    <circle cx="14" cy="12" r="5.5" fill="url(#proteinGradient)"/>
    <!-- 高光效果 -->
    <circle cx="14" cy="12" r="5.5" fill="url(#highlight)"/>

    <!-- 最大立方体外框 - 深绿色细实线 -->
    
    <!-- 前面外框 -->
    <rect x="4" y="8" width="12" height="12" stroke="#2e7d32" stroke-width="0.6" fill="none" stroke-linecap="round"/>
    
    <!-- 后面外框 (偏移显示3D效果) -->
    <rect x="8" y="4" width="12" height="12" stroke="#2e7d32" stroke-width="0.6" fill="none" stroke-linecap="round"/>
    
    <!-- 连接前后面的边 -->
    <line x1="4" y1="8" x2="8" y2="4" stroke="#2e7d32" stroke-width="0.6" stroke-linecap="round"/>
    <line x1="16" y1="8" x2="20" y2="4" stroke="#2e7d32" stroke-width="0.6" stroke-linecap="round"/>
    <line x1="16" y1="20" x2="20" y2="16" stroke="#2e7d32" stroke-width="0.6" stroke-linecap="round"/>
    <line x1="4" y1="20" x2="8" y2="16" stroke="#2e7d32" stroke-width="0.6" stroke-linecap="round"/>
    
    <!-- 内部8个小立方体 - 深绿色细虚线 -->
    
    <!-- 前层4个小立方体 -->
    <!-- 前左上小立方体 -->
    <rect x="4" y="8" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    <!-- 前右上小立方体 -->
    <rect x="10" y="8" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    <!-- 前左下小立方体 -->
    <rect x="4" y="14" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    <!-- 前右下小立方体 -->
    <rect x="10" y="14" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    
    <!-- 后层4个小立方体 -->
    <!-- 后左上小立方体 -->
    <rect x="8" y="4" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    <!-- 后右上小立方体 -->
    <rect x="14" y="4" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    <!-- 后左下小立方体 -->
    <rect x="8" y="10" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    <!-- 后右下小立方体 -->
    <rect x="14" y="10" width="6" height="6" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" fill="none" stroke-linecap="round"/>
    
    <!-- 连接前后小立方体的深度线 -->
    <!-- 左上立方体连接 -->
    <line x1="4" y1="8" x2="8" y2="4" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    <line x1="10" y1="8" x2="14" y2="4" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    <line x1="4" y1="14" x2="8" y2="10" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    <line x1="10" y1="14" x2="14" y2="10" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    
    <!-- 右上立方体连接 -->
    <line x1="16" y1="8" x2="20" y2="4" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    <line x1="16" y1="14" x2="20" y2="10" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    
    <!-- 左下立方体连接 -->
    <line x1="4" y1="20" x2="8" y2="16" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    <line x1="10" y1="20" x2="14" y2="16" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
    
    <!-- 右下立方体连接 -->
    <line x1="16" y1="20" x2="20" y2="16" stroke="#2e7d32" stroke-width="0.5" stroke-dasharray="1,1" stroke-linecap="round"/>
  </g>
</svg> 