<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3Dmol.js 鼠标点击显示原子编号 (XYZ 格式)</title>
    <link rel="icon" type="image/png" href="/src/images/icon.png">
    <script src="/static/scripts/jquery.js"></script>
    <script src="/static/scripts/3dmol.js"></script>
    <script src="https://unpkg.com/smiles-drawer@2.0.1/dist/smiles-drawer.min.js"></script>
    <style>

        .main-viewer {
            position: relative;  /* 添加相对定位 */
            width: 600px;
            height: 400px;
            border: 1px solid black;
            background-color: black;
            margin-bottom: 20px;
        }

        /* 添加新的样式类来包装主查看器 */
        .main-viewer-wrapper {
            position: relative;
            width: 600px;
            height: 400px;
        }

        /* 确保 canvas 元素正确定位 */
        .main-viewer canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .large-3d-viewer {
            width: 600px;
            height: 400px;
            border: 1px solid #ccc;
            background-color: black;
        }

        #info {
            margin-top: 10px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        #resetButton {
            margin-top: 10px;
            padding: 5px 10px;
            font-size: 14px;
        }
        #saveButton {
            margin-top: 10px;
            padding: 5px 10px;
            font-size: 14px;
        }
        #loadButton {
            margin-top: 10px;
            padding: 5px 10px;
            font-size: 14px;
        }

        /* 更新样式 */
        #structure2d-container {
            margin-top: 20px;
            padding: 10px;
            text-align: left;
            display: flex;
            gap: 20px;
        }
        
        #molecules-list {
            width: 200px;
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
        }
        
        .molecule-item {
            cursor: pointer;
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
        }
        
        .molecule-item:hover {
            background-color: #f0f0f0;
        }
        
        .molecule-item.selected {
            background-color: #e0e0e0;
        }
        
        #structure2d {
            border: 1px solid #ccc;
            background: white;
        }

        /*搜索栏*/
        .search-container {
        margin-bottom: 10px;
        padding: 5px;
        display: flex;
        gap: 5px;
    }
    
        #molecule-search {
            width: 120px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        #search-button {
            padding: 5px 10px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 3px;
            cursor: pointer;
        }
        
        #search-button:hover {
            background-color: #e0e0e0;
        }

        /* 添加 centers 相关样式 */
        #centers-container {
            margin-top: 20px;
            padding: 10px;
            border-top: 1px solid #ccc;
        }
        
        .center-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .center-label {
            min-width: 80px;
        }
        
        .coordinate-inputs {
            display: flex;
            gap: 10px;
        }
        
        .coordinate-input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        .delete-center-btn {
            padding: 5px 10px;
            background-color: #ff4444;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .delete-center-btn:hover {
            background-color: #cc0000;
        }

        /* 添加独立center view 相关样式*/
        /* 修改 center viewer 相关样式 */
        .center-viewer-container {
            display: flex;
            flex-direction: column;  /* 改为垂直排列 */
            gap: 10px;
            margin-top: 10px;
            width: 100%;  /* 确保容器占满宽度 */
        }

        .center-3d-viewer {
            width: 400px;
            height: 300px;
            border: 1px solid #ccc;
            position: relative;  /* 添加相对定位 */
            background-color: black;  /* 添加背景色 */
        }

        .center-item {
            display: flex;
            flex-direction: column;  /* 改为垂直排列 */
            gap: 10px;
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .center-controls {
            display: flex;
            gap: 10px;
            margin-top: 5px;
        }
        
        .load-protein-btn {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .load-protein-btn:hover {
            background-color: #45a049;
        }
        
        .protein-file-input {
            display: none;
        }

        #multi-model-container {
            margin-top: 40px;
            margin-bottom: 20px;
            padding: 20px;
            border-top: 2px solid #ccc;
            width: 100%;
            background-color: #fff;
        }

        .viewer-content {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .viewer-wrapper {
            flex: 1;
            position: relative;
            width: 600px;
            height: 400px;
        }

        #multi-model-viewer {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            border: 1px solid #ccc;
            background-color: black;
        }

        .multi-model-panel {
            width: 300px;
            flex-shrink: 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .multi-model-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
        }

    .molecule-item:hover {
        background-color: #f0f0f0;
    }

    .style-controls {
        margin-top: 5px;
        padding: 5px;
        background-color: #f9f9f9;
        border-radius: 3px;
    }

    .style-button {
        margin: 2px;
        padding: 3px 8px;
        border: 1px solid #ddd;
        border-radius: 3px;
        background-color: white;
        cursor: pointer;
    }

    .style-button.active {
        background-color: #e0e0e0;
    }

    .molecule-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 5px;
        cursor: pointer;
    }

    .molecule-checkbox {
        margin: 0;
        cursor: pointer;
    }

    .model-group {
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
    }

    .model-header {
        display: flex;
        justify-content: space-between;
        padding: 5px;
        background-color: #f5f5f5;
        border-radius: 3px;
        margin-bottom: 5px;
    }

    .residues-list {
        margin-top: 10px;
        padding: 5px;
        border-top: 1px solid #ddd;
    }

    .residue-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px;
        margin: 2px 0;
        background-color: #f5f5f5;
        border-radius: 3px;
    }

    .residue-style-controls {
        display: flex;
        gap: 5px;
    }

    .residue-style-controls button {
        padding: 2px 5px;
        font-size: 12px;
        border: 1px solid #ddd;
        border-radius: 3px;
        background-color: white;
        cursor: pointer;
    }

    .residue-style-controls button:hover {
        background-color: #e0e0e0;
    }


    </style>
</head>

<body>
    <div class="main-viewer-wrapper">
        <div id="main-viewer" class="main-viewer"></div>
    </div>
    <div id="info">点击原子以查看其编号和坐标。</div>
    <div class="button-group"></div>
        <input type="file" id="pdbFileInput" accept=".pdb,.sdf" style="display: none;" />
        <button id="loadButton">加载文件</button>
        <button id="resetButton">复原原始数据</button>
        <button id="saveButton">保存当前结构</button>
    </div>
    <!-- 修改 2D 结构显示区域的 HTML 结构 -->
    <div id="structure2d-container">
        <div class="search-container">
            <input type="number" id="molecule-search" min="1" placeholder="输入分子序号...">
            <button id="search-button">搜索</button>
        </div>
        <div id="molecules-list"></div>
        <canvas id="structure2d" width="400" height="300"></canvas>
    </div>
    <!-- center 编辑 -->
    <div id="centers-container">
        <button id="add-center-btn">添加口袋Center</button>
        <div id="centers-list"></div>
    </div>

    <!-- 多文件查看器部分 -->
    <div id="multi-model-container">
        <div class="viewer-header">
            <h3>多文件查看器</h3>
            <div class="viewer-controls">
                <input type="file" id="multiModelFileInput" accept=".pdb,.sdf" style="display: none;" multiple />
                <button id="loadMultiModelButton">加载分子文件</button>
                <input type="file" id="pharmacophoreFileInput" accept=".json" style="display: none;" />
                <button id="loadPharmacophoreButton">加载药效团</button>
            </div>
        </div>
        <div class="viewer-content">
            <div class="viewer-wrapper">
                <div id="multi-model-viewer"></div>
            </div>
            <div class="multi-model-panel">
                <div id="multi-model-list" class="multi-model-list"></div>
            </div>
        </div>
    </div>

    <div id="n1info">点击原子以查看其编号和坐标。</div>
    
    <script>
        window.onload = function () {
            // 1. 声明全局变量
            let mainViewer; // 主查看器
            let model;
            let data = '';

            // 2. 创建主查看器的初始化函数
            function initializeMainViewer() {
                if (mainViewer) {
                    console.log("主查看器已存在，跳过初始化");
                    return mainViewer;
                }

                console.log("初始化主查看器...");
                mainViewer = $3Dmol.createViewer('main-viewer', {
                    defaultcolors: $3Dmol.rasmolElementColors,
                    backgroundColor: 'black',
                    width: 600,          // 明确指定宽度
                    height: 400         // 明确指定高度
                });

                // 确保查看器完全初始化
                mainViewer.render(() => {
                    console.log("主查看器初始化完成");
                });
                            
                return mainViewer;
            }

            console.log("Window loaded");

            // 3. 按顺序初始化各个组件
            try {
                console.log("开始初始化...");
                
                // 初始化主查看器
                mainViewer = initializeMainViewer();
                
                // 加载分子列表
                loadMoleculesList();
                
                // 设置中心点管理
                setupCentersManagement();
                
                // 设置多文件查看器
                const multiViewer = setupMultiModelViewer();
                
                console.log("初始化完成");
            } catch (error) {
                console.error("初始化过程出错:", error);
            }


            // 检查 $3Dmol 是否已加载
            if (typeof $3Dmol === 'undefined') {
                console.error("3Dmol.js 未加载");
                return;
            }
            console.log("3Dmol.js 已加载");

            // // 创建一个 viewer 实例
            // viewer = $3Dmol.createViewer('main-viewer', { 
            //     defaultcolors: $3Dmol.rasmolElementColors 
            // });
            // console.log("Viewer created");

            // 获取 DOM 元素
            const fileInput = document.getElementById('pdbFileInput');
            const loadButton = document.getElementById('loadButton');


            // 在window.onload函数内部添加cleanupCurrentModel函数
            function cleanupCurrentModel() {
                console.log("清理当前模型...");
                try {
                    // 清除所有标记和球体
                    if (addedSpheres && addedSpheres.length > 0) {
                        addedSpheres.forEach(sphere => mainViewer.removeShape(sphere));
                        addedSpheres = [];
                    }
                    
                    if (addedLabels && addedLabels.length > 0) {
                        addedLabels.forEach(label => mainViewer.removeLabel(label));
                        addedLabels = [];
                    }
                    
                    // 清除点击记录
                    clickedAtoms = [];
                    
                    // 移除当前模型
                    if (model) {
                        mainViewer.removeModel(model);
                        model = null;
                    }
                    
                    // 重新渲染视图
                    mainViewer.render();
                    console.log("模型清理完成");
                    
                } catch (error) {
                    console.error("清理模型时出错:", error);
                    throw error;
                }
            }

            // 添加文件加载按钮事件监听器
            loadButton.addEventListener('click', function() {
                fileInput.click();
            });

            // 文件读取处理
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (!file) {
                    console.log("未选择文件");
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const fileContent = e.target.result;
                        const fileExt = file.name.split('.').pop().toLowerCase();

                        // 根据文件类型选择验证方法
                        let isValid = false;
                        if (fileExt === 'pdb') {
                            isValid = validatePDBData(String(fileContent));
                        } else if (fileExt === 'sdf') {
                            isValid = validateSDFData(String(fileContent));
                        } else {
                            throw new Error("不支持的文件格式");
                        }
                        
                        if (!isValid) {
                            throw new Error("无效的文件格式");
                        }
                        
                        data = String(fileContent);
                        origin_data = data;
                        
                        cleanupCurrentModel();

                        // 加载模型时指定正确的格式
                        model = mainViewer.addModel(data, fileExt, {
                            keepH: true,
                            assignBonds: fileExt === 'pdb',
                            bonds: true
                        });
                        
                        // 设置样式
                        mainViewer.setStyle({}, {
                            stick: {
                                radius: 0.15,
                                opacity: 0.9,
                                colorscheme: 'Jmol'
                            },
                            sphere: {
                                radius: 0.35,
                                colorscheme: 'Jmol'
                            }
                        });

                        // 更新视图
                        mainViewer.zoomTo();
                        setClickable();
                        mainViewer.render();

                        console.log("文件加载成功");
                        infoDiv.innerHTML = `${fileExt.toUpperCase()}文件加载成功`;
                            
                    } catch (error) {
                        console.error("处理文件时出错:", error);
                        infoDiv.innerHTML = "处理文件时出错: " + error.message;
                    }
                };
                
                reader.onerror = function(error) {
                    console.error("读取文件时出错:", error);
                    infoDiv.innerHTML = "读取文件时出错";
                };
                
                reader.readAsText(file);


            });


            // 修改文件保存功能
            document.getElementById('saveButton').addEventListener('click', function() {
                try {
                    // 检查data是否存在且为字符串
                    if (!data) {
                        throw new Error("没有可保存的数据");
                    }
                    
                    // 确定文件格式
                    const fileExt = data.trim().startsWith('ATOM') ? 'pdb' : 'sdf';
                    const mimeType = fileExt === 'pdb' ? 'chemical/x-pdb' : 'chemical/x-mdl-sdfile';
                    
                    // 创建 Blob 对象
                    const blob = new Blob([data], { type: mimeType });
                    
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    
                    // 生成文件名（使用当前时间戳）
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    a.download = `molecule_${timestamp}.${fileExt}`;
                    
                    // 触发下载
                    document.body.appendChild(a);
                    a.click();
                    
                    // 清理
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    infoDiv.innerHTML = `结构已保存为${fileExt.toUpperCase()}文件`;
                    console.log(`${fileExt.toUpperCase()}文件保存成功`);
                    
                } catch (error) {
                    console.error("保存文件时出错:", error);
                    infoDiv.innerHTML = "保存文件失败: " + error.message;
                }
            });

            // 修改loadModel函数中的模型加载部分
            function loadModel() {
                return new Promise((resolve, reject) => {
                    try {

                        console.log("开始加载PDB数据...");

                        // 确保PDB数据格式正确
                        let cleanData = data.trim().split('\n')
                            .map(line => line.trimLeft())  // 移除每行开头的空格
                            .join('\n');

                        // 添加模型并配置选项
                            model = viewer.addModel(cleanData, "pdb", {
                            keepH: true,           // 保留氢原子
                            assignBonds: true,     // 自动分配化学键
                            bonds: true            // 显示键
                        });

                        // 立即检查模型
                        console.log("模型加载状态:", {
                            modelExists: !!model,
                            hasAtoms: !!(model && model.atoms),
                            atomCount: model && model.atoms ? model.atoms.length : 0
                        });

                        // 设置样式
                        mainViewer.setStyle({}, {
                            stick: {
                                radius: 0.15,
                                opacity: 0.9,
                                colorscheme: 'Jmol'
                            },
                            sphere: {
                                radius: 0.35,
                                colorscheme: 'Jmol'
                            }
                        });

                        // 确保正确渲染
                        mainViewer.zoomTo();

                        // 使用requestAnimationFrame确保渲染完成
                        requestAnimationFrame(() => {
                            mainViewer.render();

                            if (model && model.atoms && model.atoms.length > 0) {
                                console.log("Model loaded successfully");
                                onModelLoaded(); // 添加这行
                                resolve(model);
                            } else {
                                reject(new Error("模型加载后未找到原子数据"));
                            }
                        });

                    } catch (error) {
                        console.error("模型加载错误:", error);
                        reject(error);
                    }
                });
            }

            function validateSDFData(data) {
                const lines = data.trim().split('\n');
                console.log("验证SDF数据:");
                console.log("总行数:", lines.length);

                if (lines.length < 4) { // SDF至少需要4行
                    console.error("SDF数据行数不足");
                    return false;
                }

                // 检查原子和键的数量行
                const countsLine = lines[3].trim();
                if (countsLine.length < 6) {
                    console.error("无效的原子/键计数行");
                    return false;
                }

                const atomCount = parseInt(countsLine.substring(0, 3));
                console.log("SDF声明的原子数:", atomCount);

                return atomCount > 0;
            }

            // 修改validateSDFData函数为validatePDBData
            function validatePDBData(data) {
                const lines = data.trim().split('\n');
                console.log("验证PDB数据:");
                console.log("总行数:", lines.length);

                if (lines.length < 2) {
                    console.error("PDB数据行数不足");
                    return false;
                }
            // 计算原子数量并输出调试信息（包括ATOM和HETATM）
            const atomLines = lines.filter(line => 
                line.startsWith('ATOM') || line.startsWith('HETATM')
            );
            const atomCount = atomLines.length;

            // 分别统计ATOM和HETATM的数量
            const standardAtoms = atomLines.filter(line => line.startsWith('ATOM')).length;
            const heteroAtoms = atomLines.filter(line => line.startsWith('HETATM')).length;
            console.log("找到的原子行:", atomCount);
            console.log("标准原子(ATOM)数量:", standardAtoms);
            console.log("杂原子(HETATM)数量:", heteroAtoms);
            
            console.log("找到的ATOM行:", atomCount);
            if (atomCount > 0) {
                console.log("第一个原子行示例:", atomLines[0]);
            }

                return atomCount > 0;
        }


            // 获取用于显示信息的 DOM 元素
            var infoDiv = document.getElementById('info');

            // 存储添加的球体
            var addedSpheres = [];
            var addedLabels = [];
            var clickedAtoms = [];

            // 修改setClickable函数
            function setClickable() {
                if (!mainViewer || !model) {
                    console.error("Viewer或Model未初始化");
                    return;
                }
                
                console.log("设置点击处理程序...");
                
                // 先移除旧的点击事件
                mainViewer.setClickable({}, false);
                
                // 添加新的点击事件
                mainViewer.setClickable({}, true, function(atom, mainViewer, event, container) {
                    if (!atom) {
                        console.log("未点击到原子");
                        return;
                    }
                    
                    console.log("点击事件触发，原子:", atom);
                    
                    const atomIndex = atom.serial;
                    const existingIndex = clickedAtoms.indexOf(atom.index);
                    
                    if (existingIndex !== -1) {
                        // 取消选择原子
                        handleAtomDeselection(existingIndex, atomIndex);
                    } else {
                        // 选择新原子
                        handleAtomSelection(atom);
                    }
                    
                    mainViewer.render();
                });
                
                console.log("点击处理程序设置完成");
            }

            // 处理原子选择
            function handleAtomSelection(atom) {
                const atomIndex = atom.serial;
                const atomName = atom.atom;
                const resName = atom.resn;
                const atomPosition = `(${atom.x.toFixed(2)}, ${atom.y.toFixed(2)}, ${atom.z.toFixed(2)})`;
                
                // 添加标签
                const label = mainViewer.addLabel(`${atomIndex}`, {
                    position: { x: atom.x, y: atom.y, z: atom.z },
                    backgroundColor: 'white',
                    fontColor: 'red',
                    fontSize: 10,
                    showBackground: false
                });
                addedLabels.push(label);
                
                // 添加高亮球体
                const sphere = mainViewer.addSphere({
                    center: { x: atom.x, y: atom.y, z: atom.z },
                    radius: 0.5,
                    color: 'green',
                    opacity: 0.7
                });
                addedSpheres.push(sphere);
                
                clickedAtoms.push(atom.index);
                
                infoDiv.innerHTML = `
                    点击的原子:
                    编号: ${atomIndex}
                    名称: ${atomName}
                    残基: ${resName}
                    坐标: ${atomPosition}
                `;
            }

            // 处理原子取消选择
            function handleAtomDeselection(existingIndex, atomIndex) {
                mainViewer.removeShape(addedSpheres[existingIndex]);
                mainViewer.removeLabel(addedLabels[existingIndex]);
                
                addedSpheres.splice(existingIndex, 1);
                addedLabels.splice(existingIndex, 1);
                clickedAtoms.splice(existingIndex, 1);
                
                infoDiv.innerHTML = `已取消选择原子 ${atomIndex}`;
            }

            // 添加到loadModel成功后的处理
            function onModelLoaded() {
                console.log("模型加载完成，设置点击事件");
                setClickable();
                mainViewer.render();
            }                                               
    
            
            // 移除初始的setClickable调用
            // setClickable();  // 删除这行
            
            console.log("初始化完成");

            // 添加键盘事件监听器
            document.addEventListener('keydown', function (event) {
                if (event.key === 'r') {
                    if (addedSpheres.length === 0 && addedLabels.length === 0) {
                        return;
                    }
                    console.log("Key 'r' pressed, removing all added spheres");
                    // 移除所有添加的球体
                    addedSpheres.forEach(function (sphere) {
                        mainViewer.removeShape(sphere);
                    });
                    
                    // 移除所有添加的标签
                    addedLabels.forEach(function (label) {
                        mainViewer.removeLabel(label);
                    });
                    addedSpheres = [];
                    addedLabels = [];
                    clickedAtoms = [];
                    mainViewer.render();
                    infoDiv.innerHTML = '所有球体已移除。';
                } else if (event.key === 'd') {
                    if (clickedAtoms.length === 0) {
                        console.log("No atoms selected to remove");
                        return;
                    }
                    console.log("删除选中的原子...");
                    
                    try {
    
                        const fileExt = data.trim().startsWith('ATOM') ? 'pdb' : 'sdf';

                        if (fileExt === 'pdb') {
                            // 解析 PDB 数据
                            let lines = data.trim().split('\n');
                            let newLines = [];
                            let currentAtomIndex = 1;
                            let oldToNewIndex = new Map();
                            let connectRecords = [];  // 存储CONECT记录
                            
                            // 第一遍扫描：收集CONECT记录和处理原子
                            for (let line of lines) {
                                if (line.startsWith('ATOM') || line.startsWith('HETATM')) {
                                    let atomSerial = parseInt(line.substring(6, 11));
                                    if (!clickedAtoms.includes(atomSerial - 1)) {
                                        // 记录原子编号映射
                                        oldToNewIndex.set(atomSerial, currentAtomIndex);
                                        
                                        // 更新原子编号
                                        let newLine = 
                                            line.substring(0, 6) +
                                            String(currentAtomIndex).padStart(5) +
                                            line.substring(11);
                                        newLines.push(newLine);
                                        currentAtomIndex++;
                                    }
                                } else if (line.startsWith('CONECT')) {
                                    connectRecords.push(line);  // 保存CONECT记录
                                } else if (!line.startsWith('END')) {
                                    newLines.push(line);
                                }
                            }
                            
                            // 第二遍扫描：处理CONECT记录
                            for (let connect of connectRecords) {
                                let parts = connect.split(/\s+/).filter(Boolean);
                                let atomNums = parts.slice(1).map(x => parseInt(x));
                                let mainAtom = atomNums[0];
                                
                                // 只处理未删除的原子的连接
                                if (oldToNewIndex.has(mainAtom)) {
                                    let newConnect = ['CONECT'];
                                    // 添加主原子的新编号
                                    newConnect.push(String(oldToNewIndex.get(mainAtom)).padStart(5));
                                    
                                    // 添加连接原子的新编号
                                    for (let i = 1; i < atomNums.length; i++) {
                                        if (oldToNewIndex.has(atomNums[i])) {
                                            newConnect.push(String(oldToNewIndex.get(atomNums[i])).padStart(5));
                                        }
                                    }
                                    
                                    // 只添加有效的连接记录
                                    if (newConnect.length > 2) {
                                        newLines.push(newConnect.join(''));
                                    }
                                }
                            }
                            
                            // 添加END记录
                            newLines.push('END');
                            
                            // 更新PDB数据
                            data = newLines.join('\n');
                            
                            // 清理当前模型和标记
                            cleanupCurrentModel();
                            
                            // 添加新模型，保持原有连接
                            model = mainViewer.addModel(data, "pdb", {
                                keepH: true,
                                assignBonds: false,  // 禁用自动键分配
                                bonds: true
                            });
                            
                            // 设置样式
                            mainViewer.setStyle({}, {
                                stick: {
                                    radius: 0.15,
                                    opacity: 0.9,
                                    colorscheme: 'Jmol'
                                },
                                sphere: {
                                    radius: 0.35,
                                    colorscheme: 'Jmol'
                                }
                            });

                            // 更新视图
                            mainViewer.zoomTo();
                            setClickable();
                            mainViewer.render();
                        }
                        else {
                            // SDF格式处理
                            console.log("原始数据: ", data)
                            let lines = data.trim().split('\n');
                            let newLines = [];
                            let countLineIndex = -1;
                            let atomCount = 0;  // 在这里声明
                            let bondCount = 0;  // 在这里声明
                        
                            // 从第1行开始查找原子数和键数行
                            for (let i = 0; i < lines.length; i++) {
                                const line = lines[i].trim();
                                // 使用正则表达式匹配两个开头的正整数
                                const matches = line.match(/^\s*(\d+)\s+(\d+)/);
                                if (matches) {
                                    atomCount = parseInt(matches[1]);
                                    bondCount = parseInt(matches[2]);
                                    countLineIndex = i;
                                    console.log("找到原子和键数量行:", i);
                                    console.log("原子数:", atomCount);
                                    console.log("键数:", bondCount);
                                    break;
                                }
                            }

                            if (countLineIndex === -1) {
                                throw new Error("无法找到原子和键数量行");
                            }

                            // 保留前N行（分子名称、程序信息、注释）
                            newLines.push('');
                            newLines.push(...lines.slice(0, countLineIndex));

                            // 解析原子和键的数量
                            let countsLine = lines[countLineIndex];
                            console.log("处理计数行:", countsLine);
                            atomCount = parseInt(countsLine.substring(0, 3));
                            bondCount = parseInt(countsLine.substring(3, 6));
                            
                            console.log("原始原子数:", atomCount);
                            console.log("原始键数:", bondCount);
                            console.log("要删除的原子索引:", clickedAtoms);
                            
                            // 创建原子映射
                            let atomLines = lines.slice(countLineIndex + 1, countLineIndex + 1 + atomCount);
                            let keptAtoms = [];
                            let oldToNewIndex = new Map();
                            let newAtomIndex = 1;
                            
                            // 修改索引处理逻辑
                            atomLines.forEach((line, index) => {
                                // index 是从0开始的数组索引
                                const currentAtomIndex = index + 1;  // SDF文件中原子编号从1开始
                                
                                // 在删除前输出调试信息
                                console.log(`处理原子 ${currentAtomIndex}:`);
                                console.log(`clickedAtoms:`, clickedAtoms);
                                
                                // 检查此原子是否被选中删除
                                if (!clickedAtoms.includes(index)) {  // 修改这里的比较逻辑
                                    keptAtoms.push(line);
                                    oldToNewIndex.set(currentAtomIndex, newAtomIndex);
                                    console.log(`保留原子 ${currentAtomIndex} -> ${newAtomIndex}`);
                                    newAtomIndex++;
                                } else {
                                    console.log(`删除原子 ${currentAtomIndex}`);
                                }
                            });
                            
                            let newAtomCount = keptAtoms.length;
                            
                            // 处理化学键
                            let bondLines = [];
                            let originalBondLines = lines.slice(countLineIndex + 1 + atomCount, 
                                                        countLineIndex + 1 + atomCount + bondCount);
                            // console.log("原始键数:", originalBondLines)
                            console.log("原始键数:", oldToNewIndex)
                            // 更新键的处理
                            originalBondLines.forEach(line => {
                                let atom1 = parseInt(line.substring(0, 3));
                                let atom2 = parseInt(line.substring(3, 6));
                                let bondOrder = line.substring(6, 9);
                                let bondType = line.substring(9).trim();
                                
                                // 检查两个原子是否被删除
                                const atom1Deleted = clickedAtoms.includes(atom1 - 1);
                                const atom2Deleted = clickedAtoms.includes(atom2 - 1);
                                
                                // 如果两个原子都未被删除，则保留这个键
                                if (!atom1Deleted && !atom2Deleted) {
                                    // 计算新的原子编号
                                    let newAtom1 = atom1;
                                    let newAtom2 = atom2;
                                    
                                    // 对于每个被删除的原子，更新后续原子的编号
                                    clickedAtoms.forEach(deletedIndex => {
                                        // 原子编号需要减1，因为deletedIndex是从0开始的
                                        const deletedAtom = deletedIndex + 1;
                                        
                                        // 如果原子编号大于被删除的原子编号，则减1
                                        if (atom1 > deletedAtom) newAtom1--;
                                        if (atom2 > deletedAtom) newAtom2--;
                                    });
                                    
                                    // 创建新的键记录
                                    let newLine = 
                                        String(newAtom1).padStart(3) +
                                        String(newAtom2).padStart(3) +
                                        bondOrder +
                                        (bondType ? ' ' + bondType : '');
                                        
                                    bondLines.push(newLine);
                                    console.log(`更新键: ${atom1}-${atom2} -> ${newAtom1}-${newAtom2}`);
                                } else {
                                    console.log(`删除键: ${atom1}-${atom2} (原子被删除)`);
                                }
                            });
                            
                            // 更新计数行
                            let newCountsLine = 
                                String(newAtomCount).padStart(3) +
                                String(bondLines.length).padStart(3) +
                                countsLine.substring(6);
                            newLines.push(newCountsLine);

                            // 添加原子和键信息
                            newLines.push(...keptAtoms);
                            newLines.push(...bondLines);
                            
                            // 添加结束标记
                            newLines.push('M  END');
                            newLines.push('$$$$')

                            // 清理当前模型
                            cleanupCurrentModel();

                            // 更新数据并记录调试信息
                            data = newLines.join('\n');
                            console.log("总行数:", newLines.length);
                            console.log("原子数:", newAtomCount);
                            console.log("键数:", bondLines.length);  
                            
                            // 加载新模型
                            console.log("正在加载新模型...");
                            console.log("新数据:", data);  

                            model = mainViewer.addModel(data, 'sdf', {
                                keepH: true,
                                assignBonds: false,  // 使用文件中的键信息
                                bonds: true
                            });   
                                                         
                            // 设置样式
                            mainViewer.setStyle({}, {
                                stick: {
                                    radius: 0.15,
                                    opacity: 0.9,
                                    colorscheme: 'Jmol'
                                },
                                sphere: {
                                    radius: 0.35,
                                    colorscheme: 'Jmol'
                                }
                            });

                            // 更新视图
                            mainViewer.zoomTo();
                            setClickable();
                            // 使用 Promise 确保渲染完成
                            return new Promise((resolve) => {
                                requestAnimationFrame(() => {
                                    mainViewer.render();
                                    console.log("视图渲染完成，原子数:", model.atoms);
                                    resolve();
                                });
                            });
                        }
   
                    } catch (error) {
                        console.error("删除原子时出错:", error);
                        infoDiv.innerHTML = "删除原子时出错: " + error.message;
                    }

                }
            });
            // 修改复原按钮事件监听器
            document.getElementById('resetButton').addEventListener('click', function () {
                console.log("Reset button clicked, restoring original PDB data");
                
                try {
                    // 清除现有的标记和球体
                    addedSpheres.forEach(sphere => mainViewer.removeShape(sphere));
                    addedLabels.forEach(label => mainViewer.removeLabel(label));
                    addedSpheres = [];
                    addedLabels = [];
                    clickedAtoms = [];
                    
                    // 恢复原始数据
                    data = origin_data;

                    // 确定文件格式
                    const fileExt = data.trim().startsWith('ATOM') ? 'pdb' : 'sdf';
                    console.log("Restoring file format:", fileExt);
                    
                    // 移除当前模型
                    if (model) {
                        mainViewer.removeModel(model);
                    }
                    
                    // 添加新模型，根据文件格式设置适当的选项
                    model = mainViewer.addModel(data, fileExt, {
                        keepH: true,
                        assignBonds: fileExt === 'pdb',  // 只有 PDB 格式才自动分配键
                        bonds: true
                    });
                    
                    // 设置样式
                    mainViewer.setStyle({}, {
                        stick: {
                            radius: 0.15,
                            opacity: 0.9,
                            colorscheme: 'Jmol'
                        },
                        sphere: {
                            radius: 0.35,
                            colorscheme: 'Jmol'
                        }
                    });
                    
                    // 更新视图
                    mainViewer.zoomTo();
                    // 重要：先设置点击事件，再渲染
                    setClickable();
                    mainViewer.render();
                    
                    // 更新信息显示
                    infoDiv.innerHTML = '已恢复原始分子结构。';
                    console.log("Model restored successfully");
                    
                } catch (error) {
                    console.error("Error restoring model:", error);
                    infoDiv.innerHTML = '恢复原始数据时出错，请查看控制台。';
                }
            });
        
            // 初始化 SmilesDrawer
            let smilesDrawer = new SmilesDrawer.Drawer({
                width: 400,
                height: 300,
                bondThickness: 1.5,
                atomVisualization: 'default'
            });
            
            // 加载分子列表
            async function loadMoleculesList() {
                try {
                    const response = await fetch('data/molecules.json');
                    const molecules = await response.json();
                    const listContainer = document.getElementById('molecules-list');
                    
                    molecules.forEach((molecule, index) => {
                        const div = document.createElement('div');
                        div.className = 'molecule-item';
                        
                        // 如果没有名称，显示 SMILES 和序号
                        if (!molecule.name) {
                            // 如果 SMILES 字符串太长，截断它
                            const truncatedSmiles = molecule.smiles.length > 20 
                                ? molecule.smiles.substring(0, 20) + '...' 
                                : molecule.smiles;
                            div.textContent = `#${index + 1}: ${truncatedSmiles}`;
                            
                            // 添加完整 SMILES 作为 title，鼠标悬停时显示
                            div.title = molecule.smiles;
                        } else {
                            div.textContent = molecule.name;
                        }
                        
                        div.dataset.smiles = molecule.smiles;
                        
                        div.addEventListener('click', function() {
                            // 移除其他选中状态
                            document.querySelectorAll('.molecule-item').forEach(item => {
                                item.classList.remove('selected');
                            });
                            
                            // 添加选中状态
                            div.classList.add('selected');
                            
                            // 绘制结构
                            drawStructure(molecule.smiles);
                        });
                        
                        listContainer.appendChild(div);                         
                    });
                    
                    // 默认显示第一个分子
                    if (molecules.length > 0) {
                        listContainer.firstChild.click();
                    }
                    // 在加载完分子列表后设置搜索功能
                    setupSearch();
                } catch (error) {
                    console.error('加载分子列表失败:', error);
                }
            }


            function setupSearch() {
                const searchInput = document.getElementById('molecule-search');
                const searchButton = document.getElementById('search-button');
                const moleculesList = document.getElementById('molecules-list');
                
                function searchMolecule() {
                    const searchValue = parseInt(searchInput.value);
                    if (isNaN(searchValue) || searchValue < 1) {
                        alert('请输入有效的分子序号');
                        return;
                    }
                    
                    const moleculeItems = moleculesList.getElementsByClassName('molecule-item');
                    if (searchValue > moleculeItems.length) {
                        alert(`序号超出范围，最大序号为 ${moleculeItems.length}`);
                        return;
                    }
                    
                    // 找到对应序号的分子项并点击
                    const targetItem = moleculeItems[searchValue - 1];
                    if (targetItem) {
                        // 滚动到目标位置
                        targetItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // 触发点击事件
                        targetItem.click();
                    }
                }
                
                // 添加搜索按钮点击事件
                searchButton.addEventListener('click', searchMolecule);
                
                // 添加回车键搜索功能
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchMolecule();
                    }
                });
            }

            
            // 绘制分子结构
            function drawStructure(smiles) {
                const canvas = document.getElementById('structure2d');
                SmilesDrawer.parse(smiles, function(tree) {
                    smilesDrawer.draw(tree, canvas, 'light', false);
                }, function(error) {
                    console.error('SMILES 解析错误:', error);
                });
            }

            // 在 window.onload 函数中添加
            function setupCentersManagement() {
                const addCenterBtn = document.getElementById('add-center-btn');
                const centersList = document.getElementById('centers-list');
                let centerCount = 0;
                
                function createCenterItem(index) {
                    const centerDiv = document.createElement('div');
                    centerDiv.className = 'center-item';
                    centerDiv.dataset.index = index;
                    
                    // 创建输入部分的容器
                    const inputSection = document.createElement('div');
                    inputSection.className = 'center-input-section';
                    
                    // Center 标签
                    const label = document.createElement('span');
                    label.className = 'center-label';
                    label.textContent = `Center ${index}:`;
                    
                    // 坐标输入框容器
                    const coordInputs = document.createElement('div');
                    coordInputs.className = 'coordinate-inputs';
                    
                    // 创建 x, y, z 输入框
                    ['x', 'y', 'z'].forEach(coord => {
                        const input = document.createElement('input');
                        input.type = 'number';
                        input.step = 'any';
                        input.className = 'coordinate-input';
                        input.placeholder = coord;
                        input.id = `center-${index}-${coord}`;
                        coordInputs.appendChild(input);
                    });
                    
                    // 删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'delete-center-btn';
                    deleteBtn.textContent = '删除';
                    
                    // 创建 3D 查看器容器
                    const viewerContainer = document.createElement('div');
                    viewerContainer.className = 'center-viewer-container';
                    
                    // 创建 3D 查看器
                    const viewerDiv = document.createElement('div');
                    viewerDiv.className = 'center-3d-viewer';
                    viewerDiv.id = `center-viewer-${index}`;
                    
                    // 创建文件输入和加载按钮
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.className = 'protein-file-input';
                    fileInput.id = `protein-file-${index}`;
                    fileInput.accept = '.pdb,.sdf';
                    
                    const loadButton = document.createElement('button');
                    loadButton.className = 'load-protein-btn';
                    loadButton.textContent = '加载蛋白质文件';
                    
                    // 创建控制按钮容器
                    const controls = document.createElement('div');
                    controls.className = 'center-controls';
                    controls.appendChild(loadButton);
                    
                    // 组装界面
                    inputSection.appendChild(label);
                    inputSection.appendChild(coordInputs);
                    inputSection.appendChild(deleteBtn);
                    
                    viewerContainer.appendChild(viewerDiv);
                    viewerContainer.appendChild(controls);
                    viewerContainer.appendChild(fileInput);
                    
                    centerDiv.appendChild(inputSection);
                    centerDiv.appendChild(viewerContainer);
                    

                    // 修改初始化 3Dmol.js 查看器的代码
                    const viewer = $3Dmol.createViewer(viewerDiv, {
                        defaultcolors: $3Dmol.rasmolElementColors,
                        backgroundColor: "black",  // 设置背景色
                        width: 400,  // 明确设置宽度
                        height: 300  // 明确设置高度
                    });

                    // 确保查看器已创建后再添加事件监听
                    viewer.render(function() {
                        // 设置文件加载事件
                        loadButton.addEventListener('click', () => {
                            fileInput.click();
                        });
                        
                        fileInput.addEventListener('change', (e) => {
                            const file = e.target.files[0];
                            if (!file) return;
                            
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                const content = e.target.result;
                                const fileExt = file.name.split('.').pop().toLowerCase();
                                
                                try {
                                    // 清除现有模型
                                    viewer.clear();
                                    
                                    // 加载新模型
                                    const model = viewer.addModel(content, fileExt, {
                                        keepH: true,
                                        assignBonds: false
                                    });
                                    
                                    // 设置卡通样式
                                    viewer.setStyle({}, {
                                        cartoon: {}
                                    });
                                    
                                    // 添加蛋白质表面
                                    viewer.addSurface($3Dmol.SurfaceType.MS, {
                                        opacity: 0.7,
                                        color: 'white'
                                    });
                                    
                                    // 更新视图
                                    viewer.zoomTo();
                                    
                                    // 确保渲染完成
                                    requestAnimationFrame(() => {
                                        viewer.render();
                                    });
                                    
                                    console.log(`Center ${index} viewer loaded model successfully`);
                                    
                                } catch (error) {
                                    console.error(`Center ${index} viewer error:`, error);
                                }
                            };
                            reader.readAsText(file);
                        });
                    });

                    
                    // 设置文件加载事件
                    loadButton.addEventListener('click', () => {
                        fileInput.click();
                    });
                    
                    fileInput.addEventListener('change', (e) => {
                        const file = e.target.files[0];
                        if (!file) return;
                        
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const content = e.target.result;
                            const fileExt = file.name.split('.').pop().toLowerCase();
                            
                            // 清除现有模型
                            viewer.clear();
                            
                            // 加载新模型
                            const model = viewer.addModel(content, fileExt);
                            
                            // 设置卡通样式
                            viewer.setStyle({}, {
                                cartoon: {}  // 使用卡通样式
                            });
                            
                            // 添加蛋白质表面
                            viewer.addSurface($3Dmol.SurfaceType.MS, {
                                opacity: 0.7,
                                color: 'white'
                            });
                            
                            // 更新视图
                            viewer.zoomTo();
                            viewer.render();
                        };
                        reader.readAsText(file);
                    });


                    // 设置删除事件
                    deleteBtn.onclick = () => {
                        // 清除查看器
                        viewer.clear();
                        // 移除整个 center 项
                        centerDiv.remove();
                        updateCenterIndices();
                    };
                    
                    return centerDiv;
                }

                
                function updateCenterIndices() {
                    const centers = centersList.getElementsByClassName('center-item');
                    Array.from(centers).forEach((center, i) => {
                        const newIndex = i + 1;
                        center.dataset.index = newIndex;
                        center.querySelector('.center-label').textContent = `Center ${newIndex}:`;
                        
                        // 更新输入框 ID
                        ['x', 'y', 'z'].forEach(coord => {
                            const input = center.querySelector(`#center-${center.dataset.index}-${coord}`);
                            if (input) {
                                input.id = `center-${newIndex}-${coord}`;
                            }
                        });
                    });
                    centerCount = centers.length;
                }
                
                addCenterBtn.addEventListener('click', () => {
                    centerCount++;
                    const centerItem = createCenterItem(centerCount);
                    centersList.appendChild(centerItem);
                });
            }

            function setupMultiModelViewer() {

            // 等待 DOM 加载完成
            return new Promise((resolve) => {
                // 确保容器存在且清空
                const container = document.getElementById('multi-model-viewer');
                if (!container) {
                    console.error('找不到 multi-model-viewer 容器');
                    return;
                }
                container.innerHTML = '';
                // 创建查看器
                const multiModelViewer = $3Dmol.createViewer(container, {
                    defaultcolors: $3Dmol.rasmolElementColors,
                    backgroundColor: 'black'
                });

                // 确保查看器完全初始化
                multiModelViewer.render(() => {
                    console.log('多文件查看器初始化完成');
                    setupMultiModelControls(multiModelViewer);
                    resolve(multiModelViewer);
                });
            });

            }

            // 将控制逻辑分离到单独的函数
            function setupMultiModelControls(multiModelViewer) {
                const loadButton = document.getElementById('loadMultiModelButton');
                const fileInput = document.getElementById('multiModelFileInput');
                const modelsList = document.getElementById('multi-model-list');

                // 添加药效团加载相关功能
                const pharmacophoreButton = document.getElementById('loadPharmacophoreButton');
                const pharmacophoreInput = document.getElementById('pharmacophoreFileInput');
                
                console.log('初始化药效团按钮:', pharmacophoreButton);
                console.log('初始化药效团文件输入:', pharmacophoreInput);     


                // 为 multi-model viewer 创建独立的状态管理
                const multiModelState = {
                    addedSpheres: [],
                    addedLabels: [],
                    clickedAtoms: [],
                    selectedModels: new Map(),
                    pharmacophores: new Map()
                };

                // 药效团
                pharmacophoreButton.addEventListener('click', () => {
                    console.log('点击加载药效团按钮');
                    pharmacophoreInput.click();
                });

                pharmacophoreInput.addEventListener('change', async (e) => {
                    const file = e.target.files[0];
                    if (!file) return;

                    try {
                        const content = await readFileAsync(file);
                        console.log('读取文件内容:', content.substring(0, 100) + '...');
                        const pharmacophores = JSON.parse(content);
                        addPharmacophoreToList(pharmacophores);
                    } catch (error) {
                        console.error('加载药效团文件失败:', error);
                    }
                });

                function addPharmacophoreToList(pharmacophores) {
                    const modelGroup = document.createElement('div');
                    modelGroup.className = 'model-group';
                    
                    const modelHeader = document.createElement('div');
                    modelHeader.className = 'model-header';
                    modelHeader.innerHTML = `
                        <span>药效团</span>
                        <span>(${Object.keys(pharmacophores).length} 类)</span>
                    `;

                    const pharmacophoreList = document.createElement('div');
                    pharmacophoreList.className = 'model-molecules';

                    // 遍历每种药效团类型
                    Object.entries(pharmacophores).forEach(([type, data]) => {
                        const item = document.createElement('div');
                        item.className = 'molecule-item';
                        
                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.className = 'molecule-checkbox';
                        
                        const label = document.createElement('span');
                        label.textContent = `${type} (${data.coords.length})`;
                        if (data.color) {
                            label.style.color = data.color;
                        }

                        item.appendChild(checkbox);
                        item.appendChild(label);

                        checkbox.addEventListener('change', () => {
                            if (checkbox.checked) {
                                // 显示该类药效团的所有球体
                                data.coords.forEach((pos, index) => {
                                    const sphere = multiModelViewer.addSphere({
                                        center: { x: pos[0], y: pos[1], z: pos[2] },
                                        radius: 0.8, // 可以根据需要调整球体大小
                                        color: data.color,
                                        opacity: 0.7
                                    });
                                    if (!multiModelState.pharmacophores) {
                                        multiModelState.pharmacophores = new Map();
                                    }
                                    if (!multiModelState.pharmacophores.has(type)) {
                                        multiModelState.pharmacophores.set(type, []);
                                    }
                                    multiModelState.pharmacophores.get(type).push(sphere);
                                });
                            } else {
                                // 移除该类药效团的所有球体
                                if (multiModelState.pharmacophores && multiModelState.pharmacophores.has(type)) {
                                    multiModelState.pharmacophores.get(type).forEach(sphere => {
                                        multiModelViewer.removeShape(sphere);
                                    });
                                    multiModelState.pharmacophores.delete(type);
                                }
                            }
                            multiModelViewer.render();
                        });

                        pharmacophoreList.appendChild(item);
                    });

                    modelGroup.appendChild(modelHeader);
                    modelGroup.appendChild(pharmacophoreList);
                    
                    const modelsList = document.getElementById('multi-model-list');
                    modelsList.appendChild(modelGroup);
                }

                // 添加多文件查看器的点击处理功能
                function setMultiModelClickable() {
                    if (!multiModelViewer) return;
                    multiModelViewer.setClickable({}, false);
                    multiModelViewer.setClickable({}, true, function(atom, viewer, event, container) {
                        if (!atom) return;

                        // 1. 准备原子信息文本
                        let atomInfo = `原子编号: ${atom.serial}\n`;
                        atomInfo += `元素: ${atom.elem}\n`;
                        atomInfo += `坐标: (${atom.x.toFixed(2)}, ${atom.y.toFixed(2)}, ${atom.z.toFixed(2)})\n`;
                        
                        // 如果是氨基酸的一部分，添加氨基酸信息
                        if (atom.resn) {
                            atomInfo += `氨基酸: ${atom.resn}${atom.resi}\n`;
                            atomInfo += `链: ${atom.chain}\n`;
                            if (atom.atom) {
                                atomInfo += `原子名称: ${atom.atom}`;
                            }
                        }

                        // 更新 info 区域显示
                        document.getElementById('n1info').innerHTML = atomInfo.replace(/\n/g, '<br>');

                        // 2. 处理点击标记
                        const atomIndex = atom.serial;
                        const existingIndex = multiModelState.clickedAtoms.indexOf(atom.index);
                        
                        if (existingIndex !== -1) {
                            // 取消选择原子
                            multiModelViewer.removeShape(multiModelState.addedSpheres[existingIndex]);
                            multiModelViewer.removeLabel(multiModelState.addedLabels[existingIndex]);
                            
                            multiModelState.addedSpheres.splice(existingIndex, 1);
                            multiModelState.addedLabels.splice(existingIndex, 1);
                            multiModelState.clickedAtoms.splice(existingIndex, 1);
                        } else {

                            const label = multiModelViewer.addLabel(`${atomIndex}`, {
                                position: { x: atom.x, y: atom.y, z: atom.z },
                                backgroundColor: 'transparent',
                                fontColor: 'white',
                                fontSize: 13,
                                inFront: true,          // 确保标签在前面
                                showBackground: false,
                                font: 'Arial',          // 使用 Arial 字体
                            });
                            
                            const sphere = multiModelViewer.addSphere({
                                center: { x: atom.x, y: atom.y, z: atom.z },
                                radius: 0.6,
                                color: '#33FF33',
                                opacity: 0.7
                            });
                            
                            multiModelState.addedLabels.push(label);
                            multiModelState.addedSpheres.push(sphere);
                            multiModelState.clickedAtoms.push(atom.index);
                        }
                        
                        multiModelViewer.render();
                    });                 

                }


                // 创建残基项
                function createResidueItem(residue, selection) {
                    try {
                        // 查找当前模型对应的组
                        const modelGroups = document.querySelectorAll('.model-group');
                        let targetGroup = null;
                        
                        // 遍历所有模型组找到匹配的
                        for (const group of modelGroups) {
                            const molecules = group.querySelector('.model-molecules');
                            const molItems = molecules.querySelectorAll('.molecule-checkbox:checked');
                            
                            for (const item of molItems) {
                                const molItem = item.closest('.molecule-item');
                                if (molItem) {
                                    targetGroup = group;
                                    break;
                                }
                            }
                            if (targetGroup) break;
                        }

                        if (!targetGroup) {
                            console.log('未找到对应的模型组');
                            return;
                        }

                        // 创建或获取残基列表容器
                        let residuesList = targetGroup.querySelector('.residues-list');
                        if (!residuesList) {
                            residuesList = document.createElement('div');
                            residuesList.className = 'residues-list';
                            targetGroup.appendChild(residuesList);
                        }

                        // 创建残基项
                        const residueItem = document.createElement('div');
                        residueItem.className = 'residue-item';
                        residueItem.innerHTML = `
                            <span>${residue.name}${residue.resi} (Chain ${residue.chain})</span>
                            <div class="residue-style-controls">
                                <button data-style="stick">Stick</button>
                                <button data-style="cartoon">Cartoon</button>
                                <button data-style="sphere">Sphere</button>
                            </div>
                        `;

                        // 添加样式按钮事件
                        residueItem.querySelectorAll('button').forEach(btn => {
                            btn.addEventListener('click', () => {
                                updateResidueStyle(selection, btn.dataset.style);
                            });
                        });

                        residuesList.appendChild(residueItem);
                        console.log('成功创建残基项:', residue);
                    } catch (error) {
                        console.error('创建残基项时出错:', error);
                    }

                }

                // 修改 highlightResidue 函数
                function highlightResidue(selection) {
                    try {
                        multiModelViewer.setStyle({}, {
                            cartoon: {
                                color: 'lightgray',
                                opacity: 0.5
                            }
                        });
                        
                        // 高亮选中的残基
                        multiModelViewer.setStyle(selection, {
                            cartoon: {
                                color: 'red',
                                opacity: 1.0
                            }
                        });
                        
                        multiModelViewer.render();
                    } catch (error) {
                        console.error('高亮残基时出错:', error);
                    }
                }

                // 修改 updateResidueStyle 函数
                function updateResidueStyle(selection, style) {
                    try {
                        const styleConfig = {};
                        switch (style) {
                            case 'stick':
                                styleConfig.stick = {
                                    radius: 0.2,
                                    opacity: 0.4,
                                    colorscheme: 'Jmol'
                                };
                                break;
                            case 'cartoon':
                                styleConfig.cartoon = {
                                    color: 'spectrum'
                                };
                                break;
                            case 'sphere':
                                styleConfig.sphere = {
                                    radius: 0.3,
                                    colorscheme: 'Jmol'
                                };
                                break;
                        }
                        
                        multiModelViewer.setStyle(selection, styleConfig);
                        multiModelViewer.render();
                    } catch (error) {
                        console.error('更新残基样式时出错:', error);
                    }
                }

                // 添加键盘事件监听 (使用事件委托)
                document.addEventListener('keydown', function(event) {
                    // 仅当焦点在多文件查看器区域时响应
                    const multiModelContainer = document.getElementById('multi-model-container');
                    if (event.target.closest('#multi-model-container')) {
                        if (event.key === 'r') {
                            if (multiModelState.addedSpheres.length === 0) return;
                            
                            // 移除所有标记
                            multiModelState.addedSpheres.forEach(sphere => multiModelViewer.removeShape(sphere));
                            multiModelState.addedLabels.forEach(label => multiModelViewer.removeLabel(label));
                            
                            multiModelState.addedSpheres = [];
                            multiModelState.addedLabels = [];
                            multiModelState.clickedAtoms = [];
                            
                            multiModelViewer.render();
                        }
                    }
                });


                // 添加选中状态记录
                const selectedModels = new Map(); // 用于记录选中的模型

                // 添加 readFileAsync 函数定义
                function readFileAsync(file) {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = (e) => resolve(e.target.result);
                        reader.onerror = (error) => reject(error);
                        reader.readAsText(file);
                    });
                }

                // 添加按钮点击事件
                loadButton.addEventListener('click', () => {
                    fileInput.click();
                });


                // 添加 parseSDF 函数定义
                function parseSDF(content) {
                    const molecules = [];
                    const molBlocks = content.split('$$$$\n').filter(block => block.trim());
                    
                    molBlocks.forEach((block, index) => {
                        try {
                            // 解析每个分子的数据
                            const lines = block.trim().split('\n');
                            let atomCount = 0;
                            let bondCount = 0;
                            
                            // 查找原子和键的数量
                            for (let i = 0; i < lines.length; i++) {
                                const matches = lines[i].trim().match(/^\s*(\d+)\s+(\d+)/);
                                if (matches) {
                                    atomCount = parseInt(matches[1]);
                                    bondCount = parseInt(matches[2]);
                                    break;
                                }
                            }
                            
                            // 存储分子信息
                            molecules.push({
                                id: index + 1,
                                data: block + '$$$$\n',
                                atomCount: atomCount,
                                bondCount: bondCount
                            });
                            
                            console.log(`解析分子 ${index + 1}:`, {
                                atomCount: atomCount,
                                bondCount: bondCount
                            });
                            
                        } catch (error) {
                            console.error(`解析分子 ${index + 1} 时出错:`, error);
                            // 即使出错也添加分子，但使用默认值
                            molecules.push({
                                id: index + 1,
                                data: block + '$$$$\n',
                                atomCount: 0,
                                bondCount: 0
                            });
                        }
                    });
                    
                    console.log(`共解析 ${molecules.length} 个分子`);
                    return molecules;
                }
                
                fileInput.addEventListener('change', async (e) => {
                    const files = e.target.files;
                    for (let file of files) {
                        try {
                            const content = await readFileAsync(file);
                            const fileExt = file.name.split('.').pop().toLowerCase();
                            
                            if (fileExt === 'sdf') {
                                const molecules = parseSDF(content);
                                addModelToList({
                                    name: file.name,
                                    type: 'sdf',
                                    molecules: molecules
                                });
                            } else {
                                addModelToList({
                                    name: file.name,
                                    type: fileExt,
                                    content: content,
                                    molecules: [{
                                        id: 1,
                                        data: content
                                    }]
                                });
                            }
                        } catch (error) {
                            console.error(`处理文件 ${file.name} 时出错:`, error);
                        }
                    }
                });
                
                function addModelToList(model) {
                    const modelGroup = document.createElement('div');
                    modelGroup.className = 'model-group';
                    
                    const modelHeader = document.createElement('div');
                    modelHeader.className = 'model-header';
                    modelHeader.innerHTML = `
                        <span>${model.name}</span>
                        <span>${model.molecules.length > 1 ? `(${model.molecules.length} 分子)` : ''}</span>
                    `;
                    const styleControls = document.createElement('div');
                    styleControls.className = 'style-controls';
                    styleControls.innerHTML = `
                        <button class="style-button active" data-style="stick">Stick</button>
                        <button class="style-button" data-style="cartoon">Cartoon</button>
                        <button class="style-button" data-style="sphere">Sphere</button>
                    `;
                    const moleculesList = document.createElement('div');
                    moleculesList.className = 'model-molecules';
                    
                    model.molecules.forEach((mol, index) => {
                        const molItem = document.createElement('div');
                        molItem.className = 'molecule-item';
                        molItem.textContent = model.molecules.length > 1 ? 
                            `分子 ${mol.id}` : model.name;

                        // 添加复选框
                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.className = 'molecule-checkbox';
                        molItem.insertBefore(checkbox, molItem.firstChild);

                        // 添加复选框事件
                        checkbox.addEventListener('change', () => {
                            if (checkbox.checked) {
                                multiModelState.selectedModels.set(`${model.name}_${mol.id}`, {
                                model: model,
                                molecule: mol,
                                style: 'stick'
                            });                                
                            } else {
                                multiModelState.selectedModels.delete(`${model.name}_${mol.id}`);
                            }
                            updateMultiModelDisplay();
                        });
                        moleculesList.appendChild(molItem);
                    });
            
                    // 更新样式按钮状态和事件
                    const styleButtons = styleControls.querySelectorAll('.style-button');
                    styleButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        styleButtons.forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        const style = e.target.dataset.style;
                        // 更新所选分子的样式
                        for (let [key, data] of multiModelState.selectedModels) {
                            data.style = style;
                        }
                        updateMultiModelDisplay();
                        });
                    });
                // 7. 按顺序将所有元素添加到主容器
                modelGroup.appendChild(modelHeader);
                modelGroup.appendChild(styleControls);
                modelGroup.appendChild(moleculesList);

                // 8. 将主容器添加到模型列表
                const modelsList = document.getElementById('multi-model-list');
                modelsList.appendChild(modelGroup);


            }

                // 更新显示函数
                function updateMultiModelDisplay() {

                    // 1. 保存当前药效团状态和勾选状态
                    const savedPharmacophores = new Map();
                    const checkedTypes = new Set();

                    // 保存已勾选的药效团类型
                    document.querySelectorAll('.molecule-item input[type="checkbox"]:checked').forEach(checkbox => {
                        const label = checkbox.nextElementSibling;
                        if (label && label.textContent) {
                            const type = label.textContent.split(' (')[0];
                            checkedTypes.add(type);
                        }
                    });

                    // 保存药效团数据
                    for (let [type, spheres] of multiModelState.pharmacophores.entries()) {
                        savedPharmacophores.set(type, {
                            spheres: spheres.map(sphere => ({
                                center: sphere.center,
                                color: sphere.color,
                                radius: sphere.radius,
                                opacity: sphere.opacity
                            }))
                        });
                    }

                    // 1. 清除当前显示
                    multiModelViewer.clear();

                    // 2. 重置标记状态
                    multiModelState.addedSpheres = [];
                    multiModelState.addedLabels = [];
                    multiModelState.clickedAtoms = [];
                    
                    // 显示所有选中的分子
                    for (let [key, data] of multiModelState.selectedModels) {
                        const newModel = multiModelViewer.addModel(
                            data.molecule.data, 
                            data.model.type, 
                            {
                                keepH: true,
                                assignBonds: data.model.type === 'pdb'
                            }
                        );
                        
                        // 设置基本样式
                        if (data.model.type === 'pdb') {

                            multiModelViewer.setStyle({model: newModel}, {
                            cartoon: {
                                color: 'gray',
                                opacity: 1.0
                            },
                            stick: {
                                radius: 0.15,
                                opacity: 0.8,  // 改为可见
                                colorscheme: 'Jmol'
                            },
                            sphere: {
                                radius: 0.2,
                                opacity: 0.8,
                                colorscheme: 'Jmol'
                            }
                        });

                        } else {
                            multiModelViewer.setStyle({model: newModel}, {
                                stick: {
                                    radius: 0.2,
                                    opacity: 0.9,
                                    colorscheme: 'yellowCarbon'
                                },
                                sphere: {
                                    radius: 0.4,
                                    colorscheme: 'yellowCarbon'
                                }
                            });
                        }

                        // 恢复之前的样式设置
                        // if (savedState.styles[key]) {
                        //     multiModelViewer.setStyle(
                        //         savedState.styles[key].selection,
                        //         savedState.styles[key].style
                        //     );
                        // }
                    }

                    // 5. 恢复药效团显示
                    if (multiModelState.pharmacophores) {
                        for (let [type, spheres] of multiModelState.pharmacophores) {
                            const savedSpheres = spheres.map(sphere => ({
                                center: sphere.center,
                                color: sphere.color,
                                radius: sphere.radius,
                                opacity: sphere.opacity
                            }));
                            savedPharmacophores.set(type, { spheres: savedSpheres });
                        }
                    }

                    // 清除现有药效团
                    multiModelState.pharmacophores.clear();

                    // 重新添加药效团
                    for (let [type, data] of savedPharmacophores.entries()) {
                        const spheres = data.spheres.map(sphereData => {
                            return multiModelViewer.addSphere({
                                center: sphereData.center,
                                radius: sphereData.radius,
                                color: sphereData.color,
                                opacity: sphereData.opacity,
                            });
                        });
                        multiModelState.pharmacophores.set(type, spheres);
                    }

                    
                    multiModelViewer.zoomTo();
                    setMultiModelClickable();

                    // 使用 requestAnimationFrame 确保渲染完成
                    requestAnimationFrame(() => {
                        multiModelViewer.render();
                        console.log('渲染完成，当前模型:', multiModelViewer.getModel());
                    });

                }
                    
                // 修改样式更新函数
                function updateStyle(multiModelViewer, style, modelObj) {
                    const styleConfig = {};
                    switch (style) {
                        case 'stick':
                            styleConfig.stick = {
                                radius: 0.2,
                                opacity: 0.9,
                                colorscheme: 'Jmol'  // 移除对 model.type 的引用
                            };
                            styleConfig.sphere = {   // 添加球体样式
                                radius: 0.3,        // 增加球体半径到 0.5
                                colorscheme: 'Jmol'
                            };
                            break;
                        case 'cartoon':
                            styleConfig.cartoon = {
                                color: 'spectrum'
                            };
                            break;
                        case 'sphere':
                            styleConfig.sphere = {
                                radius: 0.3,
                                colorscheme: 'Jmol'  // 移除对 model.type 的引用
                            };
                            break;
                    }
                    
                    // 只更新特定模型的样式
                    if (modelObj) {
                        multiModelViewer.setStyle({model: modelObj}, styleConfig);
                    } else {
                        multiModelViewer.setStyle({}, styleConfig);
                    }
                    multiModelViewer.render();              
            }


                // if (model.molecules.length > 1) {
                //     modelHeader.addEventListener('click', () => {
                //         moleculesList.classList.toggle('show');
                //     });
                // }

                // if (model.molecules.length === 1) {
                //     displayMolecule(model, model.molecules[0], multiModelViewer);
                // }

                // // 如果只有一个分子，直接显示
                // if (model.molecules.length === 1) {
                //     displayMolecule(model, model.molecules[0], multiModelViewer);
                // }
                
                function displayMolecule(model, molecule, multiModelViewer) {
                    multiModelViewer.clear();
                    const newModel = multiModelViewer.addModel(molecule.data, model.type, {
                        keepH: true,
                        assignBonds: model.type === 'pdb'
                    });

                    // 根据文件类型设置默认样式
                    if (model.type === 'pdb') {
                        // 蛋白质默认使用 cartoon 样式
                        multiModelViewer.setStyle({model: newModel}, {
                            cartoon: {
                                color: 'spectrum' // 使用光谱颜色
                            },
                            line: {
                                hidden: true
                            }
                        });
                        // 可选：添加蛋白质表面
                        multiModelViewer.addSurface($3Dmol.SurfaceType.MS, {
                            opacity: 0.3,
                            color: 'white'
                        });
                    } else {
                        // 小分子默认使用 stick 样式
                        multiModelViewer.setStyle({model: newModel}, {
                            stick: {
                                radius: 0.15,
                                opacity: 0.9,
                                colorscheme: 'Jmol'
                            },
                            sphere: {
                                radius: 0.35,
                                colorscheme: 'Jmol'
                            }
                        });
                    }

                    multiModelViewer.zoomTo();
                    multiModelViewer.render();


                }
                
                
                return multiModelViewer;
            }

            // // 加载分子列表
            // loadMoleculesList();
            // setupCentersManagement();
            // setupMultiModelViewer();

        };
    </script>
</body>

</html>