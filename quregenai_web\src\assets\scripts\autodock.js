/**
 * AutoDock 页面的核心JS功能
 * 包括: 分子查看器、文件上传、原子选择和表单控制
 */

// 全局变量定义
let viewer = null;
let uploadedFiles = [];
let selectedAtoms = [];

/**
 * 初始化3DMol分子查看器
 */
function initViewer() {
  // 配置3DMol查看器
  viewer = $3Dmol.createViewer($("#viewer-container"), {
    backgroundColor: "black",
    defaultcolors: $3Dmol.rasmolElementColors
  });

  // 添加拖拽上传功能
  const viewerContainer = document.getElementById('viewer-container');
  viewerContainer.addEventListener('dragover', handleDragOver);
  viewerContainer.addEventListener('drop', handleFileDrop);
}

/**
 * 处理文件拖拽到查看器上
 */
function handleDragOver(e) {
  e.stopPropagation();
  e.preventDefault();
  e.dataTransfer.dropEffect = 'copy';
}

/**
 * 处理拖拽文件的放置
 */
function handleFileDrop(e) {
  e.stopPropagation();
  e.preventDefault();
  const files = e.dataTransfer.files;
  handleFileUpload(files);
}

/**
 * 处理文件上传
 */
function handleFileUpload(files) {
  showLoadingIndicator();
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const fileName = file.name;
    const fileExt = fileName.split('.').pop().toLowerCase();
    
    // 检查文件类型
    if (!['pdb', 'sdf', 'pdbqt', 'mol2'].includes(fileExt)) {
      showError(`不支持的文件格式: ${fileName}. 仅支持PDB, SDF, PDBQT和MOL2格式`);
      continue;
    }
    
    // 读取文件内容
    const reader = new FileReader();
    reader.onload = function(e) {
      const fileContent = e.target.result;
      
      try {
        // 根据不同文件类型添加到查看器
        addMoleculeToViewer(fileContent, fileName, fileExt);
        // 将文件添加到已上传列表
        addFileToList(fileName, fileExt, fileContent);
        
        // 更新文件选择区域
        updateFileSelectors();
        
      } catch (error) {
        showError(`无法加载文件 ${fileName}: ${error.message}`);
      }
    };
    
    reader.onerror = function() {
      showError(`读取文件 ${fileName} 时发生错误`);
    };
    
    reader.readAsText(file);
  }
  
  hideLoadingIndicator();
}

/**
 * 将分子添加到3DMol查看器
 */
function addMoleculeToViewer(fileContent, fileName, fileExt) {
  let modelType = '';
  let renderStyle = {};

  // 根据文件类型确定渲染方式
  switch (fileExt) {
    case 'pdb':
      modelType = 'pdb';
      renderStyle = { cartoon: { color: 'spectrum' } };
      break;
    case 'pdbqt':
      modelType = 'pdb';
      renderStyle = { cartoon: { color: 'spectrum' } };
      break;
    case 'sdf':
      modelType = 'sdf';
      renderStyle = { stick: {} };
      break;
    case 'mol2':
      modelType = 'mol2';
      renderStyle = { stick: {} };
      break;
    default:
      throw new Error(`不支持的文件格式: ${fileExt}`);
  }

  // 添加到查看器
  let model = viewer.addModel(fileContent, modelType, {keepH: true});
  model.setStyle({}, renderStyle);

  // 保存文件信息
  uploadedFiles.push({
    name: fileName,
    content: fileContent,
    model: model,
    modelId: model.getID(),
    type: fileExt,
    // 为蛋白质文件设置类型
    isProtein: fileExt === 'pdb' || fileExt === 'pdbqt'
  });

  // 重新渲染查看器
  viewer.zoomTo();
  viewer.render();

  // 设置点击事件
  attachClickHandlers();
}

/**
 * 添加文件到上传列表
 */
function addFileToList(fileName, fileType, content) {
  const fileList = document.getElementById('file-list-container');
  
  // 清除"暂无文件"的消息
  if (fileList.querySelector('p')) {
    fileList.innerHTML = '<h3>已上传文件</h3>';
  }
  
  // 添加文件列表如果不存在
  let fileListUl = fileList.querySelector('ul');
  if (!fileListUl) {
    fileListUl = document.createElement('ul');
    fileList.appendChild(fileListUl);
  }
  
  // 创建文件列表项
  const listItem = document.createElement('li');
  listItem.innerHTML = `
    <label>
      <input type="checkbox" class="file-check" data-filename="${fileName}">
      ${fileName} <small>(${fileType})</small>
    </label>
  `;
  fileListUl.appendChild(listItem);
}

/**
 * 将点击事件附加到查看器
 */
function attachClickHandlers() {
  viewer.setClickable({}, true);
  viewer.setHoverable({}, true);
  viewer.enableHover(true);

  // 设置点击事件
  viewer.linkViewer(viewer);
  viewer.removeAllSurfaces();
  viewer.mapAtomProperties($3Dmol.applyPartialCharges);
  
  viewer.clicked = function(picked) {
    if (picked === null || !picked.atom || !picked.model) {
      return;
    }
    
    // 高亮显示选中的原子
    const model = viewer.getModel(picked.model);
    const atom = picked.atom;
    const atomId = atom.serial;
    
    // 查找相关文件
    let fileInfo = null;
    for (const file of uploadedFiles) {
      if (file.modelId === picked.model) {
        fileInfo = file;
        break;
      }
    }
    
    if (!fileInfo) {
      return;
    }
    
    // 添加原子到选择列表
    addSelectedAtom(atom, fileInfo.name);
  };
}

/**
 * 添加选中的原子到列表
 */
function addSelectedAtom(atom, fileName) {
  // 检查是否已经选择了这个原子
  const existingAtom = selectedAtoms.find(a => 
    a.serial === atom.serial && a.fileName === fileName
  );
  
  if (existingAtom) {
    return; // 如果已经选择了这个原子，则不再添加
  }
  
  // 创建原子信息对象
  const atomInfo = {
    ...atom,
    fileName: fileName
  };
  
  // 添加到选中原子数组
  selectedAtoms.push(atomInfo);
  
  // 更新UI
  updateAtomInfoList();
  
  // 如果有原子被选择，启用填充按钮
  if (selectedAtoms.length > 0) {
    document.getElementById('auto-fill-coords').removeAttribute('disabled');
  }
}

/**
 * 更新原子信息列表
 */
function updateAtomInfoList() {
  const atomInfoList = document.getElementById('atom-info-list');
  
  if (selectedAtoms.length === 0) {
    atomInfoList.innerHTML = '<p class="no-atoms-message">暂无选择的原子</p>';
    document.getElementById('clear-atoms-btn').setAttribute('disabled', 'disabled');
    return;
  }
  
  // 启用清除按钮
  document.getElementById('clear-atoms-btn').removeAttribute('disabled');
  
  // 清空列表
  atomInfoList.innerHTML = '';
  
  // 创建原子项
  selectedAtoms.forEach((atom, index) => {
    const element = atom.elem || '未知';
    const chainId = atom.chain || '-';
    const resname = atom.resn || '未知';
    const resNum = atom.resi || '-';
    const atomName = atom.atom || atom.name || '未知';
    const serial = atom.serial || index + 1;
    
    const atomItem = document.createElement('div');
    atomItem.className = 'atom-item';
    atomItem.dataset.index = index;
    atomItem.innerHTML = `
      <div class="atom-item-header">
        <span class="atom-serial">原子 #${serial}</span>
        <span class="atom-file">${atom.fileName}</span>
      </div>
      <div class="atom-details">
        <span class="atom-detail">
          <span class="atom-detail-label">元素:</span>
          <span class="atom-detail-value">${element}</span>
        </span>
        <span class="atom-detail">
          <span class="atom-detail-label">残基:</span>
          <span class="atom-detail-value">${resname} ${resNum}</span>
        </span>
        <span class="atom-detail">
          <span class="atom-detail-label">链:</span>
          <span class="atom-detail-value">${chainId}</span>
        </span>
        <span class="atom-detail">
          <span class="atom-detail-label">原子名:</span>
          <span class="atom-detail-value">${atomName}</span>
        </span>
      </div>
      <div class="atom-coordinates">
        X: ${atom.x.toFixed(3)}, Y: ${atom.y.toFixed(3)}, Z: ${atom.z.toFixed(3)}
      </div>
    `;
    
    // 添加点击事件，点击时填充坐标
    atomItem.addEventListener('click', () => {
      document.getElementById('pocket-x').value = atom.x.toFixed(3);
      document.getElementById('pocket-y').value = atom.y.toFixed(3);
      document.getElementById('pocket-z').value = atom.z.toFixed(3);
      
      // 添加高亮样式
      document.querySelectorAll('.atom-item').forEach(item => {
        item.classList.remove('selected-for-autodock');
      });
      atomItem.classList.add('selected-for-autodock');
    });
    
    atomInfoList.appendChild(atomItem);
  });
}

/**
 * 清除所有选择的原子
 */
function clearSelectedAtoms() {
  selectedAtoms = [];
  updateAtomInfoList();
  document.getElementById('auto-fill-coords').setAttribute('disabled', 'disabled');
}

/**
 * 更新文件选择器
 */
function updateFileSelectors() {
  // 更新蛋白质文件选择器
  const proteinGrid = document.getElementById('protein-files-grid');
  if (proteinGrid) {
    proteinGrid.innerHTML = '';
    
    const proteinFiles = uploadedFiles.filter(file => file.isProtein);
    
    if (proteinFiles.length === 0) {
      proteinGrid.innerHTML = `
        <div class="no-files-message">
          暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件
        </div>
      `;
    } else {
      proteinFiles.forEach(file => {
        const fileCard = document.createElement('div');
        fileCard.className = 'file-card';
        fileCard.dataset.filename = file.name;
        fileCard.innerHTML = `
          <div class="file-card-header">
            <div class="file-type-icon">🧬</div>
            <div class="file-name">${file.name}</div>
          </div>
          <div class="file-info">${file.type.toUpperCase()}</div>
          <div class="selection-indicator">✓</div>
        `;
        
        // 点击选择文件
        fileCard.addEventListener('click', () => {
          document.querySelectorAll('#protein-files-grid .file-card').forEach(card => {
            card.classList.remove('selected');
          });
          fileCard.classList.add('selected');
          
          // 更新已选蛋白质显示区域
          const selectedProteinDisplay = document.getElementById('selected-protein-display');
          const selectedProteinInfo = document.getElementById('selected-protein-info');
          
          selectedProteinDisplay.style.display = 'block';
          selectedProteinInfo.innerHTML = `
            <div class="selected-file-item">
              <span class="file-name">${file.name}</span>
              <button type="button" class="remove-file-btn" title="移除选择">×</button>
            </div>
          `;
          
          // 添加移除按钮事件
          selectedProteinInfo.querySelector('.remove-file-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            selectedProteinDisplay.style.display = 'none';
            fileCard.classList.remove('selected');
          });
        });
        
        proteinGrid.appendChild(fileCard);
      });
    }
  }
  
  // 更新配体文件选择器
  const ligandGrid = document.getElementById('uploaded-files-grid');
  if (ligandGrid) {
    ligandGrid.innerHTML = '';
    
    // 显示所有文件，因为任何类型都可能是配体
    if (uploadedFiles.length === 0) {
      ligandGrid.innerHTML = `
        <div class="no-files-message">
          暂无已上传的分子文件，请先在上方分子查看器中上传文件
        </div>
      `;
    } else {
      uploadedFiles.forEach(file => {
        const fileCard = document.createElement('div');
        fileCard.className = 'file-card';
        fileCard.dataset.filename = file.name;
        fileCard.innerHTML = `
          <div class="file-card-header">
            <div class="file-type-icon">${file.isProtein ? '🧬' : '💊'}</div>
            <div class="file-name">${file.name}</div>
          </div>
          <div class="file-info">${file.type.toUpperCase()}</div>
          <div class="selection-indicator">✓</div>
        `;
        
        // 点击选择文件
        fileCard.addEventListener('click', () => {
          fileCard.classList.toggle('selected');
          updateSelectedFilesDisplay();
        });
        
        ligandGrid.appendChild(fileCard);
      });
    }
  }
}

/**
 * 更新已选择的文件显示区域
 */
function updateSelectedFilesDisplay() {
  const selectedCards = document.querySelectorAll('#uploaded-files-grid .file-card.selected');
  const selectedFiles = Array.from(selectedCards).map(card => card.dataset.filename);
  const selectedFilesDisplay = document.getElementById('selected-files-display');
  const selectedFilesList = document.getElementById('selected-files-list');
  const selectedFilesCount = document.getElementById('selected-files-count');
  
  if (selectedFiles.length === 0) {
    selectedFilesDisplay.style.display = 'none';
    return;
  }
  
  selectedFilesDisplay.style.display = 'block';
  selectedFilesCount.textContent = selectedFiles.length;
  selectedFilesList.innerHTML = '';
  
  selectedFiles.forEach(fileName => {
    const fileItem = document.createElement('div');
    fileItem.className = 'selected-file-item';
    fileItem.innerHTML = `
      <span class="file-name">${fileName}</span>
      <button type="button" class="remove-file-btn" title="移除选择" data-filename="${fileName}">×</button>
    `;
    
    fileItem.querySelector('.remove-file-btn').addEventListener('click', (e) => {
      e.stopPropagation();
      const fileNameToRemove = e.target.dataset.filename;
      document.querySelectorAll(`#uploaded-files-grid .file-card[data-filename="${fileNameToRemove}"]`).forEach(card => {
        card.classList.remove('selected');
      });
      updateSelectedFilesDisplay();
    });
    
    selectedFilesList.appendChild(fileItem);
  });
}

/**
 * 清除所有选择的配体文件
 */
function clearLigandSelection() {
  document.querySelectorAll('#uploaded-files-grid .file-card').forEach(card => {
    card.classList.remove('selected');
  });
  document.getElementById('selected-files-display').style.display = 'none';
}

/**
 * 自动填充口袋中心坐标
 */
function autoFillCoordinates() {
  if (selectedAtoms.length === 0) {
    return;
  }
  
  // 如果只有一个原子，直接使用其坐标
  if (selectedAtoms.length === 1) {
    const atom = selectedAtoms[0];
    document.getElementById('pocket-x').value = atom.x.toFixed(3);
    document.getElementById('pocket-y').value = atom.y.toFixed(3);
    document.getElementById('pocket-z').value = atom.z.toFixed(3);
    return;
  }
  
  // 如果有多个原子，计算几何中心
  let sumX = 0, sumY = 0, sumZ = 0;
  selectedAtoms.forEach(atom => {
    sumX += atom.x;
    sumY += atom.y;
    sumZ += atom.z;
  });
  
  const centerX = sumX / selectedAtoms.length;
  const centerY = sumY / selectedAtoms.length;
  const centerZ = sumZ / selectedAtoms.length;
  
  document.getElementById('pocket-x').value = centerX.toFixed(3);
  document.getElementById('pocket-y').value = centerY.toFixed(3);
  document.getElementById('pocket-z').value = centerZ.toFixed(3);
}

/**
 * 显示错误信息
 */
function showError(message) {
  const errorMessage = document.getElementById('error-message');
  errorMessage.textContent = message;
  errorMessage.style.display = 'block';
  
  // 5秒后自动隐藏
  setTimeout(() => {
    errorMessage.style.display = 'none';
  }, 5000);
}

/**
 * 显示加载指示器
 */
function showLoadingIndicator() {
  document.getElementById('loading-indicator').style.display = 'block';
}

/**
 * 隐藏加载指示器
 */
function hideLoadingIndicator() {
  document.getElementById('loading-indicator').style.display = 'none';
}

/**
 * 重置表单输入
 */
function resetForm() {
  document.getElementById('pocket-x').value = '';
  document.getElementById('pocket-y').value = '';
  document.getElementById('pocket-z').value = '';
  document.getElementById('size-x').value = '15';
  document.getElementById('size-y').value = '15';
  document.getElementById('size-z').value = '15';
  document.getElementById('threads').value = '2000';
  
  // 清除已选择的文件
  clearLigandSelection();
  document.getElementById('selected-protein-display').style.display = 'none';
  document.querySelectorAll('#protein-files-grid .file-card').forEach(card => {
    card.classList.remove('selected');
  });
}

/**
 * 验证表单输入
 */
function validateForm() {
  // 检查蛋白质文件选择
  const selectedProtein = document.querySelector('#protein-files-grid .file-card.selected');
  if (!selectedProtein) {
    showError('请选择一个蛋白质受体文件');
    return false;
  }
  
  // 检查配体文件选择
  const selectedLigands = document.querySelectorAll('#uploaded-files-grid .file-card.selected');
  if (selectedLigands.length === 0) {
    showError('请至少选择一个配体文件');
    return false;
  }
  
  // 检查口袋坐标
  const pocketX = document.getElementById('pocket-x').value;
  const pocketY = document.getElementById('pocket-y').value;
  const pocketZ = document.getElementById('pocket-z').value;
  
  if (!pocketX || !pocketY || !pocketZ) {
    showError('请输入完整的口袋中心坐标');
    return false;
  }
  
  return true;
}

// 初始化蛋白质文件选择功能
function initializeProteinFileSelection() {
  console.log('AutoDock: Initializing protein file selection interface...');

  // 获取界面元素
  const proteinFilesGrid = document.getElementById('protein-files-grid');
  const localProteinInput = document.getElementById('local-protein-input');
  const proteinDropZone = document.getElementById('protein-drop-zone');
  const selectedProteinDisplay = document.getElementById('selected-protein-display');
  const selectedProteinInfo = document.getElementById('selected-protein-info');
  const clearProteinSelectionBtn = document.getElementById('clear-protein-selection-btn');

  // 存储选中的蛋白质文件（单选）
  let selectedProteinFile = null;

  // 更新已上传蛋白质文件网格
  function updateProteinFilesGrid() {
      if (!proteinFilesGrid) return;

      if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
          // 过滤出PDB和PDBQT文件
          const proteinFiles = uploadedFilesData.filter(file =>
              file.type.toLowerCase() === 'pdb' || file.type.toLowerCase() === 'pdbqt'
          );

          if (proteinFiles.length > 0) {
              proteinFilesGrid.innerHTML = '';
              proteinFiles.forEach(file => {
                  const fileCard = document.createElement('div');
                  fileCard.className = 'file-card';
                  fileCard.dataset.fileId = file.id;
                  fileCard.innerHTML = `
                      <div class="file-card-header">
                          <span class="file-type-icon">${getFileTypeIcon(file.type)}</span>
                          <span class="file-name" title="${file.name}">${file.name}</span>
                      </div>
                      <div class="file-info">${file.type.toUpperCase()} • ${formatFileSize(file.size || 0)}</div>
                      <div class="selection-indicator">✓</div>
                  `;

                  // 添加点击事件（单选）
                  fileCard.addEventListener('click', function() {
                      selectProteinFile(file, fileCard);
                  });

                  proteinFilesGrid.appendChild(fileCard);
              });
          } else {
              proteinFilesGrid.innerHTML = '<div class="no-files-message">暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件</div>';
          }
      } else {
          proteinFilesGrid.innerHTML = '<div class="no-files-message">暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件</div>';
      }
  }

  // 将函数引用存储到全局变量
  globalUpdateProteinFilesGrid = updateProteinFilesGrid;

  // 选择蛋白质文件（单选模式）
  function selectProteinFile(file, fileCard) {
      // 清除之前的选择
      proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
          card.classList.remove('selected');
      });

      // 选择新文件
      selectedProteinFile = {
          id: file.id,
          name: file.name,
          type: file.type,
          content: file.content,
          source: 'uploaded'
      };
      fileCard.classList.add('selected');

      updateSelectedProteinDisplay();
  }

  // 本地蛋白质文件上传处理
  if (localProteinInput && proteinDropZone) {
      // 点击上传区域
      proteinDropZone.addEventListener('click', function() {
          localProteinInput.click();
      });

      // 文件选择处理
      localProteinInput.addEventListener('change', function(e) {
          handleLocalProteinUpload(e.target.files);
      });

      // 拖拽上传处理
      proteinDropZone.addEventListener('dragover', function(e) {
          e.preventDefault();
          this.classList.add('dragover');
      });

      proteinDropZone.addEventListener('dragleave', function(e) {
          e.preventDefault();
          this.classList.remove('dragover');
      });

      proteinDropZone.addEventListener('drop', function(e) {
          e.preventDefault();
          this.classList.remove('dragover');
          handleLocalProteinUpload(e.dataTransfer.files);
      });
  }

  // 处理本地蛋白质文件上传
  function handleLocalProteinUpload(files) {
      if (files.length > 0) {
          const file = files[0]; // 只取第一个文件（单选）
          const reader = new FileReader();
          reader.onload = function(e) {
              // 清除之前的选择
              proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                  card.classList.remove('selected');
              });

              selectedProteinFile = {
                  id: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                  name: file.name,
                  type: getFileType(file.name),
                  content: e.target.result,
                  source: 'local'
              };

              updateSelectedProteinDisplay();
              showNotification(`已选择蛋白质文件: ${file.name}`, 'success');
          };
          reader.readAsText(file);
      }

      // 清空input
      localProteinInput.value = '';
  }

  // 更新已选蛋白质文件显示
  function updateSelectedProteinDisplay() {
      if (!selectedProteinDisplay || !selectedProteinInfo) return;

      if (selectedProteinFile) {
          selectedProteinDisplay.style.display = 'block';

          selectedProteinInfo.innerHTML = `
              <div class="selected-file-item">
                  <span class="file-name">${selectedProteinFile.name} (${selectedProteinFile.type.toUpperCase()}) ${selectedProteinFile.source === 'local' ? '- 本地' : ''}</span>
                  <button class="remove-file-btn" id="remove-protein-btn">×</button>
              </div>
          `;

          // 添加删除按钮事件
          document.getElementById('remove-protein-btn').addEventListener('click', function() {
              selectedProteinFile = null;

              // 清除网格中的选中状态
              proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                  card.classList.remove('selected');
              });

              updateSelectedProteinDisplay();
          });
      } else {
          selectedProteinDisplay.style.display = 'none';
      }
  }

  // 清空选择按钮
  if (clearProteinSelectionBtn) {
      clearProteinSelectionBtn.addEventListener('click', function() {
          selectedProteinFile = null;

          // 清除网格中的选中状态
          proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
              card.classList.remove('selected');
          });

          updateSelectedProteinDisplay();
      });
  }

  // 初始化时更新文件网格
  updateProteinFilesGrid();

  // 提供全局访问接口
  window.getSelectedProteinFile = function() {
      return selectedProteinFile;
  };
}

// 以下为初始化时需要执行的函数
export function init() {
  // DOM加载完成后执行
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化查看器
    if (typeof $3Dmol !== 'undefined') {
      initViewer();
    } else {
      showError('3DMol.js库未加载，无法初始化分子查看器');
    }
    
    // 为文件上传按钮绑定事件
    const fileInput = document.getElementById('file-input');
    const uploadBtn = document.querySelector('.upload-btn');
    
    if (fileInput && uploadBtn) {
      uploadBtn.addEventListener('click', () => {
        fileInput.click();
      });
      
      fileInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
      });
    }
    
    // 绑定其他功能按钮
    const clearAtomsBtn = document.getElementById('clear-atoms-btn');
    if (clearAtomsBtn) {
      clearAtomsBtn.addEventListener('click', clearSelectedAtoms);
    }
    
    const autoFillBtn = document.getElementById('auto-fill-coords');
    if (autoFillBtn) {
      autoFillBtn.addEventListener('click', autoFillCoordinates);
    }
    
    const clearSelectionBtn = document.getElementById('clear-selection-btn');
    if (clearSelectionBtn) {
      clearSelectionBtn.addEventListener('click', clearLigandSelection);
    }
    
    const clearProteinSelectionBtn = document.getElementById('clear-protein-selection-btn');
    if (clearProteinSelectionBtn) {
      clearProteinSelectionBtn.addEventListener('click', () => {
        document.getElementById('selected-protein-display').style.display = 'none';
        document.querySelectorAll('#protein-files-grid .file-card').forEach(card => {
          card.classList.remove('selected');
        });
      });
    }
    
    const validateInputsBtn = document.getElementById('validate-inputs');
    if (validateInputsBtn) {
      validateInputsBtn.addEventListener('click', validateForm);
    }
    
    const resetInputsBtn = document.getElementById('reset-inputs');
    if (resetInputsBtn) {
      resetInputsBtn.addEventListener('click', resetForm);
    }
    
    // 本地文件上传功能
    const localProteinInput = document.getElementById('local-protein-input');
    const proteinDropZone = document.getElementById('protein-drop-zone');
    
    if (localProteinInput && proteinDropZone) {
      proteinDropZone.addEventListener('click', () => {
        localProteinInput.click();
      });
      
      localProteinInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
      });
    }
    
    const localLigandInput = document.getElementById('local-ligand-input');
    const ligandDropZone = document.getElementById('ligand-drop-zone');
    
    if (localLigandInput && ligandDropZone) {
      ligandDropZone.addEventListener('click', () => {
        localLigandInput.click();
      });
      
      localLigandInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
      });
    }
    
    // 配体选项卡功能
    const ligandTabs = document.querySelectorAll('.ligand-tab');
    if (ligandTabs.length > 0) {
      ligandTabs.forEach(tab => {
        tab.addEventListener('click', () => {
          document.querySelectorAll('.ligand-tab').forEach(t => {
            t.classList.remove('active');
          });
          tab.classList.add('active');
          
          const tabId = tab.dataset.tab;
          document.querySelectorAll('.ligand-tab-content').forEach(content => {
            content.classList.remove('active');
          });
          document.getElementById(`${tabId}-tab-content`).classList.add('active');
        });
      });
    }
  });
}

export default {
  init
}; 