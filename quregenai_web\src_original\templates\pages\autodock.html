{% extends "layout_with_nav.html" %}

{% block title %}AutoDock - QureGenAI 药物设计平台{% endblock %}

{% block page_css %}
<link rel="stylesheet" href="/src/styles/batch-smiles-enhanced.css">
{% endblock %}

{% block page_js %}
<script src="/src/scripts/3dmol.js"></script>
<script src="/src/scripts/show.js"></script>
<script src="/src/scripts/smiles-drawer.min.js"></script>
{% endblock %}

{% block inline_styles %}
<link rel="stylesheet" href="/src/styles/pages/autodock.css">
{% endblock %}

{% block content %}
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%;">
        <div style="flex: 1;">
            <h1 class="page-title">
                <span class="page-icon"><img src="/src/images/autodock-cube.svg" alt="AutoDock"></span>
                <span data-i18n="page-title">AutoDock 高通量筛选与分子对接</span>
                <a href="https://mp.weixin.qq.com/s/q6m5Q68KBwP_M22V3pwnXw" target="_blank" class="tutorial-btn">
                    <span>📚</span>
                    <span data-i18n="tutorial-btn">平台教程</span>
                </a>
            </h1>
            <p class="page-subtitle" data-i18n="page-subtitle">AutoDock是一套应用广泛的开源分子对接软件套件，用于预测小分子配体（如候选药物）与已知三维结构的生物大分子（通常是蛋白质受体）的结合模式和亲和力。它通过模拟配体在受体活性位点的各种可能构象和朝向，并利用能量打分函数评估结合强度，从而帮助科研人员理解分子间的相互作用机制，并广泛应用于药物发现和虚拟筛选等领域。该套件主要包含AutoDock程序（执行对接计算，采用拉马克遗传算法和经验自由能打分函数）和AutoGrid程序（预先计算格点图，用于表示受体）。</p>
        </div>
    </div>
</div>

    <!-- 对接口袋识别和分析指南 -->
    <div class="docking-pocket-guide">
        <h3 class="guide-title" data-i18n="guide-title">🎯 对接口袋识别与分析指南</h3>
        
        <div class="guide-section">
            <div class="guide-card">
                <div class="card-header">
                    <h4 class="card-title">
                        <span class="card-icon">🔍</span>
                        <span data-i18n="step-one-title">步骤一：蛋白质结构分析</span>
                    </h4>
                </div>
                <div class="card-content">
                    <ul>
                        <li data-i18n="step-one-item1"><strong>上传蛋白质文件：</strong>首先上传PDB或PDBQT格式的蛋白质结构文件</li>
                        <li data-i18n="step-one-item2"><strong>观察整体结构：</strong>在3D查看器中旋转、缩放，熟悉蛋白质的整体折叠结构</li>
                        <li data-i18n="step-one-item3"><strong>寻找潜在口袋：</strong>寻找蛋白质表面的凹陷区域，这些通常是配体结合位点</li>
                        <li data-i18n="step-one-item4"><strong>关注活性位点：</strong>如果已知活性中心残基，重点观察这些区域</li>
                    </ul>
                </div>
            </div>

            <div class="guide-card">
                <div class="card-header">
                    <h4 class="card-title">
                        <span class="card-icon">📍</span>
                        <span data-i18n="step-two-title">步骤二：口袋中心点确定</span>
                    </h4>
                </div>
                <div class="card-content">
                    <ul>
                        <li data-i18n="step-two-item1"><strong>点击原子选择：</strong>在3D查看器中点击口袋内的关键原子（如活性位点残基）</li>
                        <li data-i18n="step-two-item2"><strong>查看原子信息：</strong>选中的原子信息会显示在下方，包括残基类型和坐标</li>
                        <li data-i18n="step-two-item3"><strong>设置中心点：</strong>点击原子信息条目可直接将该坐标设为对接中心</li>
                        <li data-i18n="step-two-item4"><strong>多原子平均：</strong>选择多个原子时，系统会自动计算几何中心</li>
                    </ul>
                </div>
            </div>

            <div class="guide-card">
                <div class="card-header">
                    <h4 class="card-title">
                        <span class="card-icon">📏</span>
                        <span data-i18n="step-three-title">步骤三：对接盒子尺寸设置</span>
                    </h4>
                </div>
                <div class="card-content">
                    <ul>
                        <li data-i18n="step-three-item1"><strong>估算口袋大小：</strong>观察目标口袋的大致尺寸范围</li>
                        <li data-i18n="step-three-item2"><strong>考虑配体尺寸：</strong>确保对接盒子足够容纳配体分子的各种构象</li>
                        <li><span data-i18n="step-three-item3"><strong>推荐尺寸：</strong></span>
                            <ul>
                                <li data-i18n="size-small">小分子配体：15-20 Å</li>
                                <li data-i18n="size-peptide">肽段配体：20-25 Å</li>
                                <li data-i18n="size-large">大分子配体：25-30 Å</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="guide-card">
                <div class="card-header">
                    <h4 class="card-title">
                        <span class="card-icon">💡</span>
                        <span data-i18n="tips-title">专业建议与注意事项</span>
                    </h4>
                </div>
                <div class="card-content">
                    <div class="tips-grid">
                        <div class="tip-item">
                            <strong data-i18n="pocket-types-title">🎪 常见口袋类型：</strong>
                            <ul>
                                <li data-i18n="pocket-type1">酶活性位点（通常较深且狭窄）</li>
                                <li data-i18n="pocket-type2">蛋白质-蛋白质相互作用界面（通常较浅且宽阔）</li>
                                <li data-i18n="pocket-type3">变构调节位点（远离活性中心）</li>
                            </ul>
                        </div>
                        
                        <div class="tip-item">
                            <strong data-i18n="precautions-title">⚠️ 注意事项：</strong>
                            <ul>
                                <li data-i18n="precaution1">避免将对接盒子设置得过大，会增加计算时间</li>
                                <li data-i18n="precaution2">确保盒子完全包含目标结合位点</li>
                                <li data-i18n="precaution3">考虑蛋白质柔性，为侧链运动留出空间</li>
                                <li data-i18n="precaution4">如有已知结合物结构，可参考其结合模式</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速参考卡片 -->
    </div>

</section>

<!-- 新的分子查看器区域 -->
<section class="molecule-viewer-section">
    <h2 class="section-title" data-i18n="viewer-title">分子结构查看器</h2>
    <div id="loading-indicator" data-i18n="loading">加载中...</div>
    <div id="error-message"></div>
    <div class="viewer-container">
        <div class="viewer-section">
            <div id="viewer-container"></div>
        </div>
        
        <div class="controls-section">
            <div class="upload-section">
                <label for="file-input" class="upload-btn" data-i18n="upload-btn">
                    点击上传结构文件
                </label>
                <input type="file" id="file-input" multiple accept=".pdb,.sdf,.pdbqt">
                <div class="file-type-info">
                    <strong data-i18n="supported-formats">支持的文件格式：</strong><br>
                    <span data-i18n="pdb-desc" data-i18n-key="pdb-desc">PDB文件 - 蛋白质结构（卡通渲染）</span><br>
                    <span data-i18n="sdf-desc" data-i18n-key="sdf-desc">SDF文件 - 小分子结构（球棍模型）</span><br>
                    <span data-i18n="pdbqt-desc" data-i18n-key="pdbqt-desc">PDBQT文件 - 电荷结构文件</span><br>
                </div>
            </div>
            
            <div id="file-list-container">
                <h3 data-i18n="uploaded-files">已上传文件</h3>
                <p data-i18n="no-files">暂无文件 </p>
            </div>
        </div>
    </div>
    
    <!-- 新增原子信息列表区域 -->
    <div class="atom-info-section">
        <div class="atom-info-header">
            <h3 data-i18n="selected-atoms-info">已选择的原子信息</h3>
            <button id="clear-atoms-btn" class="clear-atoms-btn" data-i18n="clear-all">一键清除</button>
        </div>
        
        <!-- 添加使用说明 -->
        <div class="atom-selection-guide" style="background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem;">
            <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                <div style="font-size: 1.25rem; color: #0284c7; margin-top: 0.125rem;">💡</div>
                <div style="flex: 1;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #0c4a6e; font-size: 0.95rem; font-weight: 600;" data-i18n="atom-selection-guide">原子选择使用说明：</h4>
                    <ul style="margin: 0; padding-left: 1.25rem; color: #0369a1; font-size: 0.875rem; line-height: 1.5;">
                        <li style="margin-bottom: 0.25rem;" data-i18n="atom-guide1">在上方3D分子查看器中，<strong>点击蛋白质或小分子上的原子</strong>进行选择</li>
                        <li style="margin-bottom: 0.25rem;" data-i18n="atom-guide2">选中的原子信息会显示在下方列表中，包括原子类型、残基信息和坐标</li>
                        <li style="margin-bottom: 0.25rem;" data-i18n="atom-guide3"><strong>点击下方原子信息条目</strong>，可直接将该原子坐标设为对接口袋中心点</li>
                        <li style="margin-bottom: 0;" data-i18n="atom-guide4">选择<strong>多个原子</strong>时，系统会自动计算它们的几何中心作为口袋中心坐标</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div id="atom-info-list" class="atom-info-list">
            <p class="no-atoms-message" data-i18n="no-atoms">暂无选择的原子</p>
        </div>
    </div>

    <!-- 新增输入文件版块 -->
    <div class="input-files-section">
        <h3 class="section-title" data-i18n="input-params-title">AutoDock 输入参数设置</h3>
        
        <!-- 文件选择分组 -->
        <div class="parameter-group">
            <div class="group-header">
                <h4 class="group-title" data-i18n="docking-files-selection">📁 对接文件选择</h4>
                <div class="group-description" data-i18n="files-selection-desc">选择用于分子对接的蛋白质和小分子文件</div>
            </div>
            
            <div class="input-grid">
                <!-- 蛋白质文件选择 -->
                <div class="input-group">
                    <label class="input-label">
                        <span class="label-text" data-i18n="protein-receptor-file">🧬 蛋白质受体文件 (PDB)</span>
                        <span class="required">*</span>
                    </label>
                    
                    <!-- 蛋白质文件选择区域 -->
                    <div class="protein-file-selection-section">
                        <!-- 已上传文件选择 -->
                        <div class="uploaded-files-section">
                            <h5 class="section-subtitle">
                                <span class="subtitle-icon">📂</span>
                                <span data-i18n="uploaded-files-selection">从已上传文件选择</span>
                            </h5>
                            <div id="protein-files-grid" class="uploaded-files-grid">
                                <div class="no-files-message" data-i18n="no-protein-files">
                                    <span data-i18n="no-protein-files">暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分隔线 -->
                        <div class="section-divider">
                            <span class="divider-text" data-i18n="or">或</span>
                        </div>
                        
                        <!-- 本地文件上传 -->
                        <div class="local-upload-section">
                            <h5 class="section-subtitle">
                                <span class="subtitle-icon">💾</span>
                                <span data-i18n="upload-local-receptor">上传本地受体文件</span>
                            </h5>
                            <div class="local-upload-area">
                                <input type="file" id="local-protein-input" accept=".pdb,.pdbqt" style="display: none;">
                                <div class="upload-drop-zone" id="protein-drop-zone">
                                    <div class="upload-icon">📁</div>
                                    <div class="upload-text">
                                        <strong data-i18n="click-select-pdb">点击选择PDB/PDBQT文件</strong> <span data-i18n="or-drag">或</span> <strong data-i18n="drag-files-here">拖拽文件到此处</strong>
                                    </div>
                                    <div class="upload-hint" data-i18n="supported-pdb-formats">
                                        支持格式：PDB、PDBQT
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 已选蛋白质文件显示 -->
                        <div id="selected-protein-display" class="selected-files-display" style="display: none;">
                            <div class="selected-files-header">
                                <h5 data-i18n="selected-protein-file">已选择的蛋白质文件</h5>
                                <button id="clear-protein-selection-btn" class="clear-selection-btn" data-i18n="clear-selection">清空选择</button>
                            </div>
                            <div id="selected-protein-info" class="selected-files-list"></div>
                        </div>
                    </div>
                    
                    <div class="input-hint" data-i18n="protein-file-hint">选择一个PDB或PDBQT格式的蛋白质文件作为对接受体</div>
                </div>
            </div>

            <!-- 小分子文件选择 - 独立显示在下方 -->
            <div class="input-group" style="margin-top: 1.5rem;">
                <label class="input-label">
                    <span class="label-text" data-i18n="ligand-file">💊 小分子配体文件</span>
                    <span class="required">*</span>
                </label>
                
                <!-- Tab导航 -->
                <div class="ligand-tabs">
                    <button type="button" class="ligand-tab  active" data-tab="files"><span data-i18n="select-ligand-files">选择配体分子结构文件</span></button>
                    <button type="button" class="ligand-tab" data-tab="batch-file"><span data-i18n="batch-smiles-file">批量SMILES文件</span></button>
                    <button type="button" class="ligand-tab" data-tab="batch-text"><span data-i18n="batch-smiles-input">批量SMILES输入</span></button>
                </div>

                <!-- Tab内容 -->
                <!-- 分子结构文件选择 -->
                <div id="files-tab-content" class="ligand-tab-content active">
                    <div class="ligand-input-container">
                        <!-- 文件选择区域 -->
                        <div class="file-selection-section">
                            <!-- 已上传文件选择 -->
                            <div class="uploaded-files-section">
                                <h5 class="section-subtitle">
                                    <span class="subtitle-icon">📂</span>
                                    <span data-i18n="select-from-uploaded-ligands">从已上传的配体分子文件中选择</span>
                                </h5>
                                <div id="uploaded-files-grid" class="uploaded-files-grid">
                                    <div class="no-files-message">
                                        <span data-i18n="no-ligand-files">暂无已上传的分子文件，请先在上方分子查看器中上传文件</span>
                        </div>
                        </div>
                    </div>
                            
                            <!-- 分隔线 -->
                            <div class="section-divider">
                                <span class="divider-text" data-i18n="or">或</span>
                            </div>
                            
                            <!-- 本地文件上传 -->
                            <div class="local-upload-section">
                                <h5 class="section-subtitle">
                                    <span class="subtitle-icon">💾</span>
                                    <span data-i18n="upload-local-ligand">上传本地配体文件</span>
                                </h5>
                                <div class="local-upload-area">
                                    <input type="file" id="local-ligand-input" multiple accept=".pdb,.sdf,.pdbqt,.mol2" style="display: none;">
                                    <div class="upload-drop-zone" id="ligand-drop-zone">
                                        <div class="upload-icon">📁</div>
                                        <div class="upload-text">
                                            <strong data-i18n="click-select-files">点击选择文件</strong> <span data-i18n="or-drag">或</span> <strong data-i18n="drag-files-here">拖拽文件到此处</strong>
                                        </div>
                                        <div class="upload-hint" data-i18n="supported-ligand-formats">
                                            支持格式：PDB、SDF、PDBQT、MOL2
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 已选文件显示 -->
                            <div id="selected-files-display" class="selected-files-display" style="display: none;">
                                <div class="selected-files-header">
                                    <h5><span data-i18n="selected-files">已选择的文件</span> (<span id="selected-files-count">0</span><span data-i18n="files-count">个</span>)</h5>
                                    <button id="clear-selection-btn" class="clear-selection-btn" data-i18n="clear-selection">清空选择</button>
                                </div>
                                <div id="selected-files-list" class="selected-files-list"></div>
                            </div>
                        </div>
                    </div>
                    <div class="input-hint">
                        <span data-i18n="file-selection-hint">点击已上传文件或上传本地文件进行选择</span><br>
                        <strong data-i18n="single-file-mode">选择1个文件：</strong><span data-i18n="single-file-desc">使用单分子对接模式</span><br>
                        <strong data-i18n="multi-file-mode">选择多个文件：</strong><span data-i18n="multi-file-desc">使用多分子批量对接模式</span>
                    </div>
                </div>

                <!-- 批量SMILES文件上传 -->
                <div id="batch-file-tab-content" class="ligand-tab-content">
                    <div class="batch-upload-container">
                        <div class="csv-upload-section">
                            <input type="file" id="csv-file-input" accept=".csv" style="display: none;">
                            <button id="upload-csv-btn" class="upload-btn">
                                <span class="btn-icon">📁</span>
                                <span data-i18n="upload-csv">上传CSV文件</span>
                            </button>
                            <div class="csv-format-hint">
                                <span data-i18n="csv-format-desc">CSV格式：第一列为name，第二列为smiles</span><br>
                                <span data-i18n="csv-example">例如：aspirin,CC(=O)OC1=CC=CC=C1C(=O)O</span>
                            </div>
                        </div>
                        
                        <div class="batch-molecules-container" style="display: none;">
                            <div class="molecules-panel">
                                <div class="panel-header">
                                    <h5><span data-i18n="molecule-list">分子列表</span> (<span id="molecule-count">0</span><span data-i18n="molecules-count">个</span>)</h5>
                                    <div class="batch-status-info" style="font-size: 0.8rem; color: #059669; margin-top: 0.25rem;">
                                        <span data-i18n="batch-docking-info">💊 所有分子将用于批量对接</span>
                                    </div>
                                    <div class="search-container">
                                        <input type="text" id="molecule-search" placeholder="搜索分子名称...">
                                        <button id="search-btn" class="search-btn">🔍</button>
                                    </div>
                                </div>
                                <div id="molecules-list" class="molecules-list"></div>
                            </div>
                            
                            <div class="structure-panel">
                                <div class="panel-header">
                                    <h5 data-i18n="2d-structure">2D结构</h5>
                                </div>
                                <canvas id="structure2d" width="300" height="200"></canvas>
                                <div id="molecule-info" class="molecule-info"></div>
                            </div>
                        </div>
                    </div>
                    <div class="input-hint" data-i18n="csv-upload-hint">上传包含分子名称和SMILES的CSV文件，所有分子将自动用于批量对接</div>
                </div>

                <!-- 批量SMILES直接输入 -->
                <div id="batch-text-tab-content" class="ligand-tab-content">
                    <div class="text-input-container">
                        <label for="batch-smiles-text" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151;" data-i18n="direct-smiles-input">
                            直接输入SMILES字符串
                        </label>
                        <textarea 
                            id="batch-smiles-text" 
                            placeholder="每行输入一个SMILES字符串，例如：&#10;CCO&#10;CC(=O)OC1=CC=CC=C1C(=O)O&#10;CN1C=NC2=C1C(=O)N(C(=O)N2C)C"
                            style="width: 100%; min-height: 120px; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 0.5rem; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.875rem; resize: vertical; transition: border-color 0.2s ease;"
                            onfocus="this.style.borderColor='#3b82f6'"
                            onblur="this.style.borderColor='#e5e7eb'"
                        ></textarea>
                        <button id="parse-smiles-text-btn" class="upload-btn" style="margin-top: 0.75rem;">
                            <span class="btn-icon" >📝</span>
                            <span data-i18n="parse-smiles">解析SMILES文本</span>
                        </button>
                    </div>
                    <div class="input-hint" data-i18n="smiles-input-hint">每行输入一个SMILES字符串，系统将自动为每个分子生成名称</div>
                </div>
            </div>
        </div>

        <!-- 对接参数分组 -->
        <div class="parameter-group">
            <div class="group-header">
                <h4 class="group-title" data-i18n="docking-params-title">⚙️ 对接参数</h4>
                <div class="group-description" data-i18n="docking-params-desc">设置分子对接的空间范围和计算参数</div>
            </div>
            
            <div class="input-grid">
                <!-- 口袋坐标 -->
                <div class="input-group full-width">
                    <label class="input-label">
                        <span class="label-text" data-i18n="pocket-center-coords">🎯 口袋中心坐标</span>
                        <span class="required">*</span>
                    </label>
                    <div class="coordinate-container">
                        <div class="coordinate-input-group">
                            <label for="pocket-x" data-i18n="x-coord">X 坐标</label>
                            <input type="number" id="pocket-x" step="0.1" placeholder="0.0">
                        </div>
                        <div class="coordinate-input-group">
                            <label for="pocket-y" data-i18n="y-coord">Y 坐标</label>
                            <input type="number" id="pocket-y" step="0.1" placeholder="0.0">
                        </div>
                        <div class="coordinate-input-group">
                            <label for="pocket-z" data-i18n="z-coord">Z 坐标</label>
                            <input type="number" id="pocket-z" step="0.1" placeholder="0.0">
                        </div>
                        <button id="auto-fill-coords" class="auto-fill-btn">
                            <span class="btn-icon">🎯</span>
                            <span data-i18n="auto-fill-coords">自动填入</span>
                        </button>
                    </div>
                    <div class="input-hint" data-i18n="manual-input-hint">手动输入坐标或点击按钮从已选择的原子计算中心坐标</div>
                </div>

                <!-- 对接盒子尺寸 -->
                <div class="input-group">
                    <label class="input-label">
                        <span class="label-text" data-i18n="docking-box-size">📦 对接盒子尺寸 (Å)</span>
                    </label>
                    <div class="size-container">
                        <div class="size-input-group">
                            <label for="size-x" data-i18n="x-size">X 尺寸</label>
                            <input type="number" id="size-x" value="15" min="1" max="100">
                        </div>
                        <div class="size-input-group">
                            <label for="size-y" data-i18n="y-size">Y 尺寸</label>
                            <input type="number" id="size-y" value="15" min="1" max="100">
                        </div>
                        <div class="size-input-group">
                            <label for="size-z" data-i18n="z-size">Z 尺寸</label>
                            <input type="number" id="size-z" value="15" min="1" max="100">
                        </div>
                    </div>
                    <div class="input-hint" data-i18n="box-size-hint">定义搜索空间的尺寸，默认为15Å × 15Å × 15Å</div>
                </div>

                <!-- 线程数设置 -->
                <div class="input-group">
                    <label for="threads" class="input-label">
                        <span class="label-text" data-i18n="threads-label">⚡ 计算线程数</span>
                    </label>
                    <div class="threads-input-wrapper">
                        <input type="number" id="threads" value="2000" min="1" max="10000" class="threads-input">
                        <div class="input-unit"><span data-i18n="threads-unit">线程</span></div>
                    </div>
                    <div class="input-hint"><span data-i18n="threads-hint">设置计算使用的线程数量，推荐值：2000-5000</span></div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons-container">
            <button id="validate-inputs" class="btn-secondary">
                <span class="btn-icon">✅</span>
                <span data-i18n="validate-inputs">验证输入</span>
            </button>
            <button id="submit-btn" class="btn-primary">
                <span class="btn-icon">🚀</span>
                <span data-i18n="start-docking">开始对接</span>
            </button>
            <button id="reset-inputs" class="btn-secondary">
                <span class="btn-icon">🔄</span>
                <span data-i18n="reset-params">重置参数</span>
            </button>
        </div>

        <!-- 对接结果栏 -->
        <div id="docking-results-section" class="docking-results-section" style="display: none;">
            <div class="results-header">
                <h3 class="results-title">
                    <span class="results-icon">📊</span>
                    <span data-i18n="docking-results">对接结果</span>
                </h3>
                <div class="results-controls">
                    <!-- <div class="results-status">
                        <span id="results-status-text" class="status-text" data-i18n="waiting-results">等待结果...</span>
                        <div id="results-loading" class="loading-spinner"></div>
                    </div> -->
                    <button id="hide-results-btn" class="hide-results-btn" title="隐藏结果">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M18 6L6 18M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div id="results-content" class="results-content">
                <!-- 任务信息 -->
                <div id="task-info" class="result-card task-info-card">
                    <div class="card-header">
                        <h4 data-i18n="task-info">任务信息</h4>
                    </div>
                    <div class="card-content">
                        <!-- Task ID on its own line -->
                        <div class="task-id-row">
                            <span class="info-label" data-i18n="task-id">任务ID:</span>
                            <span id="task-id" class="info-value task-id-value">-</span>
                        </div>
                        
                        <!-- Only show status -->
                        <div class="info-grid-compact">
                            <div class="info-item">
                                <span class="info-label" data-i18n="task-status">状态:</span>
                                <span id="task-status" class="info-value status-badge">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结果详情 -->
                <div id="results-details" class="result-card results-details-card">
                    <div class="card-header">
                        <h4 data-i18n="results-details">结果详情</h4>
                        <button id="toggle-details" class="toggle-btn" data-i18n="toggle-details">+</button>
                    </div>
                    <div class="card-content">
                        <div id="human-readable-results" class="human-readable-results"></div>
                        
                        <div id="detailed-results" class="detailed-results" style="display: none;">
                            <div class="results-tabs">
                                <button class="tab-btn active" data-tab="scores" data-i18n="scores-tab">结合能量</button>
                                <button class="tab-btn" data-tab="files" data-i18n="files-tab">文件下载</button>
                            </div>
                            
                            <div id="scores-tab" class="tab-content active">
                                <div id="scores-list" class="scores-list"></div>
                            </div>
                            
                            <div id="files-tab" class="tab-content">
                                <div id="files-list" class="files-list"></div>
                                
                                <!-- 模型提取功能 -->
                                <div id="model-extraction-section" class="model-extraction-section" style="margin-top: 2rem; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px; background-color: #f9fafb;">
                                    <h4 style="margin: 0 0 1rem 0; color: #374151; font-size: 1.1rem; font-weight: 600;">
                                        <span style="margin-right: 0.5rem;">🔧</span>
                                        <span data-i18n="model-extraction-title">模型提取工具</span>
                                    </h4>
                                    <p style="margin: 0 0 1rem 0; color: #6b7280; font-size: 0.875rem;">
                                        <span data-i18n="model-extraction-desc">从对接结果文件中提取指定构象并转换为PDB格式</span>
                                    </p>
                                    
                                    <div class="extraction-controls" style="display: flex; flex-direction: column; gap: 1rem;">
                                        <!-- 文件上传 -->
                                        <div class="control-group">
                                            <label for="pdbqt-file-input" style="display: block; margin-bottom: 0.5rem; color: #374151; font-weight: 500;">
                                                <span data-i18n="select-pdbqt-file">选择PDBQT对接结果文件:</span>
                                            </label>
                                            <input type="file" id="pdbqt-file-input" accept=".pdbqt" 
                                                    style="padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 4px; background-color: white; width: 100%; max-width: 400px;">
                                        </div>
                                        
                                        <!-- 模型选择 -->
                                        <div class="control-group">
                                            <label for="model-number-input" style="display: block; margin-bottom: 0.5rem; color: #374151; font-weight: 500;">
                                                <span data-i18n="select-model-number">选择构象编号:</span>
                                            </label>
                                            <input type="number" id="model-number-input" min="1" value="1" 
                                                    style="padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 4px; background-color: white; width: 100px;">
                                            <span style="margin-left: 0.5rem; color: #6b7280; font-size: 0.875rem;">
                                                <span data-i18n="default-model-number">(默认提取第1个构象)</span>
                                            </span>
                                        </div>
                                        
                                        <!-- 提取按钮 -->
                                        <div class="control-group">
                                            <button id="extract-model-btn" 
                                                    style="padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border: none; border-radius: 6px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; display: inline-flex; align-items: center; gap: 0.5rem;">
                                                <span>⚡</span>
                                                <span data-i18n="extract-and-convert">提取并转换为PDB</span>
                                            </button>
                                        </div>
                                        
                                        <!-- 状态显示 -->
                                        <div id="extraction-status" class="extraction-status" style="display: none; padding: 0.75rem; border-radius: 4px; font-size: 0.875rem;">
                                            <!-- 状态信息将在这里显示 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 任务历史部分 -->
<div id="task-history-section" class="section">
    <div class="section-header">
        <h2 data-i18n="task-history">对接任务历史</h2>
        <button id="refresh-tasks-btn" class="btn btn-refresh-green">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="23 4 23 10 17 10"></polyline>
                <polyline points="1 20 1 14 7 14"></polyline>
                <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
            </svg>
            <span data-i18n="refresh-tasks">刷新</span>
        </button>
    </div>
    <div class="task-history-container">
        <div id="tasks-list" class="tasks-list">
            <!-- 任务列表将在这里动态生成 -->
        </div>
        <!-- 添加分页控件 -->
        <div id="tasks-pagination" class="pagination-container" style="display: none;">
            <div class="pagination-info">
                <span id="pagination-info-text"></span>
            </div>
            <div class="pagination-controls">
                <button id="prev-page-btn" class="pagination-btn" onclick="loadTaskHistoryPage(currentPage - 1)">
                    <span data-i18n="prev-page">上一页</span>
                </button>
                <div id="page-numbers" class="page-numbers">
                    <!-- 页码按钮将在这里动态生成 -->
                </div>
                <button id="next-page-btn" class="pagination-btn" onclick="loadTaskHistoryPage(currentPage + 1)">
                    <span data-i18n="next-page">下一页</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 对接结果部分 -->
{% endblock %}

{% block inline_scripts %}
<script>
        // 多语言文本配置（扩展版本）
        const translations = {
            zh: {
                'page-title': 'AutoDock 高通量筛选与分子对接',
                'page-subtitle': 'AutoDock是一套应用广泛的开源分子对接软件套件，用于预测小分子配体（如候选药物）与已知三维结构的生物大分子（通常是蛋白质受体）的结合模式和亲和力。它通过模拟配体在受体活性位点的各种可能构象和朝向，并利用能量打分函数评估结合强度，从而帮助科研人员理解分子间的相互作用机制，并广泛应用于药物发现和虚拟筛选等领域。该套件主要包含AutoDock程序（执行对接计算，采用拉马克遗传算法和经验自由能打分函数）和AutoGrid程序（预先计算格点图，用于表示受体）。',
                'tutorial-btn': '平台教程',
                'guide-title': '🎯 对接口袋识别与分析指南',
                'step-one-title': '步骤一：蛋白质结构分析',
                'step-one-item1': '上传蛋白质文件：首先上传PDB或PDBQT格式的蛋白质结构文件',
                'step-one-item2': '观察整体结构：在3D查看器中旋转、缩放，熟悉蛋白质的整体折叠结构',
                'step-one-item3': '寻找潜在口袋：寻找蛋白质表面的凹陷区域，这些通常是配体结合位点',
                'step-one-item4': '关注活性位点：如果已知活性中心残基，重点观察这些区域',
                'step-two-title': '步骤二：口袋中心点确定',
                'step-two-item1': '点击原子选择：在3D查看器中点击口袋内的关键原子（如活性位点残基）',
                'step-two-item2': '查看原子信息：选中的原子信息会显示在下方，包括残基类型和坐标',
                'step-two-item3': '设置中心点：点击原子信息条目可直接将该坐标设为对接中心',
                'step-two-item4': '多原子平均：选择多个原子时，系统会自动计算几何中心',
                'step-three-title': '步骤三：对接盒子尺寸设置',
                'step-three-item1': '估算口袋大小：观察目标口袋的大致尺寸范围',
                'step-three-item2': '考虑配体尺寸：确保对接盒子足够容纳配体分子的各种构象',
                'step-three-item3': '推荐尺寸：',
                'size-small': '小分子配体：15-20 Å',
                'size-peptide': '肽段配体：20-25 Å',
                'size-large': '大分子配体：25-30 Å',
                'tips-title': '专业建议与注意事项',
                'pocket-types-title': '🎪 常见口袋类型：',
                'pocket-type1': '酶活性位点（通常较深且狭窄）',
                'pocket-type2': '蛋白质-蛋白质相互作用界面（通常较浅且宽阔）',
                'pocket-type3': '变构调节位点（远离活性中心）',
                'precautions-title': '⚠️ 注意事项：',
                'precaution1': '避免将对接盒子设置得过大，会增加计算时间',
                'precaution2': '确保盒子完全包含目标结合位点',
                'precaution3': '考虑蛋白质柔性，为侧链运动留出空间',
                'precaution4': '如有已知结合物结构，可参考其结合模式',
                'viewer-title': '分子结构查看器',
                'loading': '加载中...',
                'upload-btn': '点击上传结构文件',
                'supported-formats': '支持的文件格式：',
                'pdb-desc': '• PDB文件 - 蛋白质结构（卡通渲染）',
                'sdf-desc': '• SDF文件 - 小分子结构（球棍模型）',
                'pdbqt-desc': '• PDBQT文件 - 电荷结构文件',
                'uploaded-files': '已上传文件',
                'no-files': '暂无文件',
                'selected-atoms-info': '已选择的原子信息',
                'clear-all': '一键清除',
                'atom-selection-guide': '原子选择使用说明：',
                'atom-guide1': '在上方3D分子查看器中，点击蛋白质或小分子上的原子进行选择',
                'atom-guide2': '选中的原子信息会显示在下方，包括原子类型、残基信息和坐标',
                'atom-guide3': '点击下方原子信息条目，可直接将该原子坐标设为对接口袋中心点',
                'atom-guide4': '选择多个原子时，系统会自动计算它们的几何中心作为口袋中心坐标',
                'no-atoms': '暂无选择的原子',
                'input-params-title': 'AutoDock 输入参数设置',
                'docking-files-selection': '📁 对接文件选择',
                'files-selection-desc': '选择用于分子对接的蛋白质和小分子文件',
                'protein-receptor-file': '🧬 蛋白质受体文件 (PDB)',
                'uploaded-files-selection': '从已上传文件选择',
                'no-protein-files': '暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件',
                'or': '或',
                'upload-local-receptor': '上传本地受体文件',
                'click-select-pdb': '点击选择PDB/PDBQT文件',
                'or-drag': '或',
                'drag-files-here': '拖拽文件到此处',
                'supported-pdb-formats': '支持格式：PDB、PDBQT',
                'selected-protein-file': '已选择的蛋白质文件',
                'clear-selection': '清空选择',
                'protein-file-hint': '选择一个PDB或PDBQT格式的蛋白质文件作为对接受体',
                'ligand-file': '💊 小分子配体文件',
                'select-ligand-files': '选择配体分子结构文件',
                'batch-smiles-file': '批量SMILES文件',
                'batch-smiles-input': '批量SMILES输入',
                'select-from-uploaded-ligands': '从已上传的配体分子文件中选择',
                'no-ligand-files': '暂无已上传的分子文件，请先在上方分子查看器中上传文件',
                'upload-local-ligand': '上传本地配体文件',
                'click-select-files': '点击选择文件',
                'supported-ligand-formats': '支持格式：PDB、SDF、PDBQT、MOL2',
                'selected-files': '已选择的文件',
                'files-count': '个',
                'file-selection-hint': '点击已上传文件或上传本地文件进行选择',
                'single-file-mode': '选择1个文件：',
                'single-file-desc': '使用单分子对接模式',
                'multi-file-mode': '选择多个文件：',
                'multi-file-desc': '使用多分子批量对接模式',
                'upload-csv': '上传CSV文件',
                'csv-format-desc': 'CSV格式：第一列为name，第二列为smiles',
                'csv-example': '例如：aspirin,CC(=O)OC1=CC=CC=C1C(=O)O',
                'molecule-list': '分子列表',
                'molecules-count': '个',
                'batch-docking-info': '💊 所有分子将用于批量对接',
                'search-placeholder': '搜索分子名称...',
                '2d-structure': '2D结构',
                'csv-upload-hint': '上传包含分子名称和SMILES的CSV文件，所有分子将自动用于批量对接',
                'direct-smiles-input': '直接输入SMILES字符串',
                'smiles-placeholder': '每行输入一个SMILES字符串，例如：\nCCO\nCC(=O)OC1=CC=CC=C1C(=O)O\nCN1C=NC2=C1C(=O)N(C(=O)N2C)C',
                'parse-smiles': '解析SMILES',
                'clear-text': '清空文本',
                'smiles-input-hint': '每行输入一个SMILES字符串，解析后将显示分子列表',
                'task-history': '对接任务历史',
                'refresh': '刷新',
                'docking-params-title': '对接参数',
                'docking-params-desc': '设置分子对接的空间范围和计算参数',
                'pocket-center-coords': '口袋中心坐标',
                'x-coord': 'X 坐标',
                'y-coord': 'Y 坐标',
                'z-coord': 'Z 坐标',
                'auto-fill-coords': '自动填入',
                'manual-input-hint': '手动输入坐标或点击按钮从已选择的原子计算中心坐标',
                'docking-box-size': '对接盒子尺寸 (Å)',
                'x-size': 'X 尺寸',
                'y-size': 'Y 尺寸',
                'z-size': 'Z 尺寸',
                'box-size-hint': '定义搜索空间的尺寸，默认为15Å × 15Å × 15Å',
                'threads-label': '计算线程数',
                'validate-inputs': '验证输入',
                'start-docking': '开始对接',
                'wait-submit': '提交中...',
                'ok-submit': '提交成功',
                'reset-params': '重置参数',
                'docking-results': '对接结果',
                'waiting-results': '等待结果...',
                'hide-results-btn': '隐藏结果',
                'task-info': '任务信息',
                'task-id': '任务ID:',
                'task-status': '状态:',
                'results-details': '结果详情',
                'scores-tab': '结合能量',
                'files-tab': '文件下载',
                'model-extraction-title': '模型提取工具',
                'model-extraction-desc': '从对接结果文件中提取指定构象并转换为PDB格式',
                'select-pdbqt-file': '选择PDBQT对接结果文件:',
                'select-model-number': '选择构象编号:',
                'default-model-number': '(默认提取第1个构象)',
                'extract-and-convert': '提取并转换为PDB',
                'task-history': '对接任务历史',
                'refresh-tasks': '刷新',
                'local': '本地',
                'file-added': '已添加文件',
                'file-exists': '文件已存在',
                'protein-file-selected': '已选择蛋白质文件',
                'waiting-results': '等待结果...',
                'processing': '处理中...',
                'completed': '对接计算已完成',
                'failed': '计算失败',
                'running': '计算中...',
                'pending': '等待中...',
                'error': '发生错误',
                'success': '成功完成',
                'cancelled': '已取消',
                'timeout': '计算超时',
                'no-results': '暂无详细结果信息',
                'hide-results': '收起',
                'show-results': '展开',
                'view-task-details': '查看详情',
                'view-task-results': '查看结果',
                'created-time': '创建时间',
                'updated-time': '更新时间',
                'completed-time': '完成时间',
                'error-message': '错误信息',
                'parameters': '参数',

                // 任务结果描述
                'task-completed-summary': '任务 {taskId} 共完成了 {count} 个分子的对接计算。',
                'task-summary-keyword1': '共完成了',
                'task-summary-keyword2': '个分子的对接计算',
                'task-general-keyword1': '任务',
                'task-general-keyword2': '完成',
                'no-autodock-tasks': '暂无AutoDock任务历史',
                'no-scores-data': '暂无分数数据',
                'no-files-available': '暂无文件可下载',
                
                // 输入验证和错误信息
                'input-validation-failed': '输入验证失败：',
                'incomplete-pocket-coords': '请输入完整的口袋中心坐标',
                'validation-passed': '输入验证通过！',
                'batch-smiles-mode': '批量SMILES模式：将对接',
                'molecules': '个分子',
                'multi-file-mode-confirm': '多分子文件模式：将对接',
                'files': '个分子文件',
                'single-file-mode-confirm': '单分子文件模式',
                'single-smiles-mode': '单分子SMILES模式',
                
                // 提交和通知信息
                'docking-task-submitted': '对接任务已提交！任务ID:',
                'login-expired': '登录状态已过期，请重新登录',
                'login-abnormal': '登录状态异常，请重新登录',
                'docking-request-failed': '对接请求失败:',
                'task-results-unavailable': '任务结果不可用',
                'get-task-results-error': '获取任务结果出错',
                'get-task-details-failed': '获取任务详情失败:',
                'get-task-details-error': '获取任务详情出错',
                
                // CSV和SMILES相关
                'please-select-csv': '请选择 CSV 格式文件',
                'no-valid-molecules-in-csv': 'CSV 文件中没有找到有效的分子数据',
                'successfully-parsed-molecules': '成功解析',
                'csv-parse-failed': 'CSV 文件解析失败:',
                'text-input-area-not-found': '文本输入区域未找到',
                'please-input-smiles': '请输入 SMILES 字符串',
                'no-valid-smiles-found': '没有找到有效的 SMILES 字符串',
                'smiles-parse-failed': 'SMILES 文本解析失败:',
                
                // 确认对话框
                'batch-docking-confirm-title': '您即将开始批量分子对接：',
                'molecule-count': '• 分子数量：',
                'docking-mode': '• 对接模式：',
                'batch-smiles-file-mode': '批量SMILES文件',
                'batch-smiles-input-mode': '批量SMILES输入',
                'all-molecules-will-dock': '• 所有分子将同时进行对接',
                'confirm-continue': '确定要继续吗？',
                'multi-file-docking-confirm-title': '您即将开始多分子文件对接：',
                'molecule-file-count': '• 分子文件数量：',
                'multi-molecule-files-mode': '多分子结构文件',

                'prev-page': '上一页',
                'next-page': '下一页',
                'showing-items': '显示',
                'of-total-items': '条，共',
                'total-tasks': '条任务',
                
            },
            en: {
                'page-title': 'AutoDock High-throughput Screening and Molecular Docking',
                'page-subtitle': 'AutoDock is a widely used open-source molecular docking software suite for predicting the binding modes and affinities of small molecule ligands (such as drug candidates) with known three-dimensional structures of biological macromolecules (usually protein receptors). It helps researchers understand molecular interaction mechanisms and is widely used in drug discovery and virtual screening by simulating various possible conformations and orientations of ligands at receptor active sites and evaluating binding strength using energy scoring functions. The suite mainly includes the AutoDock program (performing docking calculations using Lamarckian genetic algorithms and empirical free energy scoring functions) and the AutoGrid program (pre-calculating grid maps to represent receptors).',
                'tutorial-btn': 'Platform Tutorial',
                'guide-title': '🎯 Docking Pocket Identification and Analysis Guide',
                'step-one-title': 'Step 1: Protein Structure Analysis',
                'step-one-item1': 'Upload protein file: First upload PDB or PDBQT format protein structure file',
                'step-one-item2': 'Observe overall structure: Rotate and zoom in the 3D viewer to familiarize with the overall protein folding structure',
                'step-one-item3': 'Find potential pockets: Look for concave areas on the protein surface, which are usually ligand binding sites',
                'step-one-item4': 'Focus on active sites: If active center residues are known, focus on observing these areas',
                'step-two-title': 'Step 2: Pocket Center Point Determination',
                'step-two-item1': 'Click atom selection: Click key atoms in the pocket (such as active site residues) in the 3D viewer',
                'step-two-item2': 'View atom information: Selected atom information will be displayed below, including residue type and coordinates',
                'step-two-item3': 'Set center point: Click atom information entries to directly set those coordinates as docking center',
                'step-two-item4': 'Multi-atom average: When multiple atoms are selected, the system will automatically calculate the geometric center',
                'step-three-title': 'Step 3: Docking Box Size Setting',
                'step-three-item1': 'Estimate pocket size: Observe the approximate size range of the target pocket',
                'step-three-item2': 'Consider ligand size: Ensure the docking box is large enough to accommodate various conformations of ligand molecules',
                'step-three-item3': 'Recommended sizes:',
                'size-small': 'Small molecule ligands: 15-20 Å',
                'size-peptide': 'Peptide ligands: 20-25 Å',
                'size-large': 'Large molecule ligands: 25-30 Å',
                'tips-title': 'Professional Advice and Precautions',
                'pocket-types-title': '🎪 Common Pocket Types:',
                'pocket-type1': 'Enzyme active sites (usually deep and narrow)',
                'pocket-type2': 'Protein-protein interaction interfaces (usually shallow and wide)',
                'pocket-type3': 'Allosteric regulatory sites (away from active center)',
                'precautions-title': '⚠️ Precautions:',
                'precaution1': 'Avoid setting the docking box too large, which will increase computation time',
                'precaution2': 'Ensure the box completely contains the target binding site',
                'precaution3': 'Consider protein flexibility, leaving space for side chain movement',
                'precaution4': 'If known complex structures are available, refer to their binding modes',
                'viewer-title': 'Molecular Structure Viewer',
                'loading': 'Loading...',
                'upload-btn': 'Click to Upload Structure Files',
                'supported-formats': 'Supported File Formats:',
                'pdb-desc': '• PDB files - Protein structures (cartoon rendering)',
                'sdf-desc': '• SDF files - Small molecule structures (ball-and-stick model)',
                'pdbqt-desc': '• PDBQT files - Charge structure files',
                'uploaded-files': 'Uploaded Files',
                'no-files': 'No files',
                'selected-atoms-info': 'Selected Atoms Information',
                'clear-all': 'Clear All',
                'atom-selection-guide': 'Atom Selection Instructions:',
                'atom-guide1': 'In the 3D molecular viewer above, click atoms on proteins or small molecules to select them',
                'atom-guide2': 'Selected atom information will be displayed in the list below, including atom type, residue information and coordinates',
                'atom-guide3': 'Click atom information entries below to directly set those atom coordinates as docking pocket center points',
                'atom-guide4': 'When multiple atoms are selected, the system will automatically calculate their geometric center as pocket center coordinates',
                'no-atoms': 'No atoms selected',
                'input-params-title': 'AutoDock Input Parameter Settings',
                'docking-files-selection': '📁 Docking Files Selection',
                'files-selection-desc': 'Select protein and small molecule files for molecular docking',
                'protein-receptor-file': '🧬 Protein Receptor File (PDB)',
                'uploaded-files-selection': 'Select from uploaded files',
                'no-protein-files': 'No uploaded protein files, please first upload PDB or PDBQT files in the molecular viewer above',
                'or': 'or',
                'upload-local-receptor': 'Upload Local Receptor File',
                'click-select-pdb': 'Click to select PDB/PDBQT files',
                'or-drag': 'or',
                'drag-files-here': 'drag files here',
                'supported-pdb-formats': 'Supported formats: PDB, PDBQT',
                'selected-protein-file': 'Selected Protein File',
                'clear-selection': 'Clear Selection',
                'protein-file-hint': 'Select a PDB or PDBQT format protein file as docking receptor',
                'ligand-file': '💊 Small Molecule Ligand File',
                'select-ligand-files': 'Select Ligand Molecule Structure Files',
                'batch-smiles-file': 'Batch SMILES File',
                'batch-smiles-input': 'Batch SMILES Input',
                'select-from-uploaded-ligands': 'Select from uploaded ligand molecule files',
                'no-ligand-files': 'No uploaded molecule files, please first upload files in the molecular viewer above',
                'upload-local-ligand': 'Upload Local Ligand Files',
                'click-select-files': 'Click to select files',
                'supported-ligand-formats': 'Supported formats: PDB, SDF, PDBQT, MOL2',
                'selected-files': 'Selected Files',
                'files-count': '',
                'file-selection-hint': 'Click uploaded files or upload local files to select',
                'single-file-mode': 'Select 1 file:',
                'single-file-desc': 'Use single molecule docking mode',
                'multi-file-mode': 'Select multiple files:',
                'multi-file-desc': 'Use multi-molecule batch docking mode',
                'upload-csv': 'Upload CSV File',
                'csv-format-desc': 'CSV format: first column for name, second column for smiles',
                'csv-example': 'Example: aspirin,CC(=O)OC1=CC=CC=C1C(=O)O',
                'molecule-list': 'Molecule List',
                'molecules-count': '',
                'batch-docking-info': '💊 All molecules will be used for batch docking',
                'search-placeholder': 'Search molecule names...',
                '2d-structure': '2D Structure',
                'csv-upload-hint': 'Upload CSV file containing molecule names and SMILES, all molecules will be automatically used for batch docking',
                'direct-smiles-input': 'Direct SMILES String Input',
                'smiles-placeholder': 'Enter one SMILES string per line, for example:\nCCO\nCC(=O)OC1=CC=CC=C1C(=O)O\nCN1C=NC2=C1C(=O)N(C(=O)N2C)C',
                'parse-smiles': 'Parse SMILES',
                'clear-text': 'Clear Text',
                'smiles-input-hint': 'Enter one SMILES string per line, molecule list will be displayed after parsing',
                'task-history': 'Docking Task History',
                'refresh': 'Refresh',
                'docking-params-title': 'Docking Parameters',
                'docking-params-desc': 'Set the spatial range and calculation parameters for molecular docking',
                'pocket-center-coords': 'Pocket Center Coordinates',
                'x-coord': 'X Coordinate',
                'y-coord': 'Y Coordinate',
                'z-coord': 'Z Coordinate',
                'auto-fill-coords': 'Auto Fill',
                'manual-input-hint': 'Manually input coordinates or click button to calculate center coordinates from selected atoms',
                'docking-box-size': 'Docking Box Size (Å)',
                'x-size': 'X size',
                'y-size': 'Y size',
                'z-size': 'Z size',
                'box-size-hint': 'Define the size of the search space, default is 15Å × 15Å × 15Å',
                'threads-label': 'Threads for computation',
                'validate-inputs': 'Validate Inputs',
                'start-docking': 'Start Docking',
                'wait-submit': 'Submitting...',
                'ok-submit': 'Submitted',
                'reset-params': 'Reset Parameters',
                'docking-results': 'Docking Results',
                'waiting-results': 'Waiting Results...',
                'hide-results-btn': 'Hide Results',
                'task-info': 'Task Information',
                'task-id': 'Task ID:',
                'task-status': 'Status:',
                'results-details': 'Results Details',
                'scores-tab': 'Scores',
                'files-tab': 'Files',
                'model-extraction-title': 'Model Extraction Tool',
                'model-extraction-desc': 'Extract specified conformations from docking results and convert to PDB format',
                'select-pdbqt-file': 'Select PDBQT docking results file:',
                'select-model-number': 'Select Conformation Number:',
                'default-model-number': '(Default extract the 1st conformation)',
                'extract-and-convert': 'Extract and Convert to PDB',
                'task-history': 'Docking Task History',
                'refresh-tasks': 'Refresh',
                'local': 'Local',
                'file-added': 'File added',
                'file-exists': 'File already exists',
                'protein-file-selected': 'Protein file selected',
                'waiting-results': 'Waiting Results...',
                'processing': 'Processing...',
                'completed': 'Docking calculation completed',
                'failed': 'Calculation failed',
                'running': 'Running...',
                'pending': 'Queued...',
                'error': 'Error occurred',
                'success': 'Successfully completed',
                'cancelled': 'Cancelled',
                'timeout': 'Calculation timeout',
                'no-results': 'No detailed result information',
                'hide-results': 'Hide',
                'show-results': 'Show',
                'view-task-details': 'View Task Details',
                'view-task-results': 'View Task Results',
                'created-time': 'Created Time',
                'updated-time': 'Updated Time',
                'completed-time': 'Completed Time',
                'error-message': 'Error Message',
                'parameters': 'Parameters',
                'task-completed-summary': 'Task {taskId} completed {count} docking calculations.',
                'task-summary-keyword1': 'completed',
                'task-summary-keyword2': 'docking calculations',
                'task-general-keyword1': 'Task',
                'task-general-keyword2': 'completed',
                'no-autodock-tasks': 'No AutoDock task history',
                'no-scores-data': 'No scores data',
                'no-files-available': 'No files available',
                        
                // Input validation and error messages
                'input-validation-failed': 'Input validation failed:',
                'incomplete-pocket-coords': 'Please enter complete pocket center coordinates',
                'validation-passed': 'Input validation passed!',
                'batch-smiles-mode': 'Batch SMILES mode: will dock',
                'molecules': 'molecules',
                'multi-file-mode-confirm': 'Multi-molecule file mode: will dock',
                'files': 'molecule files',
                'single-file-mode-confirm': 'Single molecule file mode',
                'single-smiles-mode': 'Single molecule SMILES mode',
                
                // Submit and notification messages
                'docking-task-submitted': 'Docking task submitted! Task ID:',
                'login-expired': 'Login status expired, please log in again',
                'login-abnormal': 'Login status abnormal, please log in again',
                'docking-request-failed': 'Docking request failed:',
                'task-results-unavailable': 'Task results unavailable',
                'get-task-results-error': 'Error getting task results',
                'get-task-details-failed': 'Failed to get task details:',
                'get-task-details-error': 'Error getting task details',
                
                // CSV and SMILES related
                'please-select-csv': 'Please select CSV format file',
                'no-valid-molecules-in-csv': 'No valid molecule data found in CSV file',
                'successfully-parsed-molecules': 'Successfully parsed',
                'csv-parse-failed': 'CSV file parsing failed:',
                'text-input-area-not-found': 'Text input area not found',
                'please-input-smiles': 'Please input SMILES strings',
                'no-valid-smiles-found': 'No valid SMILES strings found',
                'smiles-parse-failed': 'SMILES text parsing failed:',
                
                // Confirmation dialogs
                'batch-docking-confirm-title': 'You are about to start batch molecular docking:',
                'molecule-count': '• Number of molecules:',
                'docking-mode': '• Docking mode:',
                'batch-smiles-file-mode': 'Batch SMILES file',
                'batch-smiles-input-mode': 'Batch SMILES input',
                'all-molecules-will-dock': '• All molecules will be docked simultaneously',
                'confirm-continue': 'Are you sure you want to continue?',
                'multi-file-docking-confirm-title': 'You are about to start multi-molecule file docking:',
                'molecule-file-count': '• Number of molecule files:',
                'multi-molecule-files-mode': 'Multi-molecule structure files',
                
                'prev-page': 'Previous',
                'next-page': 'Next',
                'showing-items': 'Showing',
                'of-total-items': 'of',
                'total-tasks': 'tasks',
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            
            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.lang === lang) {
                    btn.classList.add('active');
                }
            });
            
            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            // 更新文档标题
            document.title = lang === 'zh' ? 'AutoDock - 分子设计平台' : 'AutoDock - Molecular Design Platform';
            
            // 更新页面文本
            updatePageText(lang);

            // 更新任务状态和描述
            updateTaskStatusDisplay();
            
            // 更新登录时间显示
            const loginTime = localStorage.getItem('loginTime');

            // 更新人类可读结果显示
            updateHumanReadableResults();

            // 刷新任务列表以应用新语言
            if (typeof loadTaskHistory === 'function') {
                loadTaskHistory();
            }

            // 触发语言切换事件，通知show.js更新
            window.dispatchEvent(new CustomEvent('languageChanged', {
                detail: { language: lang }
            }));
            
            console.log('Language switched to:', lang);
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];
            
            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t && t[key]) {
                    // 处理包含HTML结构的文本
                    if (t[key].includes('<strong>') || t[key].includes('<br>')) {
                        element.innerHTML = t[key];
                    } else {
                        element.textContent = t[key];
                    }
                }
            });
        }

        // 修改现有的checkLoginStatus函数，添加多语言支持
        async function checkLoginStatus() {
            console.log('AutoDock: Checking login status...');
            
            try {
                // 首先检查后端认证状态
                const response = await fetch('/api/auth/check', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                console.log('AutoDock: Backend auth check result:', data);
                
                if (data.logged_in) {
                    // 后端确认已登录，更新localStorage
                    localStorage.setItem('isLoggedIn', 'true');
                    if (data.user && data.user.username) {
                        localStorage.setItem('username', data.user.username);
                    }
                    if (data.user && data.user.loginTime) {
                        localStorage.setItem('loginTime', data.user.loginTime);
                    }
                    
                    // 显示用户信息
                    const username = data.user?.username || localStorage.getItem('username');
                    
                    if (username) {
                        document.getElementById('username').textContent = username;
                        document.getElementById('userAvatar').textContent = username.substring(0,3).toUpperCase();
                    }
                    
                    
                    console.log('AutoDock: Login status verified, user logged in');
                    return true;
                } else {
                    // 后端确认未登录，清除localStorage并跳转
                    console.log('AutoDock: Backend confirms user not logged in:', data.message);
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login';
                    return false;
                }
            } catch (error) {
                console.error('AutoDock: Error checking login status:', error);
                
                // 网络错误时，回退到localStorage检查
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                if (isLoggedIn !== 'true') {
                    console.log('AutoDock: Fallback - localStorage indicates not logged in');
                    window.location.href = 'login';
                    return false;
                }
                
                // 显示localStorage中的用户信息
                const username = localStorage.getItem('username');
                
                if (username) {
                    document.getElementById('username').textContent = username;
                    document.getElementById('userAvatar').textContent = username.substring(0,3).toUpperCase();
                }
                
                
                console.log('AutoDock: Fallback - using localStorage, assuming logged in');
                return true;
            }
        }

        // 在页面初始化时添加语言切换功能
        let isInitialized = false;
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AutoDock: Page loading...');
            // 防止重复初始化
            if (isInitialized) {
                console.log('AutoDock: Already initialized, skipping...');
                return;
            }
            isInitialized = true;
            
            // 先初始化语言切换功能
            currentLanguage = localStorage.getItem('language') || 'en';
            switchLanguage(currentLanguage);
            console.log('current language:', currentLanguage);
            
            // 然后执行其他初始化
            checkLoginStatus().then(loggedIn => {

                // 重要：在这里再次确保语言设置，然后更新登录时间
                const savedLanguage = localStorage.getItem('language') || 'zh';
                if (currentLanguage !== savedLanguage) {
                    console.log('AutoDock: Language mismatch detected, correcting...');
                    currentLanguage = savedLanguage;
                }
                const loginTime = localStorage.getItem('loginTime');
                 if (loginTime) {
                     console.log('AutoDock: Updating login time after language initialization');
                    // 使用setTimeout确保DOM完全加载
                    setTimeout(() => {
                        const finalLanguage = localStorage.getItem('language') || 'zh';
                         if (currentLanguage !== finalLanguage) {
                            console.log('AutoDock: Final language correction from', currentLanguage, 'to', finalLanguage);
                            currentLanguage = finalLanguage;
                        }
                     }, 600);
                 }
                if (loggedIn) {
                    // 只有登录后才初始化其他功能
                    console.log('AutoDock: User is logged in, initializing features...');
                    initializeMultiFileSelection();
                    initializeProteinFileSelection();
                    initializeButtonEvents();
                    // 设置文件上传监听器
                    setupFileUploadListener();
                    loadTaskHistory();
                    initializeTabs();

                    // 确保show.js已加载并初始化分子查看器
                    if (typeof window.MoleculeViewer !== 'undefined' && window.MoleculeViewer.initialize) {
                        try {
                            window.MoleculeViewer.initialize();
                            console.log('AutoDock: Molecule viewer initialized successfully');
                        } catch (error) {
                            console.error('AutoDock: Error initializing molecule viewer:', error);
                        }
                    } else {
                        console.warn('AutoDock: MoleculeViewer not found, retrying in 1 second...');
                        setTimeout(function() {
                            if (typeof window.MoleculeViewer !== 'undefined' && window.MoleculeViewer.initialize) {
                                try {
                                    window.MoleculeViewer.initialize();
                                    console.log('AutoDock: Molecule viewer initialized successfully (retry)');
                                } catch (error) {
                                    console.error('AutoDock: Error initializing molecule viewer (retry):', error);
                                }
                            } else {
                                console.error('AutoDock: MoleculeViewer still not available after retry');
                            }
                        }, 1000);
                    }
                    console.log('AutoDock: All features initialized');
                }
            });

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });
        });
        // ================ 多语言切换功能结束 ================


        // 全局变量
        let batchMolecules = [];
        let refreshInterval = null;

        // 全局变量用于存储更新函数引用
        let globalUpdateUploadedFilesGrid = null;
        let globalUpdateProteinFilesGrid = null;


        // 初始化标签页切换功能
        function initializeTabs() {
            console.log('AutoDock: Initializing tabs...');
            
            const ligandTabs = document.querySelectorAll('.ligand-tab');
            const ligandTabContents = document.querySelectorAll('.ligand-tab-content');
            
            ligandTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    console.log('AutoDock: Tab clicked:', this.getAttribute('data-tab'));
                    
                    // 移除所有标签页的活动状态
                    ligandTabs.forEach(t => t.classList.remove('active'));
                    ligandTabContents.forEach(content => content.classList.remove('active'));
                    
                    // 激活当前标签页
                    this.classList.add('active');
                    
                    // 显示对应的内容
                    const tabType = this.getAttribute('data-tab');
                    const tabContent = document.getElementById(`${tabType}-tab-content`);
                    if (tabContent) {
                        tabContent.classList.add('active');
                        console.log('AutoDock: Activated tab content:', tabType);
                    }
                });
            });
            
            // 初始化结果区域的标签页切换
            const resultTabs = document.querySelectorAll('.tab-btn');
            const resultTabContents = document.querySelectorAll('.tab-content');
            
            resultTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    console.log('AutoDock: Result tab clicked:', this.getAttribute('data-tab'));
                    
                    // 移除所有结果标签页的活动状态
                    resultTabs.forEach(t => t.classList.remove('active'));
                    resultTabContents.forEach(content => content.classList.remove('active'));
                    
                    // 激活当前标签页
                    this.classList.add('active');
                    
                    // 显示对应的内容
                    const tabType = this.getAttribute('data-tab');
                    const tabContent = document.getElementById(`${tabType}-tab`);
                    if (tabContent) {
                        tabContent.classList.add('active');
                        console.log('AutoDock: Activated result tab content:', tabType);
                    }
                });
            });
            
            // 初始化多文件选择功能
            // initializeMultiFileSelection();
            
            // 初始化蛋白质文件选择功能
            // initializeProteinFileSelection();
            
            console.log('AutoDock: Tabs initialized successfully');
        }


        // 初始化多文件选择功能
        function initializeMultiFileSelection() {
            console.log('AutoDock: Initializing new file selection interface...');
            
            // 获取界面元素
            const uploadedFilesGrid = document.getElementById('uploaded-files-grid');
            const localLigandInput = document.getElementById('local-ligand-input');
            const ligandDropZone = document.getElementById('ligand-drop-zone');
            const selectedFilesDisplay = document.getElementById('selected-files-display');
            const selectedFilesList = document.getElementById('selected-files-list');
            const selectedFilesCount = document.getElementById('selected-files-count');
            const clearSelectionBtn = document.getElementById('clear-selection-btn');
            
            // 选中的文件数组
            let selectedFiles = [];
            
            // 更新已上传文件网格
            function updateUploadedFilesGrid() {
                if (!uploadedFilesGrid) return;
                
                if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
                    // 过滤出小分子文件
                    const ligandFiles = uploadedFilesData.filter(file => 
                        ['sdf', 'pdb', 'pdbqt', 'mol2'].includes(file.type.toLowerCase())
                    );
                    
                    if (ligandFiles.length > 0) {
                        uploadedFilesGrid.innerHTML = '';
                        ligandFiles.forEach(file => {
                            const fileCard = document.createElement('div');
                            fileCard.className = 'file-card';
                            fileCard.dataset.fileId = file.id;
                            fileCard.innerHTML = `
                                <div class="file-card-header">
                                    <span class="file-type-icon">${getFileTypeIcon(file.type)}</span>
                                    <span class="file-name" title="${file.name}">${file.name}</span>
                                </div>
                                <div class="file-info">${file.type.toUpperCase()} • ${formatFileSize(file.size || 0)}</div>
                                <div class="selection-indicator">✓</div>
                            `;
                            
                            // 添加点击事件
                            fileCard.addEventListener('click', function() {
                                toggleFileSelection(file, fileCard);
                            });
                            
                            uploadedFilesGrid.appendChild(fileCard);
                        });
                    } else {
                        uploadedFilesGrid.innerHTML = `<div class="no-files-message"><span data-i18n="no-ligand-files">${translations[currentLanguage]['no-ligand-files'] || '暂无已上传的分子文件，请先在上方分子查看器中上传文件'}</span></div>`;
                    }
                } else {
                    uploadedFilesGrid.innerHTML = `<div class="no-files-message"><span data-i18n="no-ligand-files">${translations[currentLanguage]['no-ligand-files'] || '暂无已上传的分子文件，请先在上方分子查看器中上传文件'}</span></div>`;
                }
            }
            
            // 将函数引用存储到全局变量
            globalUpdateUploadedFilesGrid = updateUploadedFilesGrid;
            
            // 切换文件选择状态
            function toggleFileSelection(file, fileCard) {
                const existingIndex = selectedFiles.findIndex(f => f.id === file.id);
                
                if (existingIndex >= 0) {
                    // 取消选择
                    selectedFiles.splice(existingIndex, 1);
                    fileCard.classList.remove('selected');
                } else {
                    // 选择文件
                    selectedFiles.push({
                        id: file.id,
                        name: file.name,
                        type: file.type,
                        content: file.content,
                        source: 'uploaded'
                    });
                    fileCard.classList.add('selected');
                }
                
                updateSelectedFilesDisplay();
            }
            
            // 本地文件上传处理
            if (localLigandInput && ligandDropZone) {
                // 点击上传区域
                ligandDropZone.addEventListener('click', function() {
                    localLigandInput.click();
                });
                
                // 文件选择处理
                localLigandInput.addEventListener('change', function(e) {
                    handleLocalFileUpload(e.target.files);
                });
                
                // 拖拽上传处理
                ligandDropZone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });
                
                ligandDropZone.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });
                
                ligandDropZone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    handleLocalFileUpload(e.dataTransfer.files);
                });
            }
            
            // 处理本地文件上传
            function handleLocalFileUpload(files) {
                Array.from(files).forEach(file => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const fileData = {
                            id: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                            name: file.name,
                            type: getFileType(file.name),
                            content: e.target.result,
                            source: 'local'
                        };
                        
                        // 检查是否已经选择了这个文件
                        const exists = selectedFiles.some(f => f.name === file.name && f.source === 'local');
                        if (!exists) {
                            selectedFiles.push(fileData);
                            updateSelectedFilesDisplay();
                            showNotification(`${translations[currentLanguage]['file-added'] || '已添加文件'}: ${file.name}`, 'success');
                        } else {
                            showNotification(`${translations[currentLanguage]['file-exists'] || '文件已存在'}: ${file.name}`, 'warning');
                        }
                    };
                    reader.readAsText(file);
                });
                
                // 清空input
                localLigandInput.value = '';
            }
            
            // 更新已选文件显示
            function updateSelectedFilesDisplay() {
                if (!selectedFilesDisplay || !selectedFilesList || !selectedFilesCount) return;
                
                if (selectedFiles.length > 0) {
                    selectedFilesDisplay.style.display = 'block';
                    selectedFilesCount.textContent = selectedFiles.length;
                    
                    selectedFilesList.innerHTML = '';
                    selectedFiles.forEach((file, index) => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'selected-file-item';
                        const localText = file.source === 'local' ? 
                        ` - ${translations[currentLanguage]['local'] || '本地'}` : '';
                    fileItem.innerHTML = `
                        <span class="file-name">${file.name} (${file.type.toUpperCase()})${localText}</span>
                        <button class="remove-file-btn" data-index="${index}">×</button>
                    `;
                    selectedFilesList.appendChild(fileItem);
                    });
                    
                    // 添加删除按钮事件
                    selectedFilesList.querySelectorAll('.remove-file-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const index = parseInt(this.getAttribute('data-index'));
                            const removedFile = selectedFiles[index];
                            
                            // 从选中列表中移除
                            selectedFiles.splice(index, 1);
                            
                            // 如果是已上传文件，更新网格显示
                            if (removedFile.source === 'uploaded') {
                                const fileCard = uploadedFilesGrid.querySelector(`[data-file-id="${removedFile.id}"]`);
                                if (fileCard) {
                                    fileCard.classList.remove('selected');
                                }
                            }
                            
                            updateSelectedFilesDisplay();
                        });
                    });
                } else {
                    selectedFilesDisplay.style.display = 'none';
                }
            }
            
            // 清空选择按钮
            if (clearSelectionBtn) {
                clearSelectionBtn.addEventListener('click', function() {
                    selectedFiles = [];
                    
                    // 清除网格中的选中状态
                    uploadedFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                        card.classList.remove('selected');
                    });
                    
                    updateSelectedFilesDisplay();
                });
            }
            
            // 初始化时更新文件网格
            updateUploadedFilesGrid();
            
            // 监听文件上传更新
            const originalUpdateFileSelectors = window.updateFileSelectors;
            window.updateFileSelectors = function() {
                if (originalUpdateFileSelectors) {
                    originalUpdateFileSelectors.call(this);
                }
                updateUploadedFilesGrid();
            };
            
            // 提供全局访问接口
            window.getSelectedLigandFiles = function() {
                return selectedFiles;
            };
            
            console.log('AutoDock: New file selection interface initialized');
        }
        
        // 获取文件类型图标
        function getFileTypeIcon(type) {
            const icons = {
                'pdb': '🧬',
                'sdf': '💊',
                'pdbqt': '⚗️',
                'mol2': '🔬'
            };
            return icons[type.toLowerCase()] || '📄';
        }
        
        // 获取文件类型
        function getFileType(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            return ext;
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 检查登录状态
        async function checkLoginStatus() {
            console.log('AutoDock: Checking login status...');
            
            try {
                // 首先检查后端认证状态
                const response = await fetch('/api/auth/check', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                console.log('AutoDock: Backend auth check result:', data);
                
                if (data.logged_in) {
                    // 后端确认已登录，更新localStorage
                    localStorage.setItem('isLoggedIn', 'true');
                    if (data.user && data.user.username) {
                        localStorage.setItem('username', data.user.username);
                    }
                    if (data.user && data.user.loginTime) {
                        localStorage.setItem('loginTime', data.user.loginTime);
                    }
                    
                    // 显示用户信息
                    const username = data.user?.username || localStorage.getItem('username');
                    const loginTime = data.user?.loginTime || localStorage.getItem('loginTime');
                    
                    if (username) {
                        document.getElementById('username').textContent = username;
                        document.getElementById('userAvatar').textContent = username.substring(0,3).toUpperCase();
                    }
                    
                    if (loginTime) {
                        const loginDate = new Date(loginTime);
                        const now = new Date();
                        const diffMinutes = Math.floor((now - loginDate) / (1000 * 60));
                        
                        let timeText;
                        if (diffMinutes < 1) {
                            timeText = '刚刚登录';
                        } else if (diffMinutes < 60) {
                            timeText = `${diffMinutes}分钟前登录`;
                        } else {
                            const diffHours = Math.floor(diffMinutes / 60);
                            timeText = `${diffHours}小时前登录`;
                        }
                        
                        document.getElementById('loginTime').textContent = timeText;
                    }
                    
                    console.log('AutoDock: Login status verified, user logged in');
                    return true;
                } else {
                    // 后端确认未登录，清除localStorage并跳转
                    console.log('AutoDock: Backend confirms user not logged in:', data.message);
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login';
                    return false;
                }
            } catch (error) {
                console.error('AutoDock: Error checking login status:', error);
                
                // 网络错误时，回退到localStorage检查
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                if (isLoggedIn !== 'true') {
                    console.log('AutoDock: Fallback - localStorage indicates not logged in');
                    window.location.href = 'login';
                    return false;
                }
                
                // 显示localStorage中的用户信息
                const username = localStorage.getItem('username');
                const loginTime = localStorage.getItem('loginTime');
                
                if (username) {
                    document.getElementById('username').textContent = username;
                    document.getElementById('userAvatar').textContent = username.substring(0,3).toUpperCase();
                }
                
                if (loginTime) {
                    const loginDate = new Date(loginTime);
                    const now = new Date();
                    const diffMinutes = Math.floor((now - loginDate) / (1000 * 60));
                    
                    let timeText;
                    if (diffMinutes < 1) {
                        timeText = '刚刚登录';
                    } else if (diffMinutes < 60) {
                        timeText = `${diffMinutes}分钟前登录`;
                    } else {
                        const diffHours = Math.floor(diffMinutes / 60);
                        timeText = `${diffHours}小时前登录`;
                    }
                    
                    document.getElementById('loginTime').textContent = timeText;
                }
                
                console.log('AutoDock: Fallback - using localStorage, assuming logged in');
                return true;
            }
        }

        // 退出登录
        function logout() {
            // 清除定期刷新
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
            
            fetch('/api/logout', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Server session ended');
                    } else {
                        console.error('Server logout failed:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error during server logout:', error);
                })
                .finally(() => {
                    // Always clear client-side storage and redirect
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login';
                });
        }

        // 更新文件选择下拉框
        function updateFileSelectors() {
            console.log('AutoDock: Updating file selectors');
            console.log('AutoDock: uploadedFilesData available:', typeof uploadedFilesData !== 'undefined');
            console.log('AutoDock: uploadedFilesData length:', typeof uploadedFilesData !== 'undefined' ? uploadedFilesData.length : 'N/A');
            if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
                console.log('AutoDock: uploadedFilesData contents:', uploadedFilesData);
            }
            
            const proteinSelect = document.getElementById('protein-select');
            const ligandSelect = document.getElementById('ligand-select');
            
            if (!proteinSelect || !ligandSelect) {
                console.log('AutoDock: File selectors not found, will retry later');
                return;
            }

            // 清空现有选项（保留默认选项）
            proteinSelect.innerHTML = '<option value="">请选择蛋白质文件...</option>';
            ligandSelect.innerHTML = '<option value="">请选择小分子文件...</option>';

            // 检查是否有全局的文件数据
            if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
                console.log('AutoDock: Found uploaded files:', uploadedFilesData.length);
                
                uploadedFilesData.forEach((fileData, index) => {
                    console.log(`AutoDock: Processing file ${index + 1}:`, {
                        id: fileData.id,
                        name: fileData.name,
                        type: fileData.type
                    });
                    
                    const proteinOption = document.createElement('option');
                    proteinOption.value = fileData.id;
                    proteinOption.textContent = `${fileData.name} (${fileData.type.toUpperCase()})`;
                    
                    const ligandOption = document.createElement('option');
                    ligandOption.value = fileData.id;
                    ligandOption.textContent = `${fileData.name} (${fileData.type.toUpperCase()})`;
                    
                    // 根据文件类型添加到对应的选择框
                    if (fileData.type === 'pdb') {
                        // PDB文件既可以作为蛋白质，也可以作为小分子
                        proteinSelect.appendChild(proteinOption);
                        ligandSelect.appendChild(ligandOption);
                        console.log(`AutoDock: Added PDB file to both selectors: ${fileData.name}`);
                    } else if (fileData.type === 'sdf' || fileData.type === 'mol2') {
                        ligandSelect.appendChild(ligandOption);
                        console.log(`AutoDock: Added ${fileData.type.toUpperCase()} file to ligand selector: ${fileData.name}`);
                    } else if (fileData.type === 'pdbqt') {
                        // PDBQT文件通常是对接结果，可以作为小分子文件
                        ligandSelect.appendChild(ligandOption);
                        console.log(`AutoDock: Added PDBQT file to ligand selector: ${fileData.name}`);
                    } else {
                        console.log(`AutoDock: Skipped file with unsupported type: ${fileData.name} (${fileData.type})`);
                    }
                });
                
                console.log(`AutoDock: Final protein select options count: ${proteinSelect.options.length}`);
                console.log(`AutoDock: Final ligand select options count: ${ligandSelect.options.length}`);
            } else {
                console.log('AutoDock: No uploaded files found or uploadedFilesData is undefined');
            }
        }

        // 监听文件上传事件
        function setupFileUploadListener() {
            // 定期检查文件列表更新
            let lastFileCount = 0;
            setInterval(() => {
                if (typeof uploadedFilesData !== 'undefined') {
                    if (uploadedFilesData.length !== lastFileCount) {
                        console.log('AutoDock: File list changed, updating selectors');
                        lastFileCount = uploadedFilesData.length;
                        updateFileSelectors();
                        
                        // 同时更新新的文件网格界面
                        if (globalUpdateUploadedFilesGrid) {
                            globalUpdateUploadedFilesGrid();
                        }
                        
                        // 更新蛋白质文件网格
                        if (globalUpdateProteinFilesGrid) {
                            globalUpdateProteinFilesGrid();
                        }
                    }
                }
            }, 1000); // 每秒检查一次
            
            // 立即执行一次更新
            setTimeout(() => {
                updateFileSelectors();
                if (globalUpdateUploadedFilesGrid) {
                    globalUpdateUploadedFilesGrid();
                }
                if (globalUpdateProteinFilesGrid) {
                    globalUpdateProteinFilesGrid();
                }
            }, 100);
        }

        // 获取选中的文件数据
        function getSelectedFileData(fileId) {
            if (typeof uploadedFilesData !== 'undefined') {
                return uploadedFilesData.find(file => file.id === fileId);
            }
            return null;
        }

        // 验证输入参数
        function validateInputs() {
            const pocketX = document.getElementById('pocket-x');
            const pocketY = document.getElementById('pocket-y');
            const pocketZ = document.getElementById('pocket-z');

            // 获取当前激活的tab
            const activeTab = getActiveLigandTab();
            const isBatchMode = (activeTab === 'batch-file' || activeTab === 'batch-text') && batchMolecules && batchMolecules.length > 0;

            let isValid = true;
            let errors = [];

            // 检查蛋白质文件（使用新的文件选择接口）
            const selectedProteinFile = window.getSelectedProteinFile ? window.getSelectedProteinFile() : null;
            if (!selectedProteinFile) {
                errors.push('请选择蛋白质文件');
                isValid = false;
            }

            // 根据激活的tab检查小分子输入
            switch(activeTab) {
                case 'files':
                    // 新的文件选择模式：使用getSelectedLigandFiles获取选中文件
                    const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
                    if (selectedFiles.length === 0) {
                        errors.push('请选择一个或多个分子结构文件');
                        isValid = false;
                    }
                    break;
                case 'batch-file':
                case 'batch-text':
                    // 批量SMILES模式：检查是否有批量分子数据
                    if (!batchMolecules || batchMolecules.length === 0) {
                        if (activeTab === 'batch-file') {
                            errors.push('请上传包含SMILES的CSV文件');
                        } else {
                            errors.push('请输入SMILES文本并解析');
                        }
                        isValid = false;
                    }
                    break;
            }

            // 检查口袋坐标
            if (!pocketX.value || !pocketY.value || !pocketZ.value) {
                errors.push(translations[currentLanguage]['incomplete-pocket-coords']);
                isValid = false;
            }

            if (!isValid) {
                alert(translations[currentLanguage]['input-validation-failed'] + '\n' + errors.join('\n'));
            } else {
                let message = translations[currentLanguage]['validation-passed'];
                if (isBatchMode) {
                    message += `\n${translations[currentLanguage]['batch-smiles-mode']} ${batchMolecules.length} ${translations[currentLanguage]['molecules']}`;
                } else if (activeTab === 'files') {
                    const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
                    
                    if (selectedFiles.length > 1) {
                        message += `\n${translations[currentLanguage]['multi-file-mode-confirm']} ${selectedFiles.length} ${translations[currentLanguage]['files']}`;
                    } else if (selectedFiles.length === 1) {
                        message += '\n' + translations[currentLanguage]['single-file-mode-confirm'];
                    }
                } else {
                    message += '\n' + translations[currentLanguage]['single-smiles-mode'];
                }
                alert(message);
            }

            return isValid;
        }

        // 准备对接数据
        function prepareData() {
            // 使用新的蛋白质文件选择接口
            const selectedProteinFile = window.getSelectedProteinFile ? window.getSelectedProteinFile() : null;

            // 获取当前激活的tab
            const activeTab = getActiveLigandTab();
            const isBatchMode = (activeTab === 'batch-file' || activeTab === 'batch-text') && batchMolecules && batchMolecules.length > 0;
            
            // 准备发送到后端的数据
            const requestData = {
                receptor: selectedProteinFile ? {
                    name: selectedProteinFile.name,
                    content: selectedProteinFile.content,
                    type: selectedProteinFile.type
                } : null,
                center_x: parseFloat(document.getElementById('pocket-x').value),
                center_y: parseFloat(document.getElementById('pocket-y').value),
                center_z: parseFloat(document.getElementById('pocket-z').value),
                size_x: parseInt(document.getElementById('size-x').value),
                size_y: parseInt(document.getElementById('size-y').value),
                size_z: parseInt(document.getElementById('size-z').value),
                thread: parseInt(document.getElementById('threads').value)
            };

            // 根据激活的tab处理配体数据
            switch(activeTab) {
                case 'files':
                    // 新的文件选择模式：使用getSelectedLigandFiles获取选中文件
                    const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
                    
                    if (selectedFiles.length > 1) {
                        // 多文件模式：使用ligands参数（文件列表）
                        requestData.ligands = selectedFiles.map(file => ({
                            name: file.name,
                            content: file.content,
                            type: file.type
                        }));
                        console.log(`多文件模式：选择了 ${selectedFiles.length} 个分子文件`);
                    } else if (selectedFiles.length === 1) {
                        // 单文件模式：使用ligand参数（单个文件）
                        const file = selectedFiles[0];
                        requestData.ligand = {
                            name: file.name,
                            content: file.content,
                            type: file.type
                        };
                        console.log('单文件模式：选择了 1 个分子文件');
                    }
                    break;
                case 'batch-file':
                case 'batch-text':
                    // 批量SMILES模式：将所有分子的SMILES用换行符连接
                    if (batchMolecules && batchMolecules.length > 0) {
                        const allSmiles = batchMolecules.map(molecule => molecule.smiles).join('\n');
                        requestData.smiles = allSmiles;
                        console.log(`批量SMILES模式（${activeTab === 'batch-file' ? '文件' : '输入'}）：包含 ${batchMolecules.length} 个分子`);
                    }
                    break;
            }

            console.log('Prepared data:', requestData);
            return requestData;
        }

        // 开始对接
        async function startDocking() {
            console.log('=== StartDocking Debug ===');
            
            // 验证输入
            if (!validateInputs()) {
                return;
            }

            // 获取当前激活的tab并检查是否在批量SMILES模式
            const activeTab = getActiveLigandTab();
            const isBatchMode = (activeTab === 'batch-file' || activeTab === 'batch-text') && batchMolecules && batchMolecules.length > 0;
            
            // 检查是否是多文件模式
            const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
            const isMultiFileMode = activeTab === 'files' && selectedFiles.length > 1;
            
            if (isBatchMode) {
                const tabName = activeTab === 'batch-file' ? translations[currentLanguage]['batch-smiles-file-mode'] : translations[currentLanguage]['batch-smiles-input-mode'];
                const confirmMessage = translations[currentLanguage]['batch-docking-confirm-title'] + '\n\n' +
                    translations[currentLanguage]['molecule-count'] + batchMolecules.length + ' ' + translations[currentLanguage]['molecules'] + '\n' +
                    translations[currentLanguage]['docking-mode'] + tabName + '\n' +
                    translations[currentLanguage]['all-molecules-will-dock'] + '\n\n' +
                    translations[currentLanguage]['confirm-continue'];
                
                if (!confirm(confirmMessage)) {
                    return;
                }
            } else if (isMultiFileMode) {
                const confirmMessage = translations[currentLanguage]['multi-file-docking-confirm-title'] + '\n\n' +
                    translations[currentLanguage]['molecule-file-count'] + selectedFiles.length + ' ' + translations[currentLanguage]['files'] + '\n' +
                    translations[currentLanguage]['docking-mode'] + translations[currentLanguage]['multi-molecule-files-mode'] + '\n' +
                    translations[currentLanguage]['all-molecules-will-dock'] + '\n\n' +
                    translations[currentLanguage]['confirm-continue'];
                
                if (!confirm(confirmMessage)) {
                    return;
                }
            }

            // 准备数据
            const data = prepareData();
            if (!data) {
                return;
            }

            // 更新UI状态
            const submitBtn = document.getElementById('submit-btn');
            const originalHTML = submitBtn.innerHTML; // 保存原始HTML结构
            const originalText = submitBtn.textContent;
            // 设置提交中状态
            submitBtn.textContent = translations[currentLanguage]["wait-submit"];
            submitBtn.disabled = true;

            try {
                console.log('发送对接请求...');
                const response = await fetch('/api/autodock', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                console.log('收到响应:', result);

                if (response.ok && result.success) {
                    // 异步任务已创建
                    console.log('任务创建成功:', result.task_id);
                    
                    // 显示任务创建成功的消息
                    showNotification(`${translations[currentLanguage]['docking-task-submitted']} ${result.task_id}`, 'success');
                    
                    // 刷新任务列表
                    if (typeof loadTaskHistory === 'function') {
                    loadTaskHistory();
                    }
                    
                    // 重置表单
                    resetForm();
                    
                } else {
                    // 处理认证失败的情况
                    if (response.status === 401) {
                        console.log('认证失败，跳转到登录页面');
                        showNotification(translations[currentLanguage]['login-expired'], 'warning');
                        // 清除本地登录状态
                        localStorage.removeItem('isLoggedIn');
                        localStorage.removeItem('username');
                        localStorage.removeItem('loginTime');
                        // 延迟跳转，让用户看到提示信息
                        setTimeout(() => {
                            window.location.href = 'login';
                        }, 2000);
                        return;
                    }
                    
                    throw new Error(result.message || '对接任务创建失败');
                }

            } catch (error) {
                console.error('对接请求失败:', error);
                
                // 检查是否是网络错误或认证问题
                if (error.message.includes('登录') || error.message.includes('认证') || error.message.includes('过期')) {
                    showNotification(translations[currentLanguage]['login-abnormal'], 'warning');
                    setTimeout(() => {
                        window.location.href = 'login';
                    }, 2000);
                } else {
                    showNotification(translations[currentLanguage]['docking-request-failed'] + ' ' + error.message, 'error');
                }
            } finally {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            // 添加样式
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                max-width: 400px;
                word-wrap: break-word;
                transition: all 0.3s ease;
            `;
            
            // 根据类型设置颜色
            switch (type) {
                case 'success':
                    notification.style.background = '#10b981';
                    break;
                case 'error':
                    notification.style.background = '#ef4444';
                    break;
                case 'warning':
                    notification.style.background = '#f59e0b';
                    break;
                default:
                    notification.style.background = '#3b82f6';
            }
            
            // 添加到页面
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 重置表单
        function resetForm() {
            // 重置基本输入
            document.getElementById('pocket-x').value = '';
            document.getElementById('pocket-y').value = '';
            document.getElementById('pocket-z').value = '';
            document.getElementById('size-x').value = '15';
            document.getElementById('size-y').value = '15';
            document.getElementById('size-z').value = '15';
            document.getElementById('threads').value = '2000';
            
            // 清空蛋白质文件选择
            const clearProteinBtn = document.getElementById('clear-protein-selection-btn');
            if (clearProteinBtn) {
                clearProteinBtn.click();
            }
            
            // 清空所有tab的数据
            clearSingleInputs();
            if (typeof clearBatchInputs === 'function') {
            clearBatchInputs();
            }
            
            // 切换回第一个tab
            const tabButtons = document.querySelectorAll('.ligand-tab');
            const tabContents = document.querySelectorAll('.ligand-tab-content');
            
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 激活第一个tab
            const firstTab = document.querySelector('.ligand-tab[data-tab="files"]');
            const firstContent = document.getElementById('files-tab-content');
            
            if (firstTab) firstTab.classList.add('active');
            if (firstContent) firstContent.classList.add('active');
        }

        // 重置单个输入
        function clearSingleInputs() {
            // 清除新的文件选择界面的选择状态
            const clearSelectionBtn = document.getElementById('clear-selection-btn');
            if (clearSelectionBtn) {
                clearSelectionBtn.click();
            }
        }

        // 重置批量输入
        function clearBatchInputs() {
            document.getElementById('csv-file-input').value = '';
            document.getElementById('molecule-search').value = '';
        }

        // 获取当前激活的tab
        function getActiveLigandTab() {
            const tabButtons = document.querySelectorAll('.ligand-tab');
            for (let i = 0; i < tabButtons.length; i++) {
                if (tabButtons[i].classList.contains('active')) {
                    return tabButtons[i].getAttribute('data-tab');
                }
            }
            return null;
        }

    // 更新任务信息显示
    function updateTaskInfo(taskInfo) {
        console.log('Updating task info:', taskInfo);
        
        // 更新任务ID
        const taskIdElement = document.getElementById('task-id');
        if (taskIdElement) {
            taskIdElement.textContent = taskInfo.task_id || '-';
        }
        
        // 更新状态
        const taskStatusElement = document.getElementById('task-status');
        if (taskStatusElement) {
            // 保存原始状态值，用于语言切换时更新
            taskStatusElement.dataset.originalStatus = taskInfo.status;
            taskStatusElement.textContent = getStatusText(taskInfo.status) || '-';
            // 根据状态设置样式
            taskStatusElement.className = 'info-value status-badge';
            if (taskInfo.status) {
                const statusClass = taskInfo.status.replace(/\s+/g, '-').replace(/[^a-zA-Z0-9-_]/g, '').toLowerCase();
                taskStatusElement.classList.add(`status-${statusClass}`);
            }
        }
    }


    // 更新任务状态显示（用于语言切换时）
    function updateTaskStatusDisplay() {
        const taskStatusElement = document.getElementById('task-status');
        if (taskStatusElement && taskStatusElement.dataset.originalStatus) {
            const originalStatus = taskStatusElement.dataset.originalStatus;
            taskStatusElement.textContent = getStatusText(originalStatus) || '-';
        }
        
        // 更新人类可读结果
        updateHumanReadableResults();
    }

    // 更新人类可读结果显示（用于语言切换时）
    function updateHumanReadableResults() {
        const humanReadableDiv = document.getElementById('human-readable-results');
        if (humanReadableDiv && window.lastResultData) {
            const { humanReadableResults, resultData } = window.lastResultData;
            
            if (humanReadableResults) {
                // 重新处理人类可读结果，使用当前语言
                const filteredResults = filterHumanReadableResults(humanReadableResults, resultData, currentLanguage);
                humanReadableDiv.innerHTML = `<pre>${filteredResults}</pre>`;
                console.log('Updated filtered results for language:', currentLanguage, filteredResults);
            } else {
                humanReadableDiv.innerHTML = `<p>${translations[currentLanguage]["no-results"]}</p>`;
            }
        } else {
            console.log('Cannot update human readable results - missing div or data');
        }
    }


    // 显示结果
    function displayResults(resultData, taskId = null) {
        console.log('=== DisplayResults Debug ===');
        console.log('Result data:', resultData);
        console.log('Task ID:', taskId);
        console.log('Result data type:', typeof resultData);
        console.log('Result data keys:', Object.keys(resultData || {}));

        // 隐藏加载动画
        hideLoadingSpinner();

        // 检查是否有错误信息
        if (resultData && resultData.error) {
            console.error('Task failed with error:', resultData.error);
            
            // 更新状态为失败
            updateResultsStatus('failed', translations[currentLanguage]['failed']);

            // 更新任务信息
            const actualTaskId = resultData.task_id || taskId;
            const taskInfo = {
                task_id: actualTaskId || 'N/A',
                status: "failed",
                error_message: resultData.error,
                completed_at: new Date().toISOString()
            };
            updateTaskInfo(taskInfo);

            // 显示错误信息
            const humanReadableDiv = document.getElementById('human-readable-results');
            humanReadableDiv.innerHTML = `
                <div style="color: #ef4444; padding: 1rem; background: #fef2f2; border: 1px solid #fecaca; border-radius: 0.5rem;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #dc2626;">❌ ${translations[currentLanguage]['task-failed']}</h4>
                    <p style="margin: 0;">${resultData.error}</p>
                </div>
            `;

            // 清空其他结果区域
            const scoresTab = document.getElementById('scores-tab');
            const filesTab = document.getElementById('files-tab');
            if (scoresTab) scoresTab.innerHTML = '<p style="text-align: center; padding: 2rem; color: #666;">无结果数据</p>';
            if (filesTab) filesTab.innerHTML = '<p style="text-align: center; padding: 2rem; color: #666;">无结果文件</p>';

            return; // 提前返回，不继续处理结果
        }


        // 更新状态为完成
        updateResultsStatus('completed', translations[currentLanguage]['completed']);

        // 更新任务信息 - 优先从结果数据中获取服务器返回的task_id
        const actualTaskId = resultData.task_id || taskId;
        const taskInfo = {
            task_id: actualTaskId || 'N/A',
            status: "completed",
            completed_at: new Date().toISOString()
        };
        updateTaskInfo(taskInfo);

        // 获取人类可读结果
        let humanReadableResults = null;
        if (resultData.results && resultData.results.human_readable_results) {
            humanReadableResults = resultData.results.human_readable_results;
        } else if (resultData.human_readable_results) {
            humanReadableResults = resultData.human_readable_results;
        }
        
        // 保存结果数据，用于语言切换时更新
        window.lastResultData = {
            humanReadableResults: humanReadableResults,
            resultData: resultData,
            taskId: actualTaskId
        };
        
        // console.log('Human readable results:', humanReadableResults);
        
        const humanReadableDiv = document.getElementById('human-readable-results');
        if (humanReadableResults) {
            // 处理人类可读结果，只保留任务总结信息
            const filteredResults = filterHumanReadableResults(humanReadableResults, resultData, currentLanguage);
            humanReadableDiv.innerHTML = `<pre>${filteredResults}</pre>`;
            console.log('Filtered results:', filteredResults);
        } else {
            humanReadableDiv.innerHTML = `<p>${translations[currentLanguage]["no-results"]}</p>`;
        }

        // 获取分数和文件数据 - 根据新的数据格式
        let scores = null;
        let files = null;
        
        if (resultData.scores) {
            scores = resultData.scores;
            files = resultData.pdbqt_files;
        }

        // console.log('原始scores数据:', scores);
        // console.log('原始files数据:', files);

        // 转换数据格式以适配现有的显示逻辑
        let processedScores = null;
        let processedFiles = null;

        if (scores && typeof scores === 'object') {
            processedScores = {};
            // 处理实际的数据格式：键是文件名（如"ligand_0.pdbqt"）
            Object.entries(scores).forEach(([filename, scoresArray]) => {
                // console.log(`处理scores条目: ${filename} -> ${scoresArray}`);
                // 从文件名提取显示用的名称
                let displayFilename = filename;
                if (filename.includes('.pdbqt')) {
                    // 将 "ligand_0.pdbqt" 转换为 "ligand_0_out"
                    displayFilename = filename.replace('.pdbqt', '_out');
                }
                processedScores[displayFilename] = scoresArray;
                //console.log(`转换后的scores键: ${filename} -> ${displayFilename}`);
            });
        }

        if (files && typeof files === 'object') {
            processedFiles = {};
            // 处理实际的数据格式：键是文件名，值可能是数组
            Object.entries(files).forEach(([filename, filePathArray]) => {
                // console.log(`处理files条目: ${filename} -> ${filePathArray}`);
                // 从文件名提取显示用的名称
                let displayFilename = filename;
                if (filename.includes('.pdbqt')) {
                    // 将 "ligand_0.pdbqt" 转换为 "ligand_0_out"
                    displayFilename = filename.replace('.pdbqt', '_out');
                }
                
                // 处理文件路径：如果是数组，取第一个；如果是字符串，直接使用
                let filePath;
                if (Array.isArray(filePathArray)) {
                    filePath = filePathArray.length > 0 ? filePathArray[0] : null;
                } else {
                    filePath = filePathArray;
                }
                
                if (filePath) {
                    // console.log(`使用文件路径: ${filePath} (原始键: ${filename}, 显示键: ${displayFilename})`);
                    processedFiles[displayFilename] = [filePath];
                } else {
                    console.log(`❌ 文件路径为空: ${filename}`);
                }
            });
        }

        // console.log('处理后的scores:', processedScores);
        // console.log('处理后的files:', processedFiles);

        // 计算排序顺序（基于第一个构象的能量得分）
        let sortedOrder = null;
        if (processedScores && Object.keys(processedScores).length > 0) {
            const scoresArray = Object.entries(processedScores).map(([filename, scoresArray]) => {
                let firstScore = null;
                if (Array.isArray(scoresArray) && scoresArray.length > 0) {
                    firstScore = scoresArray[0]; // 第一个构象的能量得分
                }
                return {
                    filename,
                    firstScore
                };
            });

            // 根据第一个构象的能量得分从小到大排序
            scoresArray.sort((a, b) => {
                if (a.firstScore === null && b.firstScore === null) return 0;
                if (a.firstScore === null) return 1;
                if (b.firstScore === null) return -1;
                return a.firstScore - b.firstScore;
            });

            sortedOrder = scoresArray.map(item => item.filename);
            console.log('Calculated sorted order:', sortedOrder);
        }
        
        // 显示分数结果
        if (processedScores && Object.keys(processedScores).length > 0) {
            console.log('✅ 调用 displayScores');
            displayScores(processedScores);
        } else {
            console.log('❌ 没有分数数据，显示默认消息');
            document.getElementById('scores-list').innerHTML = `<p>${translations[currentLanguage]['no-scores-data']}</p>`;
        }

        // 显示文件下载链接
        if (processedFiles && Object.keys(processedFiles).length > 0) {
            console.log('✅ 调用 displayFiles');
            displayFiles(processedFiles, sortedOrder, actualTaskId);
        } else {
            console.log('❌ 没有文件数据，显示默认消息');
            document.getElementById('files-list').innerHTML = `<p>${translations[currentLanguage]['no-files-available']}</p>`;
        }
        
        console.log('=== DisplayResults Debug End ===');
    }

    // 显示分数结果
    function displayScores(scores) {
        console.log('=== DisplayScores Debug ===');
        console.log('Scores input:', scores);
        console.log('Scores type:', typeof scores);
        console.log('Scores keys:', Object.keys(scores || {}));
        
        const scoresList = document.getElementById('scores-list');
        scoresList.innerHTML = '';

        if (!scores || Object.keys(scores).length === 0) {
            console.log('No scores data to display');
            scoresList.innerHTML = `<p>${translations[currentLanguage]['no-scores-data']}</p>`;
            return;
        }

        // 将scores对象转换为数组，并根据第一个构象的能量得分排序
        const scoresArray = Object.entries(scores).map(([filename, scoresArray]) => {
            let firstScore = null;
            if (Array.isArray(scoresArray) && scoresArray.length > 0) {
                firstScore = scoresArray[0]; // 第一个构象的能量得分
            }
            return {
                filename,
                scoresArray,
                firstScore
            };
        });

        // 根据第一个构象的能量得分从小到大排序（能量越低越好）
        scoresArray.sort((a, b) => {
            // 如果某个配体没有有效的分数，将其排到最后
            if (a.firstScore === null && b.firstScore === null) return 0;
            if (a.firstScore === null) return 1;
            if (b.firstScore === null) return -1;
            
            return a.firstScore - b.firstScore; // 从小到大排序
        });

        console.log('Sorted scores array:', scoresArray);

        // 更新分页状态
        paginationState.scores.data = scoresArray;
        paginationState.scores.totalItems = scoresArray.length;
        paginationState.scores.currentPage = 1; // 重置到第一页

        // 渲染第一页
        renderScoresPage();
        
        console.log('=== DisplayScores Debug End ===');
    }

    // 显示文件下载链接
    function displayFiles(files, sortedOrder = null, taskId = null) {
        console.log('=== DisplayFiles Debug ===');
        console.log('Files input:', files);
        console.log('Files type:', typeof files);
        console.log('Files keys:', Object.keys(files || {}));
        console.log('Sorted order:', sortedOrder);
        console.log('Task ID:', taskId);
        
        const filesList = document.getElementById('files-list');
        filesList.innerHTML = '';

        if (!files || Object.keys(files).length === 0) {
            console.log('No files data to display');
            filesList.innerHTML = `<p>${translations[currentLanguage]['no-files-available']}</p>`;
            return;
        }

        // 从文件名中提取原始分子序号的函数
        function extractMoleculeIndex(filename) {
            // 匹配 ligand_X_out 格式，其中X是分子序号
            const match = filename.match(/ligand_(\d+)_out/);
            if (match) {
                return parseInt(match[1], 10);
            }
            // 如果不匹配，尝试其他可能的格式
            const generalMatch = filename.match(/(\d+)/);
            if (generalMatch) {
                return parseInt(generalMatch[1], 10);
            }
            // 如果都不匹配，返回0作为默认值
            return 0;
        }

        // 如果有排序顺序，按照排序顺序处理文件；否则按原始顺序
        let filesToProcess;
        if (sortedOrder && sortedOrder.length > 0) {
            // 按照排序顺序创建文件数组
            filesToProcess = sortedOrder.map((filename, sortedIndex) => {
                const originalMoleculeIndex = extractMoleculeIndex(filename);
                return {
                    filename,
                    urlsArray: files[filename] || [],
                    sortedIndex: sortedIndex, // 排序后的位置（用于显示排名）
                    originalMoleculeIndex: originalMoleculeIndex // 原始分子序号（用于下载链接）
                };
            }).filter(item => item.urlsArray.length > 0); // 只保留有有效URL的文件
        } else {
            // 按原始顺序处理
            filesToProcess = Object.entries(files).map(([filename, urlsArray], index) => {
                const originalMoleculeIndex = extractMoleculeIndex(filename);
                return {
                    filename,
                    urlsArray,
                    sortedIndex: index,
                    originalMoleculeIndex: originalMoleculeIndex
                };
            });
        }
        
        // console.log('Files to process:', filesToProcess);
        
        // 更新分页状态，传递taskId
        paginationState.files.data = filesToProcess;
        paginationState.files.totalItems = filesToProcess.length;
        paginationState.files.currentPage = 1; // 重置到第一页
        paginationState.files.taskId = taskId; // 保存taskId

        // 渲染第一页
        renderFilesPage();
        
        console.log('=== DisplayFiles Debug End ===');
    }

    // 下载文件
    function downloadFile(filename, fileContent) {
        const blob = new Blob([fileContent], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    // 从URL下载文件
    function downloadFileFromUrl(url, filename) {
        console.log('=== Download Debug ===');
        console.log('Downloading file from URL:', url);
        console.log('Suggested filename:', filename);

        // 创建隐藏的下载链接
        const downloadApiUrl = `/api/download/results?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(filename)}`;

        const link = document.createElement('a');
        link.href = downloadApiUrl;
        link.download = filename || ''; // 设置下载文件名
        link.style.display = 'none';
        
        // 添加到DOM，触发下载，然后移除
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        
        // 直接在新窗口打开文件链接
        // window.open(url, '_blank');
    }


   // 一键下载当前查看任务的所有结果文件
   function downloadCurrentTaskAllResults(taskId) {
        console.log('=== Download Current Task All Results Debug ===');
        const state = paginationState.files;
        // const taskId = state.taskId;
        const allMolecules = state.data; // 当前任务的所有分子数据
        
        console.log('Current task ID:', taskId);
        console.log('Current task molecules count:', allMolecules.length);
        
        if (!taskId) {
            alert('未找到当前任务信息');
            return;
        }
        
        if (!Array.isArray(allMolecules) || allMolecules.length === 0) {
            alert('当前任务没有可下载的文件');
            return;
        }
        
        // 统计当前任务的总文件数
        let totalFiles = 0;
        allMolecules.forEach(({ urlsArray }) => {
            if (Array.isArray(urlsArray)) {
                totalFiles += urlsArray.length;
            }
        });
        
        if (totalFiles === 0) {
            alert('当前任务没有可下载的文件');
            return;
        }
        
        console.log(`准备下载任务 ${taskId} 的所有结果: ${allMolecules.length} 个分子, ${totalFiles} 个文件`);
        
        // 确认下载
        const confirmMessage = `确定要下载任务的所有结果吗？\n\n任务ID: ${taskId}\n分子数量: ${allMolecules.length}\n文件总数: ${totalFiles}`;
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // 显示下载进度提示
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 4px;">📦 下载任务结果</div>
            <div style="font-size: 12px;">任务ID: ${taskId}</div>
            <div style="font-size: 12px;">正在下载 ${allMolecules.length} 个分子的 ${totalFiles} 个文件...</div>
        `;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.background = '#059669';
        notification.style.color = 'white';
        notification.style.padding = '12px 16px';
        notification.style.borderRadius = '8px';
        notification.style.zIndex = '10000';
        notification.style.fontSize = '13px';
        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
        notification.style.maxWidth = '300px';
        
        document.body.appendChild(notification);
        
        // 调用后端API创建ZIP文件
        fetch(`/api/tasks/${taskId}/download-zip`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('ZIP creation response:', data);
            
            if (data.success) {
                // 更新提示信息
                notification.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 4px;">✅ ZIP文件创建成功</div>
                    <div style="font-size: 12px;">文件名: ${data.filename}</div>
                    <div style="font-size: 12px;">文件大小: ${data.file_size}</div>
                    <div style="font-size: 12px;">包含文件: ${data.files_count} 个</div>
                    <div style="font-size: 11px; margin-top: 4px; opacity: 0.8;">🚀 开始下载...</div>
                `;
                notification.style.background = '#3b82f6';
                
                // 创建下载链接并触发下载
                const downloadLink = document.createElement('a');
                downloadLink.href = data.download_url;
                downloadLink.download = data.filename;
                downloadLink.style.display = 'none';
                
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                
                console.log(`✅ 开始下载ZIP文件: ${data.filename} (${data.file_size})`);
                
                // 3秒后移除成功提示
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 3000);
                
            } else {
                // 显示错误信息
                notification.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 4px;">❌ 下载失败</div>
                    <div style="font-size: 12px; color: #fee;">错误: ${data.message}</div>
                `;
                notification.style.background = '#ef4444';
                
                // 5秒后移除错误提示
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 5000);
                
                console.error('ZIP creation failed:', data.message);
            }
        })
        .catch(error => {
            console.error('Error creating ZIP:', error);
            
            // 显示网络错误信息
            notification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 4px;">❌ 网络错误</div>
                <div style="font-size: 12px; color: #fee;">无法连接到服务器，请稍后重试</div>
                <div style="font-size: 11px; margin-top: 4px; opacity: 0.8;">${error.message}</div>
            `;
            notification.style.background = '#ef4444';
            
            // 5秒后移除错误提示
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 5000);
        });
    }


    // 模型提取功能
    function setupModelExtraction() {
        const extractBtn = document.getElementById('extract-model-btn');
        const fileInput = document.getElementById('pdbqt-file-input');
        const modelInput = document.getElementById('model-number-input');
        const statusDiv = document.getElementById('extraction-status');
        
        if (extractBtn) {
            extractBtn.addEventListener('click', async function() {
                const file = fileInput.files[0];
                const modelNumber = parseInt(modelInput.value) || 1;
                
                if (!file) {
                    showExtractionStatus('请选择PDBQT文件', 'error');
                    return;
                }
                
                if (modelNumber < 1) {
                    showExtractionStatus('构象编号必须大于0', 'error');
                    return;
                }
                
                await extractModel(file, modelNumber);
            });
        }
    }
    
    // 显示提取状态
    function showExtractionStatus(message, type = 'info') {
        const statusDiv = document.getElementById('extraction-status');
        if (!statusDiv) return;
        
        statusDiv.style.display = 'block';
        statusDiv.textContent = message;
        
        // 设置状态样式
        statusDiv.className = 'extraction-status';
        switch (type) {
            case 'success':
                statusDiv.style.backgroundColor = '#d1fae5';
                statusDiv.style.color = '#065f46';
                statusDiv.style.borderColor = '#10b981';
                break;
            case 'error':
                statusDiv.style.backgroundColor = '#fee2e2';
                statusDiv.style.color = '#991b1b';
                statusDiv.style.borderColor = '#ef4444';
                break;
            case 'warning':
                statusDiv.style.backgroundColor = '#fef3c7';
                statusDiv.style.color = '#92400e';
                statusDiv.style.borderColor = '#f59e0b';
                break;
            default: // info
                statusDiv.style.backgroundColor = '#dbeafe';
                statusDiv.style.color = '#1e40af';
                statusDiv.style.borderColor = '#3b82f6';
        }
        statusDiv.style.border = '1px solid';
    }
    
    // 提取模型
    async function extractModel(file, modelNumber) {
        const extractBtn = document.getElementById('extract-model-btn');
        const originalText = extractBtn.innerHTML;
        
        try {
            // 设置按钮为处理中状态
            extractBtn.disabled = true;
            extractBtn.innerHTML = '<span>⏳</span> 处理中...';
            extractBtn.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
            
            showExtractionStatus('正在读取文件...', 'info');
            
            // 读取文件内容
            const fileContent = await readFileContent(file);
            
            showExtractionStatus(`正在提取第${modelNumber}个构象...`, 'info');
            
            // 发送到后端处理
            const response = await fetch('/api/extract_model', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    pdbqt_content: fileContent,
                    model_number: modelNumber,
                    filename: file.name
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showExtractionStatus('提取成功！正在下载PDB文件...', 'success');
                
                // 下载生成的PDB文件
                downloadFile(result.filename, result.pdb_content);
                
                // 显示成功信息
                setTimeout(() => {
                    showExtractionStatus(`成功提取第${modelNumber}个构象并转换为PDB格式`, 'success');
                }, 1000);
                
            } else {
                showExtractionStatus('提取失败: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('提取模型时出错:', error);
            showExtractionStatus('提取过程中发生错误: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            extractBtn.disabled = false;
            extractBtn.innerHTML = originalText;
            extractBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
        }
    }
    

    // 分页状态管理
    let paginationState = {
        scores: {
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            data: []
        },
        files: {
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            data: [],
            taskId: null
        }
    };

    // 创建分页控件
    function createPaginationControls(containerId, type, currentPage, totalPages, totalItems) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 清除现有的分页控件
        const existingPagination = container.querySelector('.pagination-container');
        if (existingPagination) {
            existingPagination.remove();
        }

        if (totalPages <= 1) return; // 如果只有一页或没有数据，不显示分页控件

        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'pagination-container';

        // 分页信息
        const pageInfo = document.createElement('div');
        pageInfo.className = 'pagination-info';
        pageInfo.textContent = `共 ${totalItems} 个结果，第 ${currentPage} / ${totalPages} 页`;

        // 分页控件
        const paginationControls = document.createElement('div');
        paginationControls.className = 'pagination-controls';

        // 上一页按钮
        const prevBtn = document.createElement('button');
        prevBtn.className = 'pagination-btn';
        prevBtn.textContent = '‹';
        prevBtn.disabled = currentPage === 1;
        prevBtn.onclick = () => changePage(type, currentPage - 1);

        // 页码按钮
        const pageButtons = [];
        const maxVisiblePages = 7; // 最多显示7个页码按钮
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        // 调整起始页，确保显示足够的页码
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // 第一页
        if (startPage > 1) {
            const firstBtn = document.createElement('button');
            firstBtn.className = 'pagination-btn';
            firstBtn.textContent = '1';
            firstBtn.onclick = () => changePage(type, 1);
            pageButtons.push(firstBtn);

            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'pagination-ellipsis';
                ellipsis.textContent = '...';
                pageButtons.push(ellipsis);
            }
        }

        // 中间页码
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
            pageBtn.textContent = i.toString();
            pageBtn.onclick = () => changePage(type, i);
            pageButtons.push(pageBtn);
        }

        // 最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'pagination-ellipsis';
                ellipsis.textContent = '...';
                pageButtons.push(ellipsis);
            }

            const lastBtn = document.createElement('button');
            lastBtn.className = 'pagination-btn';
            lastBtn.textContent = totalPages.toString();
            lastBtn.onclick = () => changePage(type, totalPages);
            pageButtons.push(lastBtn);
        }

        // 下一页按钮
        const nextBtn = document.createElement('button');
        nextBtn.className = 'pagination-btn';
        nextBtn.textContent = '›';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.onclick = () => changePage(type, currentPage + 1);

        // 页面大小选择器
        const pageSizeSelector = document.createElement('div');
        pageSizeSelector.className = 'page-size-selector';
        
        const pageSizeLabel = document.createElement('span');
        pageSizeLabel.textContent = '每页显示:';
        
        const pageSizeSelect = document.createElement('select');
        const pageSizeOptions = [10, 20, 50, 100];
        pageSizeOptions.forEach(size => {
            const option = document.createElement('option');
            option.value = size;
            option.textContent = size;
            option.selected = size === paginationState[type].pageSize;
            pageSizeSelect.appendChild(option);
        });
        
        pageSizeSelect.onchange = (e) => {
            changePageSize(type, parseInt(e.target.value));
        };

        // 组装控件
        paginationControls.appendChild(prevBtn);
        pageButtons.forEach(btn => paginationControls.appendChild(btn));
        paginationControls.appendChild(nextBtn);

        pageSizeSelector.appendChild(pageSizeLabel);
        pageSizeSelector.appendChild(pageSizeSelect);

        paginationContainer.appendChild(pageInfo);
        paginationContainer.appendChild(paginationControls);
        paginationContainer.appendChild(pageSizeSelector);

        container.appendChild(paginationContainer);
    }

    // 切换页面
    function changePage(type, newPage) {
        const state = paginationState[type];
        const totalPages = Math.ceil(state.totalItems / state.pageSize);
        
        if (newPage < 1 || newPage > totalPages) return;
        
        state.currentPage = newPage;
        
        if (type === 'scores') {
            renderScoresPage();
        } else if (type === 'files') {
            renderFilesPage();
        }
    }

    // 改变页面大小
    function changePageSize(type, newPageSize) {
        const state = paginationState[type];
        state.pageSize = newPageSize;
        state.currentPage = 1; // 重置到第一页
        
        if (type === 'scores') {
            renderScoresPage();
        } else if (type === 'files') {
            renderFilesPage();
        }
    }

    // 渲染分数页面
    function renderScoresPage() {
        console.log('=== RenderScoresPage Debug ===');
        const state = paginationState.scores;
        console.log('Pagination state:', state);
        
        const startIndex = (state.currentPage - 1) * state.pageSize;
        const endIndex = Math.min(startIndex + state.pageSize, state.totalItems);
        const pageData = state.data.slice(startIndex, endIndex);
        console.log('Page data to render:', pageData);

        const scoresList = document.getElementById('scores-list');
        console.log('Scores list element:', scoresList);
        console.log('Scores list element exists:', !!scoresList);
        
        if (!scoresList) {
            console.error('❌ scores-list element not found!');
            return;
        }
        
        const ul = document.createElement('ul');
        console.log('Created ul element:', ul);

        pageData.forEach(({ filename, scoresArray: scores }, pageIndex) => {
            const globalIndex = startIndex + pageIndex;
            // console.log(`Rendering score for ${filename} (页面位置: ${pageIndex + 1}, 全局排名: ${globalIndex + 1}):`, scores);
            
            const li = document.createElement('li');
            
            if (Array.isArray(scores) && scores.length > 0) {
                const bestScore = Math.min(...scores);
                const bestIndex = scores.indexOf(bestScore);
                
                // 只显示前3个构象，如果有更多则显示省略号
                const maxDisplayConformations = 3;
                const displayScores = scores.slice(0, maxDisplayConformations);
                const hasMoreConformations = scores.length > maxDisplayConformations;
                
                const htmlContent = `
                    <strong>${filename} (排名: ${globalIndex + 1}):</strong>
                    <div style="margin-top: 0.5rem;">
                        <div style="color: #059669; font-weight: 600;">
                            最佳结合能量: ${bestScore} kcal/mol (构象 ${bestIndex + 1})
                        </div>
                        <div style="margin-top: 0.5rem;">
                            <strong>构象结合能量 (前${Math.min(scores.length, maxDisplayConformations)}个):</strong>
                            <ul style="margin-top: 0.25rem;">
                                ${displayScores.map((score, index) => 
                                    `<li style="color: ${index === bestIndex ? '#059669' : '#6b7280'}; font-weight: ${index === bestIndex ? '600' : 'normal'};">
                                        构象 ${index + 1}: ${score} kcal/mol ${index === bestIndex ? '(最佳)' : ''}
                                    </li>`
                                ).join('')}
                                ${hasMoreConformations ? `<li style="color: #9ca3af; font-style: italic;">... 还有 ${scores.length - maxDisplayConformations} 个构象</li>` : ''}
                            </ul>
                        </div>
                    </div>
                `;
                
                li.innerHTML = htmlContent;
                // console.log('Created li with content:', htmlContent);
            } else {
                const htmlContent = `<strong>${filename} (排名: ${globalIndex + 1}):</strong> <span style="color: #ef4444;">无有效分数数据</span>`;
                li.innerHTML = htmlContent;
                // console.log('Created li with no-data content:', htmlContent);
            }
            
            ul.appendChild(li);
            // console.log('Appended li to ul, ul children count:', ul.children.length);
        });

        // console.log('Final ul element:', ul);
        console.log('Final ul children count:', ul.children.length);
        // console.log('Final ul innerHTML:', ul.innerHTML);

        scoresList.innerHTML = '';
        scoresList.appendChild(ul);
        
        // console.log('Updated scoresList innerHTML:', scoresList.innerHTML);
        console.log('ScoresList element visibility:', window.getComputedStyle(scoresList).display);
        console.log('ScoresList element dimensions:', scoresList.getBoundingClientRect());

        // 创建分页控件
        const totalPages = Math.ceil(state.totalItems / state.pageSize);
        createPaginationControls('scores-list', 'scores', state.currentPage, totalPages, state.totalItems);
        
        console.log('=== RenderScoresPage Debug End ===');
    }

    // 渲染文件页面
    function renderFilesPage() {
        console.log('=== RenderFilesPage Debug ===');
        const state = paginationState.files;
        console.log('Files pagination state:', state);
        
        const startIndex = (state.currentPage - 1) * state.pageSize;
        const endIndex = Math.min(startIndex + state.pageSize, state.totalItems);
        const pageData = state.data.slice(startIndex, endIndex);
        const taskId = state.taskId; // 获取保存的taskId
        // console.log('Files page data to render:', pageData);

        const filesList = document.getElementById('files-list');
        // console.log('Files list element:', filesList);
        console.log('Files list element exists:', !!filesList);
        
        if (!filesList) {
            console.error('❌ files-list element not found!');
            return;
        }

        // 清空现有内容
        filesList.innerHTML = '';

        // 添加当前任务的全局下载按钮 (只在有数据且有taskId时显示)
        if (state.data && state.data.length > 0 && taskId) {
            const globalDownloadContainer = document.createElement('div');
            globalDownloadContainer.style.marginBottom = '1rem';
            globalDownloadContainer.style.padding = '1rem';
            globalDownloadContainer.style.background = 'linear-gradient(135deg, #f8fafc, #e2e8f0)';
            globalDownloadContainer.style.border = '2px solid #059669';
            globalDownloadContainer.style.borderRadius = '8px';
            globalDownloadContainer.style.textAlign = 'center';
            
            // 统计当前任务的文件数
            let totalMolecules = state.data.length;
            let totalFiles = 0;
            state.data.forEach(({ urlsArray }) => {
                if (Array.isArray(urlsArray)) {
                    totalFiles += urlsArray.length;
                }
            });
            
            // 任务信息
            const taskInfo = document.createElement('div');
            taskInfo.innerHTML = `
                <div style="font-size: 0.875rem; color: #374151; margin-bottom: 0.5rem;">
                    <strong>当前任务:</strong> ${taskId}
                </div>
            `;
            
            const downloadAllTaskBtn = document.createElement('button');
            downloadAllTaskBtn.innerHTML = `
                📦 下载当前任务所有结果<br>
                <span style="font-size: 0.875rem; opacity: 0.9;">${totalMolecules} 个分子 • ${totalFiles} 个文件</span>
            `;
            downloadAllTaskBtn.style.padding = '0.75rem 1.5rem';
            downloadAllTaskBtn.style.background = 'linear-gradient(135deg, #059669, #10b981)';
            downloadAllTaskBtn.style.color = 'white';
            downloadAllTaskBtn.style.border = 'none';
            downloadAllTaskBtn.style.borderRadius = '6px';
            downloadAllTaskBtn.style.fontSize = '1rem';
            downloadAllTaskBtn.style.fontWeight = '600';
            downloadAllTaskBtn.style.cursor = 'pointer';
            downloadAllTaskBtn.style.transition = 'all 0.2s ease';
            downloadAllTaskBtn.style.boxShadow = '0 2px 8px rgba(5, 150, 105, 0.3)';
            downloadAllTaskBtn.style.lineHeight = '1.2';
            
            downloadAllTaskBtn.onmouseover = () => {
                downloadAllTaskBtn.style.background = 'linear-gradient(135deg, #047857, #059669)';
                downloadAllTaskBtn.style.transform = 'translateY(-1px)';
                downloadAllTaskBtn.style.boxShadow = '0 4px 12px rgba(5, 150, 105, 0.4)';
            };
            downloadAllTaskBtn.onmouseout = () => {
                downloadAllTaskBtn.style.background = 'linear-gradient(135deg, #059669, #10b981)';
                downloadAllTaskBtn.style.transform = 'translateY(0)';
                downloadAllTaskBtn.style.boxShadow = '0 2px 8px rgba(5, 150, 105, 0.3)';
            };
            
            downloadAllTaskBtn.onclick = () => {
                const state = paginationState.files;
                const taskId = state.taskId;
                downloadCurrentTaskAllResults(taskId);
            }
            
            const downloadAllTaskDescription = document.createElement('div');
            downloadAllTaskDescription.textContent = '一键下载此任务的所有分子对接结果文件';
            downloadAllTaskDescription.style.marginTop = '0.5rem';
            downloadAllTaskDescription.style.fontSize = '0.8rem';
            downloadAllTaskDescription.style.color = '#6b7280';
            
            globalDownloadContainer.appendChild(taskInfo);
            globalDownloadContainer.appendChild(downloadAllTaskBtn);
            globalDownloadContainer.appendChild(downloadAllTaskDescription);
            filesList.appendChild(globalDownloadContainer);
        }

        
        const ul = document.createElement('ul');

        pageData.forEach(({ filename, urlsArray, sortedIndex, originalMoleculeIndex }, pageIndex) => {
            const globalIndex = startIndex + pageIndex;
            // 使用originalMoleculeIndex作为下载链接的分子序号（原始分子的真实序号）
            //console.log(`Rendering file for ${filename} (页面位置: ${pageIndex + 1}, 全局排名: ${globalIndex + 1}, 原始分子序号: ${originalMoleculeIndex}):`, urlsArray);
            
            const li = document.createElement('li');
            
            if (Array.isArray(urlsArray) && urlsArray.length > 0) {
                const fileNameSpan = document.createElement('span');
                fileNameSpan.textContent = `${filename} (排名: ${globalIndex + 1})`;
                fileNameSpan.style.fontWeight = '600';
                fileNameSpan.style.color = '#1f2937';

                // 添加一键下载所有文件的按钮
                const downloadAllContainer = document.createElement('div');
                downloadAllContainer.style.marginTop = '0.5rem';
                downloadAllContainer.style.marginBottom = '0.5rem';

                
                
                const buttonsContainer = document.createElement('div');
                buttonsContainer.style.display = 'flex';
                buttonsContainer.style.gap = '0.5rem';
                buttonsContainer.style.marginTop = '0.5rem';
                
                urlsArray.forEach((url, index) => {
                    const downloadBtn = document.createElement('button');
                    
                    if (urlsArray.length === 1) {
                        downloadBtn.textContent = '下载文件';
                    } else {
                        downloadBtn.textContent = `下载构象 ${index + 1}`;
                    }
                    
                    downloadBtn.style.padding = '0.5rem 1rem';
                    downloadBtn.style.background = '#3b82f6';
                    downloadBtn.style.color = 'white';
                    downloadBtn.style.border = 'none';
                    downloadBtn.style.borderRadius = '4px';
                    downloadBtn.style.fontSize = '0.875rem';
                    downloadBtn.style.cursor = 'pointer';
                    downloadBtn.style.transition = 'background-color 0.2s ease';
                    
                    downloadBtn.onmouseover = () => {
                        downloadBtn.style.background = '#1d4ed8';
                    };
                    downloadBtn.onmouseout = () => {
                        downloadBtn.style.background = '#3b82f6';
                    };
                    
                    // 直接使用POST返回结果中的实际文件链接
                    let downloadUrl = url; // 使用API返回的实际文件链接
                    
                    downloadBtn.onclick = () => downloadFileFromUrl(downloadUrl, `${filename}_pose_${index + 1}`);
                    
                    buttonsContainer.appendChild(downloadBtn);
                });
                
                li.appendChild(fileNameSpan);
                li.appendChild(buttonsContainer);
                li.appendChild(downloadAllContainer);

            } else {
                const displayName = `${filename} (排名: ${globalIndex + 1})`;
                li.innerHTML = `<strong>${displayName}:</strong> <span style="color: #ef4444;">无可用下载链接</span>`;
            }
            
            ul.appendChild(li);
        });

        console.log('Final files ul element:', ul);
        console.log('Final files ul children count:', ul.children.length);


        filesList.appendChild(ul);
        
        // console.log('Updated filesList innerHTML:', filesList.innerHTML);
        console.log('FilesList element visibility:', window.getComputedStyle(filesList).display);
        console.log('FilesList element dimensions:', filesList.getBoundingClientRect());

        // 创建分页控件
        const totalPages = Math.ceil(state.totalItems / state.pageSize);
        createPaginationControls('files-list', 'files', state.currentPage, totalPages, state.totalItems);
        
        console.log('=== RenderFilesPage Debug End ===');
    }



    // 显示结果栏
    function showResultsSection() {
        console.log('=== ShowResultsSection Debug ===');
        const resultsSection = document.getElementById('docking-results-section');
        console.log('Results section element:', resultsSection);
        console.log('Results section current display:', resultsSection ? resultsSection.style.display : 'element not found');
        
        if (resultsSection) {
            resultsSection.style.display = 'block';
            console.log('Results section display set to block');
            
            // 显示加载状态
            showLoadingSpinner();
            
            // 滚动到结果栏
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            console.log('Scrolled to results section');
            } else {
            console.error('Results section element not found!');
        }
        console.log('=== ShowResultsSection Debug End ===');
    }

    // 更新结果状态
    function updateResultsStatus(status, message) {
        // console.log(`=== UpdateResultsStatus: ${status} - ${message} ===`);
        // const statusText = document.getElementById('results-status-text');
        // console.log('Status text element:', statusText);
        
        // if (statusText) {
        //    const tr_message = translations[currentLanguage][message] || message;
        //    statusText.textContent = tr_message;
        //    statusText.className = `status-text status-${status}`;
        //    console.log(`Status updated to: ${status}, message: ${tr_message}`);
        //    } else {
        //     console.error('Status text element not found!');
        // }
    }

    // 显示/隐藏加载动画
    function showLoadingSpinner() {
        const loadingElement = document.getElementById('results-loading');
        if (loadingElement) {
            loadingElement.style.display = 'block';
        }
    }

    function hideLoadingSpinner() {
        const loadingElement = document.getElementById('results-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }

    // 切换详细结果显示
    function toggleDetails() {
        const detailedResults = document.getElementById('detailed-results');
        const toggleBtn = document.getElementById('toggle-details');
        
        if (detailedResults.style.display === 'none') {
            detailedResults.style.display = 'block';
            toggleBtn.textContent = '-';
        } else {
            detailedResults.style.display = 'none';
            toggleBtn.textContent = '+';
        }
    }

    // 隐藏结果区域
    function hideResults() {
        const resultsSection = document.getElementById('docking-results-section');
        if (resultsSection) {
            resultsSection.style.display = 'none';
            console.log('Results section hidden');
        }
    }


    // 显示任务结果
    async function showTaskResults(taskId) {
        try {
            console.log('=== showTaskResults Debug ===');
            console.log('Fetching results for task ID:', taskId);
            
            const response = await fetch(`/api/tasks/${taskId}`);
            const result = await response.json();
            
            // console.log('API response:', result);
            // console.log('Task data:', result.task);
            // console.log('Task result:', result.task?.result);     
            // console.log('Task result type:', typeof result.task?.result);
            
            if (result.success && result.task) {
                // 显示结果区域
                const resultsSection = document.getElementById('docking-results-section');
                if (resultsSection) {
                    resultsSection.style.display = 'block';
                    resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    console.log('Results section displayed and scrolled into view');
                } else {
                    console.error('Results section not found!');
                }
                
                // 检查任务是否有结果数据
                if (result.task.result) {
                    console.log('Task has result data, calling displayResults...');
                    // 显示结果，传递从后端获取的实际任务ID
                    displayResults(result.task.result, result.task.task_id);
                    
                    // 自动展开详细结果区域
                    setTimeout(() => {
                        const detailedResults = document.getElementById('detailed-results');
                        const toggleBtn = document.getElementById('toggle-details');
                        if (detailedResults && toggleBtn) {
                            detailedResults.style.display = 'block';
                            toggleBtn.textContent = '-';
                            console.log('Auto-expanded detailed results');
                        }
                        
                        // 如果有文件数据，自动切换到文件标签页
                        const taskResult = result.task.result;
                        let hasFiles = false;
                        
                        if (typeof taskResult === 'string') {
                            try {
                                const parsedResult = JSON.parse(taskResult);
                                hasFiles = parsedResult.results && parsedResult.results.pdbqt_files && 
                                          Object.keys(parsedResult.results.pdbqt_files).length > 0;
                            } catch (e) {
                                console.log('Could not parse task result as JSON');
                            }
                        } else if (taskResult && taskResult.results && taskResult.results.pdbqt_files) {
                            hasFiles = Object.keys(taskResult.results.pdbqt_files).length > 0;
                        }
                        
                        if (hasFiles) {
                            // 切换到文件标签页
                            const scoresTab = document.getElementById('scores-tab');
                            const filesTab = document.getElementById('files-tab');
                            const scoresBtn = document.querySelector('.tab-btn[data-tab="scores"]');
                            const filesBtn = document.querySelector('.tab-btn[data-tab="files"]');
                            
                            if (scoresTab && filesTab && scoresBtn && filesBtn) {
                                // 移除scores标签页的active状态
                                scoresTab.classList.remove('active');
                                scoresBtn.classList.remove('active');
                                
                                // 添加files标签页的active状态
                                filesTab.classList.add('active');
                                filesBtn.classList.add('active');
                                
                                console.log('Auto-switched to files tab');
                            }
                        }
                    }, 500); // 延迟500ms确保displayResults完成
                } else {
                    console.log('Task has no result data');
                }
            } else {
                showNotification(translations[currentLanguage]['task-results-unavailable'], 'warning');
            }
        } catch (error) {
            console.error('获取任务结果出错:', error);
            showNotification(translations[currentLanguage]['get-task-results-error'], 'error');
        }
    }

    // 格式化任务参数显示（隐藏文件内容，只显示文件名和大小）
    function formatTaskParameters(parameters) {
        if (!parameters) return 'N/A';
        
        try {
            // 深拷贝参数对象
            let formattedParams;
            if (typeof parameters === 'string') {
                formattedParams = JSON.parse(parameters);
            } else {
                // 如果已经是对象，进行深拷贝
                formattedParams = JSON.parse(JSON.stringify(parameters));
            }
            
            // 处理receptor文件
            if (formattedParams.receptor) {
                // const content = formattedParams.receptor.content;
                // const sizeKB = Math.round(content.length / 1024 * 100) / 100;
                formattedParams.receptor = {
                    name: formattedParams.receptor.name || '未知文件',
                    // size: `${sizeKB} KB`,
                    type: 'PDB文件'
                };
            }
            
            // 处理ligand文件
            if (formattedParams.ligand) {
                // const content = formattedParams.ligand.content;
                // const sizeKB = Math.round(content.length / 1024 * 100) / 100;
                formattedParams.ligand = {
                    name: formattedParams.ligand.name || '未知文件',
                    type: 'SDF文件'
                };
            }
            
            // 格式化显示
            return JSON.stringify(formattedParams, null, 2);
        } catch (error) {
            console.error('格式化参数时出错:', error);
            return '参数格式错误';
        }
    }

    // 显示任务详情模态框
    function showTaskDetailsModal(task) {
        // 创建模态框HTML
        const modalHtml = `
            <div id="task-details-modal" class="modal-overlay" onclick="closeTaskDetailsModal()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>${translations[currentLanguage]["task-details"]}</h3>
                        <button onclick="closeTaskDetailsModal()" class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="detail-row">
                            <label>${translations[currentLanguage]["task-id"]}</label>
                            <span class="task-id">${task.task_id}</span>
                        </div>
                        <div class="detail-row">
                            <label>${translations[currentLanguage]["task-status"]}</label>
                            <span class="task-status ${task.status}">${getStatusText(task.status)}</span>
                        </div>
                        <div class="detail-row">
                            <label>${translations[currentLanguage]["created-time"]}:</label>
                            <span>${formatDateTime(task.created_at)}</span>
                        </div>
                        <div class="detail-row">
                            <label>${translations[currentLanguage]["updated-time"]}:</label>
                            <span>${formatDateTime(task.updated_at)}</span>
                        </div>
                        ${task.completed_at ? `
                            <div class="detail-row">
                                <label>${translations[currentLanguage]["completed-time"]}:</label>
                                <span>${formatDateTime(task.completed_at)}</span>
                            </div>
                        ` : ''}
                        ${task.error_message ? `
                            <div class="detail-row">
                                <label>${translations[currentLanguage]["error-message"]}:</label>
                                <span style="color: #ef4444;">${task.error_message}</span>
                            </div>
                        ` : ''}
                        <div class="detail-row">
                            <label>${translations[currentLanguage]["parameters"]}:</label>
                            <pre class="parameters-display">${formatTaskParameters(task.parameters)}</pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }


    // 关闭任务详情模态框
    function closeTaskDetailsModal() {
        const modal = document.getElementById('task-details-modal');
        if (modal) {
            modal.remove();
        }
    }


    // 过滤和格式化人类可读结果
    function filterHumanReadableResults(humanReadableResults, resultData, lang) {
        console.log('filterHumanReadableResults called with:', {
            humanReadableResults: humanReadableResults,
            resultData: resultData,
            lang: lang,
            currentLanguage: currentLanguage
        });
        
        if (!humanReadableResults) return '';
        
        try {
            let filteredText = '';

            const targetLanguage = lang || currentLanguage;
            console.log('Using targetLanguage:', targetLanguage);
            
            // 添加任务总结信息
            if (resultData && resultData.task_id) {
                // 计算完成的分子数量
                let moleculeCount = 0;
                if (resultData.scores) {
                    moleculeCount = Object.keys(resultData.scores).length;
                    console.log('Found scores in resultData.scores, count:', moleculeCount);
                } else if (resultData.results && resultData.results.scores) {
                    moleculeCount = Object.keys(resultData.results.scores).length;
                }
                
                // 使用翻译功能显示任务总结
                const summaryTemplate = translations[targetLanguage]['task-completed-summary'];
                // 只显示任务总结，不显示每个配体的详细内容
                filteredText = summaryTemplate
                        .replace('{taskId}', resultData.task_id)
                        .replace('{count}', moleculeCount);
                
                console.log('Generated filteredText:', filteredText);
                return filteredText;
            }
            
            // 如果没有任务ID，尝试从humanReadableResults中提取总结信息
            if (typeof humanReadableResults === 'string') {
                // 如果是字符串，尝试提取总结行
                const lines = humanReadableResults.split('\n');
                for (const line of lines) {
                    // 使用多语言关键词检测
                    const keyword1 = translations[targetLanguage]['task-summary-keyword1'];
                    const keyword2 = translations[targetLanguage]['task-summary-keyword2'];
                    if (keyword1 && keyword2 && line.includes(keyword1) && line.includes(keyword2)) {
                        return line.trim();
                    }
                    
                    // 备用检测：通用任务完成关键词
                    const generalKeyword1 = translations[targetLanguage]['task-general-keyword1'];
                    const generalKeyword2 = translations[targetLanguage]['task-general-keyword2'];
                    if (generalKeyword1 && generalKeyword2 && line.includes(generalKeyword1) && line.includes(generalKeyword2)) {
                        return line.trim();
                    }
                }
                // 如果找不到总结行，返回第一行作为总结
                return lines[0] || translations[targetLanguage]['completed'];
            }
            
            // 如果是对象，尝试提取总结信息
            if (typeof humanReadableResults === 'object') {
                if (humanReadableResults.summary) {
                    return humanReadableResults.summary;
                }
                // 默认返回简单的完成信息
                return translations[targetLanguage]['completed'];
            }
            
            // 其他情况，返回简单的完成信息
            return translations[targetLanguage]['completed'];
            
        } catch (error) {
            console.error('Error filtering human readable results:', error);
            return translations[targetLanguage]['completed'];
        }
    }

    // 修改显示任务历史函数，移除分页相关的逻辑（因为现在由分页控件处理）
    function displayTaskHistory(tasks) {
        console.log('=== displayTaskHistory called ===');
        const tasksList = document.getElementById('tasks-list');
        
        if (!tasks || tasks.length === 0) {
            console.log('No tasks found, showing empty message');
            tasksList.innerHTML = `<p style="text-align: center; color: #6b7280; padding: 2rem;">${translations[currentLanguage]['no-autodock-tasks']}</p>`;
            // 没有任务时停止定期刷新
            stopPeriodicRefresh();
            return;
        }
        
        console.log('Displaying', tasks.length, 'tasks');
        // 检查是否有等待中或运行中的任务
        const hasPendingOrRunningTasks = tasks.some(task => 
            task.status === 'pending' || task.status === 'running'
        );
        
        // 根据任务状态决定是否需要定期刷新
        if (hasPendingOrRunningTasks) {
            // 如果有等待中或运行中的任务，确保定期刷新正在运行
            if (!refreshInterval) {
                startPeriodicRefresh();
            }
        } else {
            // 如果没有等待中或运行中的任务，停止定期刷新
            stopPeriodicRefresh();
        }
        
        tasksList.innerHTML = tasks.map(task => `
            <div class="task-item" data-task-id="${task.task_id}">
                <div class="task-header">
                    <span class="task-id">${task.task_id}</span>
                    <span class="task-status ${task.status}">${getStatusText(task.status)}</span>
                </div>
                <div class="task-info">
                    <div>${translations[currentLanguage]['created-time']}: ${formatDateTime(task.created_at)}</div>
                    <div>${translations[currentLanguage]['updated-time']}: ${formatDateTime(task.updated_at)}</div>
                    ${task.completed_at ? `<div>${translations[currentLanguage]['completed-time']}: ${formatDateTime(task.completed_at)}</div>` : ''}
                </div>
                <div class="task-actions">
                    <button class="task-btn view" onclick="viewTaskDetails('${task.task_id}')">${translations[currentLanguage]["view-task-details"]}</button>
                    ${task.status === 'completed' ? `<button class="task-btn view" onclick="showTaskResults('${task.task_id}')">${translations[currentLanguage]["view-task-results"]}</button>` : ''}
                </div>
            </div>
        `).join('');
    }

    // 获取状态文本
    function getStatusText(status) {
        const statusMap = {
            'pending': translations[currentLanguage]['pending'],
            'running': translations[currentLanguage]['running'],
            'completed': translations[currentLanguage]['completed'],
            'failed': translations[currentLanguage]['failed']
        };
        return statusMap[status] || status;
    }

    // 格式化日期时间
    function formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    // 查看任务详情
    async function viewTaskDetails(taskId) {
        try {
            const response = await fetch(`/api/tasks/${taskId}`);
            const result = await response.json();
            
            if (result.success) {
                showTaskDetailsModal(result.task);
            } else {
                showNotification(translations[currentLanguage]['get-task-details-failed'] + ' ' + result.message, 'error');
            }
        } catch (error) {
            console.error('获取任务详情出错:', error);
            showNotification(translations[currentLanguage]['get-task-details-error'], 'error');
        }
    }

    // // 加载任务历史
    // async function loadTaskHistory() {
    //     console.log('=== loadTaskHistory called ===');
    //     try {
    //         console.log('Fetching tasks from /api/tasks?task_type=autodock...');
    //         const response = await fetch('/api/tasks?task_type=autodock');
    //         console.log('Response status:', response.status);
    //         const result = await response.json();
    //         // console.log('API result:', result);
            
    //         if (result.success) {
    //             // console.log('Tasks data:', result.tasks);
    //             displayTaskHistory(result.tasks);
                
    //             // 处理分页信息（如果有）
    //             if (result.pagination) {
    //                 console.log(`AutoDock任务总数: ${result.pagination.total_items}, 当前页: ${result.pagination.current_page}/${result.pagination.total_pages}`);
    //                 // 这里可以添加分页UI的更新代码
    //             }
    //         } else {
    //             console.error('加载任务历史失败:', result.message);
    //             // 如果API调用失败，显示空状态
    //             displayTaskHistory([]);
    //         }
    //     } catch (error) {
    //         console.error('加载任务历史出错:', error);
    //         // 如果网络错误或其他异常，显示空状态
    //         displayTaskHistory([]);
    //     }
    // }

    // 修改原有的loadTaskHistory函数以支持分页
    async function loadTaskHistory() {
        console.log('=== loadTaskHistory called (redirecting to page 1) ===');
        await loadTaskHistoryPage(1);
    }


    // 定期刷新相关函数

    // 开始定期刷新
    function startPeriodicRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
        // 每30秒刷新一次任务状态
        refreshInterval = setInterval(() => {
            console.log('定期刷新任务状态...');
            loadTaskHistoryPage(currentPage);
        }, 30000);
        console.log('已启动定期刷新');
    }

    // 停止定期刷新
    function stopPeriodicRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
            console.log('已停止定期刷新');
        }
    }

    // 初始化蛋白质文件选择功能
    function initializeProteinFileSelection() {
        console.log('AutoDock: Initializing protein file selection interface...');
        
        // 获取界面元素
        const proteinFilesGrid = document.getElementById('protein-files-grid');
        const localProteinInput = document.getElementById('local-protein-input');
        const proteinDropZone = document.getElementById('protein-drop-zone');
        const selectedProteinDisplay = document.getElementById('selected-protein-display');
        const selectedProteinInfo = document.getElementById('selected-protein-info');
        const clearProteinSelectionBtn = document.getElementById('clear-protein-selection-btn');
        
        // 存储选中的蛋白质文件（单选）
        let selectedProteinFile = null;
        
        // 更新已上传蛋白质文件网格
        function updateProteinFilesGrid() {
            if (!proteinFilesGrid) return;
            
            if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
                // 过滤出PDB和PDBQT文件
                const proteinFiles = uploadedFilesData.filter(file => 
                    file.type.toLowerCase() === 'pdb' || file.type.toLowerCase() === 'pdbqt'
                );
                
                if (proteinFiles.length > 0) {
                    proteinFilesGrid.innerHTML = '';
                    proteinFiles.forEach(file => {
                        const fileCard = document.createElement('div');
                        fileCard.className = 'file-card';
                        fileCard.dataset.fileId = file.id;
                        fileCard.innerHTML = `
                            <div class="file-card-header">
                                <span class="file-type-icon">${getFileTypeIcon(file.type)}</span>
                                <span class="file-name" title="${file.name}">${file.name}</span>
                            </div>
                            <div class="file-info">${file.type.toUpperCase()} • ${formatFileSize(file.size || 0)}</div>
                            <div class="selection-indicator">✓</div>
                        `;
                        
                        // 添加点击事件（单选）
                        fileCard.addEventListener('click', function() {
                            selectProteinFile(file, fileCard);
                        });
                        
                        proteinFilesGrid.appendChild(fileCard);
                    });
                } else {
                    proteinFilesGrid.innerHTML = `<div class="no-files-message"><span data-i18n="no-protein-files">${translations[currentLanguage]['no-protein-files'] || '暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件'}</span></div>`;
                }
            } else {
                proteinFilesGrid.innerHTML = `<div class="no-files-message"><span data-i18n="no-protein-files">${translations[currentLanguage]['no-protein-files'] || '暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件'}</span></div>`;
            }
        }
        
        // 将函数引用存储到全局变量
        globalUpdateProteinFilesGrid = updateProteinFilesGrid;
        
        // 选择蛋白质文件（单选模式）
        function selectProteinFile(file, fileCard) {
            // 清除之前的选择
            proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 选择新文件
            selectedProteinFile = {
                id: file.id,
                name: file.name,
                type: file.type,
                content: file.content,
                source: 'uploaded'
            };
            fileCard.classList.add('selected');
            
            updateSelectedProteinDisplay();
        }
        
        // 本地蛋白质文件上传处理
        if (localProteinInput && proteinDropZone) {
            // 点击上传区域
            proteinDropZone.addEventListener('click', function() {
                localProteinInput.click();
            });
            
            // 文件选择处理
            localProteinInput.addEventListener('change', function(e) {
                handleLocalProteinUpload(e.target.files);
            });
            
            // 拖拽上传处理
            proteinDropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            proteinDropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            proteinDropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                handleLocalProteinUpload(e.dataTransfer.files);
            });
        }
        
        // 处理本地蛋白质文件上传
        function handleLocalProteinUpload(files) {
            if (files.length > 0) {
                const file = files[0]; // 只取第一个文件（单选）
                const reader = new FileReader();
                reader.onload = function(e) {
                    // 清除之前的选择
                    proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                        card.classList.remove('selected');
                    });
                    
                    selectedProteinFile = {
                        id: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                        name: file.name,
                        type: getFileType(file.name),
                        content: e.target.result,
                        source: 'local'
                    };
                    
                    updateSelectedProteinDisplay();
                    showNotification(`${translations[currentLanguage]['protein-file-selected'] || '已选择蛋白质文件'}: ${file.name}`, 'success');
                };
                reader.readAsText(file);
            }
            
            // 清空input
            localProteinInput.value = '';
        }
        
        // 更新已选蛋白质文件显示
        function updateSelectedProteinDisplay() {
            if (!selectedProteinDisplay || !selectedProteinInfo) return;
            
            if (selectedProteinFile) {
                selectedProteinDisplay.style.display = 'block';
                
                const localText = selectedProteinFile.source === 'local' ? 
                ` - ${translations[currentLanguage]['local'] || '本地'}` : '';
                selectedProteinInfo.innerHTML = `
                    <div class="selected-file-item">
                        <span class="file-name">${selectedProteinFile.name} (${selectedProteinFile.type.toUpperCase()})${localText}</span>
                        <button class="remove-file-btn" id="remove-protein-btn">×</button>
                    </div>
                `;
                    
                // 添加删除按钮事件
                document.getElementById('remove-protein-btn').addEventListener('click', function() {
                    selectedProteinFile = null;
                    
                    // 清除网格中的选中状态
                    proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                        card.classList.remove('selected');
                    });
                    
                    updateSelectedProteinDisplay();
                });
            } else {
                selectedProteinDisplay.style.display = 'none';
            }
        }
        
        // 清空选择按钮
        if (clearProteinSelectionBtn) {
            clearProteinSelectionBtn.addEventListener('click', function() {
                selectedProteinFile = null;
                
                // 清除网格中的选中状态
                proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                    card.classList.remove('selected');
                });
                
                updateSelectedProteinDisplay();
            });
        }
        
        // 初始化时更新文件网格
        updateProteinFilesGrid();
        
        // 提供全局访问接口
        window.getSelectedProteinFile = function() {
            return selectedProteinFile;
        };
    }

    // 初始化按钮事件
    // 防止重复绑定按钮事件的标志
    let buttonsInitialized = false;
    function initializeButtonEvents() {
        console.log('AutoDock: Initializing button events...');

        // 防止重复绑定
        if (buttonsInitialized) {
            console.log('AutoDock: Button events already initialized, skipping...');
            return;
        }
        buttonsInitialized = true;
        // 绑定收起/展开按钮
        const toggleDetailsBtn = document.getElementById('toggle-details');
        if (toggleDetailsBtn) {
            toggleDetailsBtn.addEventListener('click', function() {
                console.log('AutoDock: Toggle details button clicked');
                toggleDetails();
            });
            console.log('AutoDock: Toggle details button event bound');
        } else {
            console.error('AutoDock: Toggle details button not found');
        }

        // 绑定"隐藏结果"按钮
        const hideResultsBtn = document.getElementById('hide-results-btn');
        if (hideResultsBtn) {
            hideResultsBtn.addEventListener('click', function() {
                console.log('AutoDock: Hide results button clicked');
                hideResults();
            });
            console.log('AutoDock: Hide results button event bound');
        } else {
            console.error('AutoDock: Hide results button not found');
        }
        
        // 绑定"开始对接"按钮
        const submitBtn = document.getElementById('submit-btn');
        if (submitBtn) {
            submitBtn.addEventListener('click', function() {
                console.log('AutoDock: Submit button clicked');
                startDocking();
            });
            console.log('AutoDock: Submit button event bound');
        } else {
            console.error('AutoDock: Submit button not found');
        }
        
        // 绑定"验证输入"按钮
        const validateBtn = document.getElementById('validate-inputs');
        if (validateBtn) {
            validateBtn.addEventListener('click', function() {
                console.log('AutoDock: Validate button clicked');
                validateInputs();
            });
            console.log('AutoDock: Validate button event bound');
        }
        
        // 绑定"重置参数"按钮
        const resetBtn = document.getElementById('reset-inputs');
        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                console.log('AutoDock: Reset button clicked');
                resetForm();
            });
            console.log('AutoDock: Reset button event bound');
        }
        
        // 绑定"自动填入"坐标按钮
        const autoFillBtn = document.getElementById('auto-fill-coords');
        if (autoFillBtn) {
            autoFillBtn.addEventListener('click', function() {
                console.log('AutoDock: Auto-fill coordinates button clicked');
                calculateAndSetGeometricCenter();
            });
            console.log('AutoDock: Auto-fill coordinates button event bound');
        }
        
        // 绑定刷新任务按钮
        const refreshTasksBtn = document.getElementById('refresh-tasks-btn');
        if (refreshTasksBtn) {
            refreshTasksBtn.addEventListener('click', function() {
                console.log('AutoDock: Refresh tasks button clicked');
                loadTaskHistoryPage(currentPage);
            });
            console.log('AutoDock: Refresh tasks button event bound');
        }
        
        console.log('AutoDock: Button events initialized successfully');

        // 绑定 CSV 文件上传按钮
        const uploadCsvBtn = document.getElementById('upload-csv-btn');
        const csvFileInput = document.getElementById('csv-file-input');
        if (uploadCsvBtn && csvFileInput) {
            uploadCsvBtn.addEventListener('click', function() {
                console.log('AutoDock: CSV upload button clicked');
                csvFileInput.click();
            });
            
            csvFileInput.addEventListener('change', function(e) {
                console.log('AutoDock: CSV file selected');
                handleCSVUpload(e.target.files);
            });
            
            console.log('AutoDock: CSV upload button events bound');
        } else {
            console.error('AutoDock: CSV upload elements not found');
        }
        
        // 绑定 SMILES 文本解析按钮
        const parseSmilesTextBtn = document.getElementById('parse-smiles-text-btn');
        if (parseSmilesTextBtn) {
            parseSmilesTextBtn.addEventListener('click', function() {
                console.log('AutoDock: Parse SMILES text button clicked');
                handleSmilesTextParsing();
            });
            console.log('AutoDock: Parse SMILES text button event bound');
        } else {
            console.error('AutoDock: Parse SMILES text button not found');
        }
    }

    // 处理 CSV 文件上传
function handleCSVUpload(files) {
    if (files.length === 0) return;
    
    const file = files[0];
    if (!file.name.toLowerCase().endsWith('.csv')) {
        showNotification(translations[currentLanguage]['please-select-csv'], 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csvContent = e.target.result;
            const molecules = parseCSVContent(csvContent);
            
            if (molecules.length === 0) {
                showNotification(translations[currentLanguage]['no-valid-molecules-in-csv'], 'error');
                return;
            }
            
            // 显示分子列表
            displayBatchMolecules(molecules);
            showNotification(`${translations[currentLanguage]['successfully-parsed-molecules']} ${molecules.length} ${translations[currentLanguage]['molecules']}`, 'success');
            
        } catch (error) {
            console.error('CSV 解析错误:', error);
            showNotification(translations[currentLanguage]['csv-parse-failed'] + ' ' + error.message, 'error');
        }
    };
    
    reader.readAsText(file);
    
    // 清空 input
    document.getElementById('csv-file-input').value = '';
}

// 解析 CSV 内容
function parseCSVContent(csvContent) {
    const lines = csvContent.trim().split('\n');
    const molecules = [];
    
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        // 简单的 CSV 解析（假设用逗号分隔）
        const parts = line.split(',');
        if (parts.length >= 2) {
            const name = parts[0].trim().replace(/"/g, '');
            const smiles = parts[1].trim().replace(/"/g, '');
            
            if (name && smiles) {
                molecules.push({
                    name: name,
                    smiles: smiles,
                    id: 'csv_' + i + '_' + Date.now()
                });
            }
        }
    }
    
    return molecules;
}

// 处理 SMILES 文本解析
function handleSmilesTextParsing() {
    const textArea = document.getElementById('batch-smiles-text');
    if (!textArea) {
        showNotification(translations[currentLanguage]['text-input-area-not-found'], 'error');
        return;
    }
    
    const smilesText = textArea.value.trim();
    if (!smilesText) {
        showNotification(translations[currentLanguage]['please-input-smiles'], 'warning');
        return;
    }
    
    try {
        const lines = smilesText.split('\n');
        const molecules = [];
        
        for (let i = 0; i < lines.length; i++) {
            const smiles = lines[i].trim();
            if (smiles) {
                molecules.push({
                    name: `Molecule_${i + 1}`,
                    smiles: smiles,
                    id: 'text_' + i + '_' + Date.now()
                });
            }
        }
        
        if (molecules.length === 0) {
            showNotification(translations[currentLanguage]['no-valid-smiles-found'], 'warning');
            return;
        }
        
        // 显示分子列表
        displayBatchMolecules(molecules);
        showNotification(`${translations[currentLanguage]['successfully-parsed-molecules']} ${molecules.length} ${translations[currentLanguage]['molecules']}`, 'success');
        
    } catch (error) {
        console.error('SMILES 文本解析错误:', error);
        showNotification(translations[currentLanguage]['smiles-parse-failed'] + ' ' + error.message, 'error');
    }
}

// 显示批量分子列表
function displayBatchMolecules(molecules) {
    const batchContainer = document.querySelector('.batch-molecules-container');
    const moleculesList = document.getElementById('molecules-list');
    const moleculeCount = document.getElementById('molecule-count');
    
    if (!batchContainer || !moleculesList || !moleculeCount) {
        console.error('批量分子显示元素未找到');
        return;
    }
    
    // 显示容器
    batchContainer.style.display = 'block';
    
    // 更新分子数量
    moleculeCount.textContent = molecules.length;
    
    // 生成分子列表
    moleculesList.innerHTML = '';
    molecules.forEach((molecule, index) => {
        const moleculeItem = document.createElement('div');
        moleculeItem.className = 'molecule-item';
        moleculeItem.innerHTML = `
            <div class="molecule-info">
                <div class="molecule-name">${molecule.name}</div>
                <div class="molecule-smiles">${molecule.smiles}</div>
            </div>
        `;
        
        // 添加点击事件显示 2D 结构
        moleculeItem.addEventListener('click', function() {
            // 移除其他选中状态
            moleculesList.querySelectorAll('.molecule-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选中当前项
            this.classList.add('selected');
            
            // 显示 2D 结构（如果有 SMILES 绘制功能）
            displayMolecule2D(molecule.smiles, molecule.name);
        });
        
        moleculesList.appendChild(moleculeItem);
        
    });
    
    // 存储分子数据以供后续使用
    window.currentBatchMolecules = molecules;
    window.batchMolecules = molecules;
    // 重要：同时设置全局的batchMolecules变量
    batchMolecules = molecules;
}

// 显示 2D 分子结构（如果有 SMILES 绘制库）
function displayMolecule2D(smiles, name) {
    const canvas = document.getElementById('structure2d');
    const moleculeInfo = document.getElementById('molecule-info');
    
    if (!canvas || !moleculeInfo) return;
    
    // 更新分子信息
    moleculeInfo.innerHTML = `
        <div><strong>名称:</strong> ${name}</div>
        <div><strong>SMILES:</strong> ${smiles}</div>
    `;
    
    // 如果有 SMILES 绘制库（如 SMILES-Drawer），在这里绘制 2D 结构
    if (typeof SmilesDrawer !== 'undefined') {
        try {
            const smilesDrawer = new SmilesDrawer.Drawer({
                width: 300,
                height: 200
            });
            
            SmilesDrawer.parse(smiles, function(tree) {
                smilesDrawer.draw(tree, canvas, 'light', false);
            });
        } catch (error) {
            console.error('绘制 2D 结构失败:', error);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('无法绘制 2D 结构', canvas.width / 2, canvas.height / 2);
        }
    } else {
        // 如果没有绘制库，显示占位文本
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#666';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('2D 结构预览', canvas.width / 2, canvas.height / 2);
    }
}

    // 分页相关变量
    let currentPage = 1;
    let totalPages = 1;
    let totalItems = 0;
    const pageSize= 10; // 每页显示10条任务

    // 加载指定页码的任务历史
    async function loadTaskHistoryPage(page = 1) {
        console.log(`=== loadTaskHistoryPage called with page: ${page} ===`);
        
        // 验证页码
        if (page < 1) {
            page = 1;
        }
        if (page > totalPages && totalPages > 0) {
            page = totalPages;
        }
        
        currentPage = page;
        
        try {
            const url = `/api/tasks?task_type=autodock&page=${page}&page_size=${pageSize}&include_content=false`;
            console.log('Fetching tasks from:', url);
            
            const response = await fetch(url);
            console.log('Response status:', response.status);
            const result = await response.json();
            
            if (result.success) {
                console.log('Tasks data:', result.tasks);
                
                // 更新分页信息
                if (result.pagination) {
                    currentPage = result.pagination.current_page;
                    totalPages = result.pagination.total_pages;
                    totalItems = result.pagination.total_items;
                    console.log(`AutoDock任务总数: ${totalItems}, 当前页: ${currentPage}/${totalPages}`);
                } else {
                    // 兼容没有分页信息的情况
                    totalPages = 1;
                    totalItems = result.tasks ? result.tasks.length : 0;
                }
                
                displayTaskHistory(result.tasks);
                updatePaginationControls();
                
            } else {
                console.error('加载任务历史失败:', result.message);
                displayTaskHistory([]);
                updatePaginationControls();
            }
        } catch (error) {
            console.error('加载任务历史出错:', error);
            displayTaskHistory([]);
            updatePaginationControls();
        }
    }


    // 更新分页控件
    function updatePaginationControls() {
        const paginationContainer = document.getElementById('tasks-pagination');
        const paginationInfoText = document.getElementById('pagination-info-text');
        const prevBtn = document.getElementById('prev-page-btn');
        const nextBtn = document.getElementById('next-page-btn');
        const pageNumbers = document.getElementById('page-numbers');
        
        if (!paginationContainer) return;
        
        // 只要有任务就显示分页控件（与DiffDock保持一致）
        if (totalItems > 0) {
            paginationContainer.style.display = 'flex';
        } else {
            paginationContainer.style.display = 'none';
            return;
        }
        
        // 显示分页控件
        paginationContainer.style.display = 'flex';
        
        // 更新分页信息文本
        const startItem = (currentPage - 1) * pageSize + 1;
        const endItem = Math.min(currentPage * pageSize, totalItems);
        const t = translations[currentLanguage];
        
        if (paginationInfoText) {
            if (currentLanguage === 'zh') {
                paginationInfoText.textContent = `显示 ${startItem}-${endItem} 条，共 ${totalItems} 条任务`;
            } else {
                paginationInfoText.textContent = `Showing ${startItem}-${endItem} of ${totalItems} tasks`;
            }
        }
        
        // 更新上一页按钮状态
        if (prevBtn) {
            prevBtn.disabled = currentPage <= 1;
        }
        
        // 更新下一页按钮状态
        if (nextBtn) {
            nextBtn.disabled = currentPage >= totalPages;
        }
        
        // 生成页码按钮
        if (pageNumbers) {
            pageNumbers.innerHTML = generatePageNumbers();
        }
    }

    // 生成页码按钮HTML
    function generatePageNumbers() {
        const maxVisiblePages = 7; // 最多显示7个页码按钮
        let startPage = 1;
        let endPage = totalPages;
        
        if (totalPages > maxVisiblePages) {
            const halfVisible = Math.floor(maxVisiblePages / 2);
            startPage = Math.max(1, currentPage - halfVisible);
            endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
            
            // 调整开始页码，确保显示完整的页码范围
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }
        }
        
        let html = '';
        
        // 第一页和省略号
        if (startPage > 1) {
            html += `<button class="page-number" onclick="loadTaskHistoryPage(1)">1</button>`;
            if (startPage > 2) {
                html += `<span class="page-number ellipsis">...</span>`;
            }
        }
        
        // 页码范围
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            html += `<button class="page-number ${activeClass}" onclick="loadTaskHistoryPage(${i})">${i}</button>`;
        }
        
        // 省略号和最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<span class="page-number ellipsis">...</span>`;
            }
            html += `<button class="page-number" onclick="loadTaskHistoryPage(${totalPages})">${totalPages}</button>`;
        }
        
        return html;
    }


</script>
{% endblock %}