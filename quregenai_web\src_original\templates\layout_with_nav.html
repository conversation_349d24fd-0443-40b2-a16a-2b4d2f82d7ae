<!--
    Layout with Navigation
    带导航栏的布局模板

    功能: 提供包含顶部导航栏和侧边栏的完整页面布局
    继承: base.html
    组件: header.html, sidebar.html
    被继承: 所有功能页面模板

    使用示例:
    {# extends "layout_with_nav.html" #}
    {# block content #}页面内容{# endblock #}
-->
{% extends "base.html" %}

{% block extra_css %}
<!-- 导航栏样式 -->
<link rel="stylesheet" href="/src/styles/navigation.css">
<!-- 页面特定样式 -->
{% block page_css %}{% endblock %}
{% endblock %}

{% block body_class %}layout-with-nav{% endblock %}

{% block body %}
    <!-- 顶部导航栏 -->
    {% include 'components/header.html' %}
    
    <!-- 主容器 -->
    <div class="main-container">
        <!-- 侧边导航栏 -->
        {% include 'components/sidebar.html' %}
        
        <!-- 页面内容区域 -->
        <main class="content">
            {% block content %}
            <!-- 页面具体内容在这里 -->
            {% endblock %}
        </main>
    </div>
{% endblock %}

{% block extra_js %}
<!-- 导航栏脚本 -->
<script src="/src/scripts/navigation.js"></script>
<script src="/src/scripts/i18n.js"></script>
<!-- 页面特定脚本 -->
{% block page_js %}{% endblock %}
{% endblock %}
