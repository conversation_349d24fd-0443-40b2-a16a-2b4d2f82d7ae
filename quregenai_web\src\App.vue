<script>
import Layout from './components/layout/Layout.vue'
import { loginHeartbeat, isLogin } from './api/login'

export default {
  name: 'App',
  components: {
    Layout
  },
  data() {
    return {
      heartbeatInterval: null
    }
  },
  mounted() {
    this.checkLoginStatus()
    // 设置登录心跳检查，每5分钟检查一次
    this.heartbeatInterval = setInterval(this.checkHeartbeat, 5 * 60 * 1000)
  },
  beforeDestroy() {
    // 清除定时器
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }
  },
  methods: {
    checkLoginStatus() {
      // 如果本地有登录状态，则检查服务器登录状态
      if (localStorage.getItem('isLoggedIn')) {
        isLogin()
          .then(data => {
            if (!data.loginStatus) {
              // 如果服务器显示未登录，清除本地登录状态
              localStorage.removeItem('isLoggedIn')
              localStorage.removeItem('mobile')
              localStorage.removeItem('user_id')
              localStorage.removeItem('lastLoginTime')
              // 如果不在登录页，则重定向到登录页
              if (this.$route && this.$route.path !== '/login') {
                this.$router.push('/login')
              }
            }
          })
          .catch(error => {
            console.error('登录状态检查错误:', error)
          })
      }
    },
    checkHeartbeat() {
      // 如果已登录，则发送心跳请求
      if (localStorage.getItem('isLoggedIn')) {
        loginHeartbeat()
          .then(data => {
            if (!data.heartbeatStatus) {
              // 心跳失败，可能是登录已过期
              localStorage.removeItem('isLoggedIn')
              localStorage.removeItem('mobile')
              localStorage.removeItem('user_id')
              localStorage.removeItem('lastLoginTime')
              // 如果不在登录页，则重定向到登录页
              if (this.$route && this.$route.path !== '/login') {
                this.$router.push('/login')
              }
            }
          })
          .catch(error => {
            console.error('心跳检查错误:', error)
          })
      }
    }
  }
}
</script>

<template>
  <Layout>
    <router-view />
  </Layout>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  background: #f9fafb;
  margin: 0;
  padding: 0;
}

button:focus {
  outline: none;
}
</style>
