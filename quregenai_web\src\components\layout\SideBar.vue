<template>
  <nav class="sidebar" :class="{ open: sidebarOpen }">
    <h2 class="sidebar-title">{{ t('home.sidebar-title') }}</h2>
    <ul class="module-list">
      <li class="module-item">
        <router-link to="/autodock" class="module-link">
          <div class="module-icon">
            <img src="/src/assets/images/autodock-cube.svg" alt="AutoDock">
          </div>
          <div>
            <div class="module-name">AutoDock</div>
            <div class="module-description">{{ t('modules.autodock-desc') }}</div>
          </div>
        </router-link>
      </li>
      <li class="module-item">
        <router-link to="/diffdock" class="module-link">
          <div class="module-icon">
            <img src="/src/assets/images/diffdock-tetrahedral-methane.svg" alt="DiffDock">
          </div>
          <div>
            <div class="module-name">DiffDock</div>
            <div class="module-description">{{ t('modules.diffdock-desc') }}</div>
          </div>
        </router-link>
      </li>
      <li class="module-item">
        <router-link to="/protenix" class="module-link">
          <div class="module-icon">
            <img src="/src/assets/images/protenix-protein.svg" alt="Protenix">
          </div>
          <div>
            <div class="module-name">Protenix</div>
            <div class="module-description">{{ t('modules.protenix-desc') }}</div>
          </div>
        </router-link>
      </li>

      <li class="module-item">
        <router-link to="/molmap" class="module-link">
          <div class="module-icon">🧪</div>
          <div>
            <div class="module-name">MolMap</div>
            <div class="module-description">{{ t('modules.molmap-desc') }}</div>
          </div>
        </router-link>
      </li>
      <li class="module-item">
        <router-link to="/pocketvina" class="module-link">
          <div class="module-icon">🎯</div>
          <div>
            <div class="module-name">PocketVina</div>
            <div class="module-description">{{ t('modules.pocketvina-desc') }}</div>
          </div>
        </router-link>
      </li>
      <li class="module-item">
        <router-link to="/raman" class="module-link">
          <div class="module-icon">⚡</div>
          <div>
            <div class="module-name">Raman Spectral</div>
            <div class="module-description">{{ t('modules.raman-desc') }}</div>
          </div>
        </router-link>
      </li>
      <li class="module-item">
        <router-link to="/quantum_tasks" class="module-link">
          <div class="module-icon">⚛️</div>
          <div>
            <div class="module-name">{{ t('modules.quantum-tasks-name') }}</div>
            <div class="module-description">{{ t('modules.quantum-tasks-desc') }}</div>
          </div>
        </router-link>
      </li>
      <li class="module-item">
        <router-link to="/userprofile" class="module-link">
          <div class="module-icon">👤</div>
          <div>
            <div class="module-name">{{ t('modules.userprofile-name') }}</div>
            <div class="module-description">{{ t('modules.userprofile-desc') }}</div>
          </div>
        </router-link>
      </li>
    </ul>
  </nav>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { t, currentLanguage } from '../../utils/i18n'

export default {
  name: 'SideBar',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const sidebarOpen = ref(props.modelValue)

    // 监听窗口大小变化以响应式调整侧边栏
    onMounted(() => {
      window.addEventListener('resize', handleResize)

      // 清理事件监听器
      return () => {
        window.removeEventListener('resize', handleResize)
      }
    })

    // 响应式布局：处理窗口大小变化
    const handleResize = () => {
      if (window.innerWidth > 768) {
        updateSidebarState(false)
      }
    }

    // 更新侧边栏状态并向父组件发出事件
    const updateSidebarState = (state) => {
      sidebarOpen.value = state
      emit('update:modelValue', state)
    }

    return {
      sidebarOpen,
      currentLanguage,
      t
    }
  }
}
</script>

<style scoped>
.sidebar {
  width: 280px;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  padding: 2rem 0;
  overflow-y: auto;
  /* 设置最大高度，确保不会超出视窗 */
  max-height: calc(100vh - 60px);
  height: calc(100vh - 60px);
  position: fixed;
  top: 60px;
  left: 0;
}

.sidebar-title {
  padding: 0 2rem;
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  /* 固定在顶部，不随滚动移动 */
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  padding-top: 1rem;
  padding-bottom: 1rem;
}

/* 自定义滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.module-list {
  list-style: none;
  padding-bottom: 1rem;
}

.module-item {
  margin-bottom: 0.5rem;
}

.module-link {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  color: #666;
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
}

.module-link:hover {
  background-color: #f8f9fa;
  color: #333;
  border-left-color: #8fa8f3;
}

.module-link.router-link-active {
  background-color: #8fa8f3;
  color: white;
  border-left-color: #7b92f0;
}

.module-icon {
  width: 28px;
  height: 28px;
  margin-right: 1rem;
  opacity: 0.7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.module-name {
  font-weight: 500;
}

.module-description {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    bottom: 0;
    top: 60px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
  }

  .sidebar.open {
    transform: translateX(0);
  }
}
</style>