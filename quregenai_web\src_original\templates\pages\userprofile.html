{% extends "layout_with_nav.html" %}

{% block title %}分子设计平台 - 个人中心{% endblock %}

{% block page_css %}
<link rel="stylesheet" href="/src/styles/pages/userprofile.css">
{% endblock %}

{% block content %}
<section class="profile-section">
    <div class="profile-header">
        <div>
            <h1 class="profile-title" data-i18n="profile-title">个人中心</h1>
            <p class="profile-subtitle" data-i18n="profile-subtitle">管理您的个人账户和 API 访问</p>
        </div>
    </div>
    
    <div class="user-profile-card">
        <div class="large-avatar" id="largeAvatar">U</div>
        <div class="user-profile-info">
            <h2 class="user-profile-name" id="profileUsername">用户名</h2>
            <!-- <div class="user-profile-email" id="profileEmail"><EMAIL></div> -->
            <div class="qau-balance-row">
                <div class="qau-balance">
                    <span data-i18n="qau-balance-label">QAU 余额</span>: <span id="qauBalance">1000</span>
                </div>
                <button class="recharge-btn" id="rechargeBtn" data-i18n="recharge-btn">充值 QAU</button>
            </div>
            <div class="qau-description">
                <div class="qau-info-left">
                    <div class="qau-info-title" data-i18n="qau-info-title">什么是 QAU？</div>
                    <div class="qau-info-text" data-i18n="qau-info-text">
                        QAU（Quantum AI Unit）是医图生科QureGenAI与TyxonQ太玄量子的通用计费单元。<br>
                        <strong>1 QAU = 0.1 元人民币</strong>，平台中所有计费项目均以QAU单元计费。
                    </div>
                </div>

            </div>

            <div class="qau-billing-rules">
                <div class="billing-rules-header">
                    <div class="billing-rules-title" data-i18n="billing-rules-title">QureGenAI平台计费规则</div>
                </div>
                <div class="billing-rules-content">
                    <div class="billing-grid">
                        <div class="billing-card config-card">
                            <div class="billing-card-header">
                                <div class="billing-card-icon">⚙️</div>
                                <h4 class="billing-card-title" data-i18n="config-title">T0机型配置</h4>
                            </div>
                            <div class="billing-card-content">
                                <ul>
                                    <li data-i18n="config-gpu">GPU：NVIDIA Tesla（16GB显存）</li>
                                    <li data-i18n="config-cpu">CPU：8核</li>
                                    <li data-i18n="config-memory">内存：32GB</li>
                                    <li data-i18n="config-more">（更多机型即将开放）</li>
                                </ul>
                            </div>
                        </div>

                        <div class="billing-card pricing-card">
                            <div class="billing-card-header">
                                <div class="billing-card-icon">💎</div>
                                <h4 class="billing-card-title" data-i18n="pricing-title">计费标准</h4>
                            </div>
                            <div class="billing-card-content">
                                <div class="price-highlight">
                                    <span data-i18n="pricing-ai-label">AI任务：</span><strong data-i18n="pricing-ai-value">30 QAU/分钟</strong>
                                </div>
                                <div style="font-size: 0.8rem; color: #666; margin-bottom: 0.5rem;" data-i18n="pricing-ai-desc">按实际使用分钟数计费</div>
                                <div class="price-highlight">
                                    <span data-i18n="pricing-quantum-label">量子计算：</span><strong data-i18n="pricing-quantum-value">15 QAU/量子比特秒</strong>
                                </div>
                                <div style="font-size: 0.8rem; color: #666;" data-i18n="pricing-quantum-desc">量子比特数×使用时间</div>
                            </div>
                        </div>

                        <div class="billing-card rules-card">
                            <div class="billing-card-header">
                                <div class="billing-card-icon">📋</div>
                                <h4 class="billing-card-title" data-i18n="rules-title">计费规则</h4>
                            </div>
                            <div class="billing-card-content">
                                <ul>
                                    <li data-i18n="rules-minimum">按实际运行秒计算</li>
                                    <li data-i18n="rules-balance">执行前需确保账户余额充足</li>
                                    <li data-i18n="rules-records">使用记录可在控制台查询</li>
                                </ul>
                                <div style="margin-top: 1rem; padding: 0.75rem; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 0.5rem; font-size: 0.8rem;">
                                    <div style="color: #4f46e5; font-weight: 600; margin-bottom: 0.25rem;" data-i18n="rules-version">版本：ver0.1.1</div>
                                    <div style="color: #059669; font-weight: 500;" data-i18n="rules-date">生效时间：2025年7月20日</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 将现有的活动充值区域替换为以下更美观的版本 -->
            <div class="activity-recharge-container">
                <div class="activity-header">
                    <div class="activity-icon">🎁</div>
                    <div>
                        <div class="activity-title" data-i18n="activity-title">新用户福利</div>
                        <div class="activity-subtitle" data-i18n="activity-subtitle">输入神秘口号，免费领取QAU</div>
                    </div>
                </div>
                
                <div class="activity-input-group">
                    <div class="activity-input-wrapper">
                        <input type="text" 
                            id="activitySlogan" 
                            class="activity-input" 
                            placeholder="请输入活动口号..." 
                            data-placeholder-zh="请输入活动口号..."
                            data-placeholder-en="Enter activity slogan..."
                            maxlength="20"
                            autocomplete="off">
                    </div>
                    <button class="activity-btn" id="activityBtn" onclick="claimActivityRecharge()">
                        <span data-i18n="claim-activity-btn">立即领取</span>
                    </button>
                </div>
                
                <div class="activity-hint">
                    <span data-i18n="activity-hint">输入正确的活动口号可免费领取100 QAU，每个用户限领一次</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 这里将添加 API Key 管理、充值记录和任务列表 -->
    <!-- API Key 管理 -->
    <div class="api-key-section">
        <h2 class="section-title" data-i18n="api-key-title">API 密钥</h2>
        <div id="apiKeyContainer" class="api-key-container">
            <div class="api-key-header">
                <div class="api-key-name" id="apiKeyName">默认 API 密钥</div>
                <div class="api-key-date" id="apiKeyDate">创建于 2023-10-15</div>
            </div>
            <div class="api-key-value">
                <span id="apiKeyValue">••••••••••••••••••••••••••••••</span>
                <div style="display: flex; gap: 0.5rem;">
                    <button class="copy-btn" onclick="copyApiKey()" data-i18n="copy-btn">复制</button>
                    <button class="copy-btn" style="color: #e74c3c;" onclick="showDeleteKeyModal()" data-i18n="delete-btn">删除</button>
                </div>
            </div>
        </div>
        <button class="generate-key-btn" id="generateKeyBtn" data-i18n="generate-key-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
            </svg>
            生成新的 API 密钥
        </button>
    </div>
    
    <!-- 充值记录 -->
    <div class="recharge-history">
        <h2 class="section-title" data-i18n="recharge-history-title">充值记录</h2>
        <table class="recharge-table">
            <thead>
                <tr>
                    <th data-i18n="recharge-date">日期</th>
                    <th data-i18n="recharge-amount">金额</th>
                    <th data-i18n="recharge-qau">QAU</th>
                    <th data-i18n="recharge-method">充值方式</th>
                    <th data-i18n="recharge-status">状态</th>
                </tr>
            </thead>
            <tbody id="rechargeHistory">
                <!-- 充值记录内容会通过 JavaScript 动态添加 -->
            </tbody>
        </table>
        <div class="pagination" id="rechargePagination">
            <!-- 分页控件会通过 JavaScript 动态添加 -->
        </div>
    </div>
    
    <!-- 任务列表 -->
    <div class="tasks-section">
        <h2 class="section-title" data-i18n="recent-tasks-title">最近任务</h2>
        <div class="recent-tasks-table-container">
            <table class="recent-tasks-table">
                <thead>
                    <tr>
                        <th data-i18n="task-id-header">任务ID</th>
                        <th data-i18n="task-name-header">任务名称</th>
                        <th data-i18n="task-type-header">任务类型</th>
                        <th data-i18n="task-date-header">创建时间</th>
                        <th data-i18n="task-status-header">状态</th>
                        <th data-i18n="task-qau-header">QAU消耗</th>
                    </tr>
                </thead>
                <tbody id="recentTasksList">
                    <!-- 任务内容会通过 JavaScript 动态添加 -->
                </tbody>
            </table>
        </div>
    </div>
</section>
{% endblock %}

{% block page_js %}
<script>
        // 多语言文本配置
        const translations = {
            zh: {
                'profile-title': '个人中心',
                'profile-subtitle': '管理您的个人账户和 API 访问',
                'recharge-btn': '充值 QAU',
                'qau-balance-label': 'QAU 余额',
                'loading': '加载中...',
                'load-failed': '获取失败',
                'api-key-title': 'API 密钥',
                'copy-btn': '复制',
                'delete-btn': '删除',
                'generate-key-btn': '生成新的 API 密钥',
                'recharge-history-title': '充值记录',
                'recharge-date': '日期',
                'recharge-amount': '金额',
                'recharge-qau': 'QAU',
                'recharge-method': '充值方式',
                'recharge-status': '状态',
                'recent-tasks-title': '最近任务',
                'task-id-header': '任务ID',
                'task-name-header': '任务名称',
                'task-type-header': '任务类型',
                'task-date-header': '创建时间',
                'task-status-header': '状态',
                'task-qau-header': 'QAU消耗',
                'generate-key-title': '生成新的 API 密钥',
                'key-name-label': 'API 密钥名称',
                'key-warning': '注意：生成新的 API 密钥将会使旧的密钥失效！',
                'btn-cancel': '取消',
                'btn-generate': '生成',
                'key-created-title': 'API 密钥已生成',
                'key-created-message': '您的新 API 密钥如下，请妥善保管，此处仅显示一次：',
                'btn-got-it': '我已复制',
                'recharge-title': '支付宝充值 QAU',
                'recharge-message': '请选择充值金额：',
                'original-price': '原价',
                'discount-98': '98折',
                'discount-95': '95折',
                'delete-key-title': '删除 API 密钥',
                'delete-key-message': '确定要删除此 API 密钥吗？删除后将无法恢复。',
                'btn-delete': '删除',
                'key-deleted-message': 'API 密钥已成功删除',
                // Toast通知文本
                'copied-success': '已复制到剪贴板',
                'no-api-key': '没有找到有效的API密钥',
                'create-key-failed': '创建API密钥失败',
                'key-deleted-success': 'API 密钥已成功删除',
                'delete-key-failed': '删除API密钥失败',
                // QAU说明文本
                'qau-info-title': '什么是 QAU？',
                'qau-info-text': 'QAU（Quantum AI Unit）是医图生科QureGenAI与TyxonQ太玄量子的通用计费单元。<br><strong>1 QAU = 0.1 元人民币</strong>，平台中所有计费项目均以QAU单元计费。<br><span style="color: #e74c3c; font-weight: 600;">⚠️ 充值后不可提现</span>',
                // 充值相关提示
                'recharge-warning': '⚠️ 重要提示：充值后不可提现，请谨慎充值。',
                'non-refundable-notice': '充值后不可提现',
                'qau-balance-label': 'QAU 余额',
                // 活动充值文本
                'activity-title': '新用户福利',
                'activity-subtitle': '输入神秘口号，免费领取QAU',
                'activity-hint': '输入正确的活动口号可免费领取100 QAU，每个用户限领一次',
                'claim-activity-btn': '立即领取',
                // 活动充值相关提示信息
                'activity-slogan-incorrect': '活动口号不正确，请重新输入',
                'activity-already-claimed': '您已经领取过此活动奖励',
                'activity-claiming': '领取中...',
                'activity-claim-success': '🎉 恭喜！成功领取100 QAU活动奖励',
                'activity-claim-failed': '领取失败，请稍后重试',
                'activity-claimed-status': '✓ 已成功领取100 QAU活动奖励！',
                'activity-check-status-failed': '检查活动状态失败',
                // 新增：已领取状态文本
                'activity-claimed-text': '您已经领取过QAU',
                'activity-claimed-hint': '每个用户仅可领取一次活动奖励',
                // 计费规则相关文本
                'billing-rules-title': 'QureGenAI平台计费规则',
                'config-title': 'T0机型配置',
                'config-gpu': 'GPU：NVIDIA Tesla（16GB显存）',
                'config-cpu': 'CPU：8核',
                'config-memory': '内存：32GB',
                'config-more': '（更多机型即将开放）',
                'pricing-title': '计费标准',
                'pricing-ai-label': 'AI任务：',
                'pricing-ai-value': '30 QAU/分钟',
                'pricing-ai-desc': '按实际使用分钟数计费',
                'pricing-quantum-label': '量子计算：',
                'pricing-quantum-value': '15 QAU/量子比特秒',
                'pricing-quantum-desc': '量子比特数×使用时间',
                'rules-title': '计费规则',
                'rules-minimum': '按实际运行秒计算',
                'rules-balance': '执行前需确保账户余额充足',
                'rules-records': '使用记录可在控制台查询',
                'rules-version': '版本：ver0.1.1',
                'rules-date': '生效时间：2025年7月20日',
            },
            en: {
                'profile-title': 'User Profile',
                'profile-subtitle': 'Manage your personal account and API access',
                'recharge-btn': 'Recharge QAU',
                'qau-balance-label': 'QAU Balance',
                'loading': 'Loading...',
                'load-failed': 'Load Failed',
                'api-key-title': 'API Key',
                'copy-btn': 'Copy',
                'delete-btn': 'Delete',
                'generate-key-btn': 'Generate New API Key',
                'recharge-history-title': 'Recharge History',
                'recharge-date': 'Date',
                'recharge-amount': 'Amount',
                'recharge-qau': 'QAU',
                'recharge-method': 'Payment Method',
                'recharge-status': 'Status',
                'recent-tasks-title': 'Recent Tasks',
                'task-id-header': 'Task ID',
                'task-name-header': 'Task Name',
                'task-type-header': 'Task Type',
                'task-date-header': 'Created Time',
                'task-status-header': 'Status',
                'task-qau-header': 'QAU Cost',
                'generate-key-title': 'Generate New API Key',
                'key-name-label': 'API Key Name',
                'key-warning': 'Note: Generating a new API key will invalidate your old key!',
                'btn-cancel': 'Cancel',
                'btn-generate': 'Generate',
                'key-created-title': 'API Key Generated',
                'key-created-message': 'Your new API key is shown below. Please save it as it will only be displayed once:',
                'btn-got-it': 'I\'ve copied it',
                'recharge-title': 'Alipay Recharge QAU',
                'recharge-message': 'Please select an amount:',
                'original-price': 'Original Price',
                'discount-98': '2% OFF',
                'discount-95': '5% OFF',
                'delete-key-title': 'Delete API Key',
                'delete-key-message': 'Are you sure you want to delete this API key? This action cannot be undone.',
                'btn-delete': 'Delete',
                'key-deleted-message': 'API key has been successfully deleted',
                // Toast notification text
                'copied-success': 'Copied to clipboard',
                'no-api-key': 'No active API key found',
                'create-key-failed': 'Failed to create API key',
                'key-deleted-success': 'API key has been successfully deleted',
                'delete-key-failed': 'Failed to delete API key',
                // QAU description text
                'qau-info-title': 'What is QAU?',
                'qau-info-text': 'QAU (Quantum AI Unit) is the universal billing unit for QureGenAI and TyxonQ.<br><strong>1 QAU = 0.1 RMB</strong>, all billing items on the platform are charged in QAU units.<br><span style="color: #e74c3c; font-weight: 600;">⚠️ Non-refundable after recharge</span>',
                // Recharge related warnings
                'recharge-warning': '⚠️ Important Notice: Funds are non-refundable after recharge. Please recharge carefully.',
                'non-refundable-notice': 'Non-refundable after recharge',
                'qau-balance-label': 'QAU Balance',
                // activity recharge text
                'activity-title': 'New User Bonus',
                'activity-subtitle': 'Enter the secret slogan to claim free QAU',
                'activity-hint': 'Enter the correct slogan to claim 100 QAU, one per user',
                'claim-activity-btn': 'Claim Now',

                // Activity recharge related messages
                'activity-slogan-incorrect': 'Activity slogan is incorrect, please try again',
                'activity-already-claimed': 'You have already claimed this activity reward',
                'activity-claiming': 'Claiming...',
                'activity-claim-success': '🎉 Congratulations! Successfully claimed 100 QAU activity reward',
                'activity-claim-failed': 'Claim failed, please try again later',
                'activity-claimed-status': '✓ Successfully claimed 100 QAU activity reward!',
                'activity-check-status-failed': 'Failed to check activity status',

                // 新增：已领取状态文本
                'activity-claimed-text': 'You have already claimed QAU',
                'activity-claimed-hint': 'Each user can only claim the activity reward once',

                // 计费规则相关文本
                'billing-rules-title': 'QureGenAI Platform Billing Rules',
                'config-title': 'T0 Configuration',
                'config-gpu': 'GPU: NVIDIA Tesla (16GB VRAM)',
                'config-cpu': 'CPU: 8 cores',
                'config-memory': 'Memory: 32GB',
                'config-more': '(More configurations coming soon)',
                'pricing-title': 'Billing Standards',
                'pricing-ai-label': 'AI Tasks: ',
                'pricing-ai-value': '30 QAU/minute',
                'pricing-ai-desc': 'charged by actual usage minutes',
                'pricing-quantum-label': 'Quantum Computing: ',
                'pricing-quantum-value': '15 QAU/qubit-second',
                'pricing-quantum-desc': 'qubits × usage time',
                'rules-title': 'Billing Rules',
                'rules-minimum': 'Charged by actual runtime seconds',
                'rules-balance': 'Ensure sufficient account balance before execution',
                'rules-records': 'Usage records can be queried in the console',
                'rules-version': 'Version: ver0.1.1',
                'rules-date': 'Effective Date: July 20, 2025',
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // Toast通知系统
        function showToast(message, type = 'info', duration = 3000) {
            // 创建Toast容器，如果不存在
            let container = document.querySelector('.toast-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'toast-container';
                document.body.appendChild(container);
            }
            
            // 创建Toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            // 创建Toast内容
            toast.innerHTML = `
                <div class="toast-message">${message}</div>
                <button class="toast-close">&times;</button>
            `;
            
            // 添加Toast到容器
            container.appendChild(toast);
            
            // 绑定关闭按钮事件
            const closeBtn = toast.querySelector('.toast-close');
            closeBtn.addEventListener('click', () => {
                closeToast(toast);
            });
            
            // 设置自动关闭定时器
            setTimeout(() => {
                closeToast(toast);
            }, duration);
            
            return toast;
        }
        
        // 关闭Toast动画
        function closeToast(toast) {
            toast.classList.add('toast-out');
            toast.addEventListener('animationend', () => {
                toast.remove();
            });
        }

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });

            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';

            // 更新文档标题
            document.title = lang === 'zh' ? '分子设计平台 - 个人中心' : 'Molecular Design Platform - User Profile';

            // 更新页面文本
            updatePageText(lang);

            // 更新登录时间显示
            const loginTime = localStorage.getItem('loginTime');
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];

            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t[key]) {
                    // 对于包含HTML标签的文本（如QAU说明），使用innerHTML
                    if (key === 'qau-info-text') {
                        element.innerHTML = t[key];
                    } else {
                        element.textContent = t[key];
                    }
                }
            });
        }

        // 通用API请求函数，处理认证失败
        function apiRequest(url, options = {}) {
            // 确保包含credentials
            options.credentials = 'include';
            
            return fetch(url, options)
                .then(response => {
                    if (response.status === 401) {
                        // 认证失败，清除本地存储并跳转
                        clearLocalStorage();
                        window.location.href = 'login';
                        throw new Error('认证失败，请重新登录');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.redirect === '/login') {
                        // 服务器要求重新登录
                        clearLocalStorage();
                        window.location.href = 'login';
                        throw new Error(data.message || '需要重新登录');
                    }
                    return data;
                });
        }

        // 检查本地登录状态并获取用户信息
        function checkLocalLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            
            // 如果本地没有登录状态，跳转到登录页
            if (isLoggedIn !== 'true') {
                window.location.href = 'login';
                return;
            }

            // 显示本地存储的用户信息
            displayUserInfoFromLocal();
            
            // 加载个人中心数据
            loadProfileData();

            checkActivityStatus();
        }

        // 从本地存储显示用户信息
        function displayUserInfoFromLocal() {
            const username = localStorage.getItem('username') || '未知用户';
            const loginTime = localStorage.getItem('loginTime');
            
            // 头部显示
            document.getElementById('username').textContent = username;
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
            
            // 个人中心显示
            document.getElementById('profileUsername').textContent = username;
            document.getElementById('largeAvatar').textContent = username.charAt(0).toUpperCase();
            // document.getElementById('profileEmail').textContent = localStorage.getItem('email') || '<EMAIL>';
        }

        // 加载个人中心数据
        function loadProfileData() {
            // 加载QAU余额
            loadUserBalance();
        }

        // 加载用户QAU余额
        function loadUserBalance() {
            // 显示加载状态
            const balanceElement = document.getElementById('qauBalance');
            const originalText = balanceElement.textContent;
            const t = translations[currentLanguage];
            balanceElement.textContent = t['loading'];

            // 调用余额API
            apiRequest('/api/web/balance', {
                method: 'GET',
                credentials: 'include'
            })
            .then(data => {
                if (data.success && data.data) {
                    // 成功获取余额，更新显示
                    const balance = data.data.balance || 0;
                    balanceElement.textContent = balance.toFixed(2);

                    // 可选：在控制台显示详细信息用于调试
                    console.log('用户余额信息:', {
                        balance: data.data.balance,
                        total_recharge: data.data.total_recharge,
                        total_cost: data.data.total_cost,
                        user_id: data.data.user_id
                    });
                } else {
                    // API返回错误
                    console.error('获取余额失败:', data.message);
                    balanceElement.textContent = t['load-failed'];

                    // 显示错误提示
                    const errorMsg = data.message || '获取余额失败';
                    showToast(errorMsg, 'error');
                }
            })
            .catch(error => {
                // 网络错误或其他异常
                console.error('获取余额时发生错误:', error);
                balanceElement.textContent = originalText; // 恢复原始值

                // 显示错误提示
                const errorMsg = currentLanguage === 'zh' ?
                    '网络错误，无法获取余额' :
                    'Network error, unable to get balance';
                showToast(errorMsg, 'error');
            });
        }

        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('userId');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('lastAuthCheck');
            localStorage.removeItem('email');
        }

        // 退出登录
        function logout() {
            // 直接使用fetch，避免apiRequest的认证检查逻辑
            fetch('/api/logout', { 
                method: 'POST',
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('退出登录成功');
                })
                .catch(error => {
                    console.error('退出登录时发生错误:', error);
                })
                .finally(() => {
                    // 总是清除客户端存储并跳转
                    clearLocalStorage();
                    window.location.href = 'login';
                });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkLocalLoginStatus();
            
            // 初始化语言
            switchLanguage(currentLanguage);

            // 初始化充值按钮
            document.getElementById('rechargeBtn').addEventListener('click', function() {
                showRechargeModal();
            });

            // 初始化生成API密钥按钮
            document.getElementById('generateKeyBtn').addEventListener('click', function() {
                showGenerateKeyModal();
            });

            // 加载充值记录
            loadRechargeHistory(1);

            // 加载最近任务列表
            loadRecentTasks();
            
            // 加载API密钥
            loadApiKey();
            
            // 检查URL参数，处理支付宝回调
            checkPaymentCallback();

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });
        });
        
        // 检查支付宝回调
        function checkPaymentCallback() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const orderId = urlParams.get('order_id');
            const paymentStatus = urlParams.get('payment_status');
            
            // 如果URL中有订单ID和支付状态参数，说明是支付回调
            if (orderId && paymentStatus) {
                // 清除URL参数，避免刷新页面重复处理
                window.history.replaceState({}, document.title, window.location.pathname);
                
                if (paymentStatus === 'success') {
                    // 查询订单详情
                    checkPaymentResult(orderId)
                        .then(data => {
                            if (data.paid) {
                                // 显示支付成功提示
                                showPaymentSuccess(data.recharge_credits);
                                // 刷新QAU余额
                                loadUserBalance();
                                // 刷新充值记录
                                loadRechargeHistory(1);
                            } else {
                                showToast(currentLanguage === 'zh' ? '支付未完成' : 'Payment not completed', 'warning');
                            }
                        })
                        .catch(error => {
                            console.error('查询订单失败:', error);
                            showToast(currentLanguage === 'zh' ? `查询订单失败: ${error.message}` : `Failed to query order: ${error.message}`, 'error');
                        });
                } else {
                    // 支付失败或取消
                    showToast(currentLanguage === 'zh' ? '支付未完成或已取消' : 'Payment incomplete or cancelled', 'warning');
                }
            }
        }

        // API密钥管理功能
        function copyApiKey() {
            const apiKeyElement = document.getElementById('apiKeyValue');
            const apiKey = apiKeyElement.textContent;
            const t = translations[currentLanguage];
            
            // 如果apiKey是隐藏的点，就先获取真实的API Key
            if (apiKey === '••••••••••••••••••••••••••••••') {
                getActiveApiKey().then(keyData => {
                    if (keyData && keyData.api_key) {
                        copyTextToClipboard(keyData.api_key, t['copied-success']);
                    } else {
                        showToast(t['no-api-key'], 'error');
                    }
                });
            } else {
                copyTextToClipboard(apiKey, t['copied-success']);
            }
        }

        // 添加兼容性更好的复制函数
        function copyTextToClipboard(text, successMessage) {
            // 尝试使用现代Clipboard API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text)
                    .then(() => {
                        showToast(successMessage, 'success');
                    })
                    .catch(err => {
                        console.error('无法使用Clipboard API复制:', err);
                        fallbackCopyTextToClipboard(text, successMessage);
                    });
            } else {
                // 回退到传统方法
                fallbackCopyTextToClipboard(text, successMessage);
            }
        }

        // 传统的复制方法
        function fallbackCopyTextToClipboard(text, successMessage) {
            try {
                // 创建一个临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = text;
                
                // 设置样式使其不可见
                textArea.style.position = 'fixed';
                textArea.style.top = '0';
                textArea.style.left = '0';
                textArea.style.width = '2em';
                textArea.style.height = '2em';
                textArea.style.padding = '0';
                textArea.style.border = 'none';
                textArea.style.outline = 'none';
                textArea.style.boxShadow = 'none';
                textArea.style.background = 'transparent';
                
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                // 尝试执行复制命令
                const successful = document.execCommand('copy');
                if (successful) {
                    showToast(successMessage, 'success');
                } else {
                    showToast('复制失败，请手动复制', 'error');
                }
                
                // 清理
                document.body.removeChild(textArea);
            } catch (err) {
                console.error('复制失败:', err);
                showToast('复制失败，请手动复制', 'error');
            }
        }

        function showGenerateKeyModal() {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.id = 'generateKeyModal';
            modal.style.display = 'flex';
            
            const content = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" data-i18n="generate-key-title">生成新的 API 密钥</h3>
                        <button class="close-modal" onclick="closeModal('generateKeyModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="keyName" class="form-label" data-i18n="key-name-label">API 密钥名称</label>
                            <input type="text" id="keyName" class="form-input" placeholder="${currentLanguage === 'zh' ? '例如：生产环境' : 'e.g., Production'}" />
                        </div>
                        <p class="warning-text" data-i18n="key-warning">注意：生成新的 API 密钥将会使旧的密钥失效！</p>
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn btn-cancel" onclick="closeModal('generateKeyModal')" data-i18n="btn-cancel">取消</button>
                        <button class="modal-btn btn-confirm" onclick="generateNewApiKey()" data-i18n="btn-generate">生成</button>
                    </div>
                </div>
            `;
            
            modal.innerHTML = content;
            document.body.appendChild(modal);
            
            // 更新多语言文本
            updatePageText(currentLanguage);
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                setTimeout(() => {
                    modal.remove();
                    // 如果是API密钥创建弹窗，关闭后刷新页面
                    if (modalId === 'apiKeyCreatedModal') {
                        window.location.reload();
                    }
                }, 300);
            }
        }

        function generateNewApiKey() {
            const keyName = document.getElementById('keyName').value || '默认 API 密钥';
            const t = translations[currentLanguage];
            
            // 发送请求创建新密钥
            fetch('/api/web/key/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ name: keyName })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 处理响应
                closeModal('generateKeyModal');
                
                if (data.success && data.api_key) {
                    loadApiKey(); // 重新加载API密钥信息
                    // 提醒用户复制API密钥
                    showApiKeyCreatedModal(data.api_key.api_key);
                } else {
                    throw new Error(data.message || t['create-key-failed']);
                }
            })
            .catch(error => {
                console.error('生成API密钥失败:', error);
                showToast(`${t['create-key-failed']}: ${error.message}`, 'error');
            });
        }

        function updateApiKeyDisplay(apiKeyData) {
            const nameElem = document.getElementById('apiKeyName');
            const dateElem = document.getElementById('apiKeyDate');
            
            nameElem.textContent = apiKeyData.name || '默认 API 密钥';
            
            const createdDate = new Date(apiKeyData.create_time);
            dateElem.textContent = currentLanguage === 'zh' 
                ? `创建于 ${createdDate.toLocaleDateString()}` 
                : `Created on ${createdDate.toLocaleDateString()}`;
            
            // API密钥仍然隐藏，直到用户在弹窗中复制它
        }

        function showApiKeyCreatedModal(apiKey) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.id = 'apiKeyCreatedModal';
            modal.style.display = 'flex';
            
            const content = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" data-i18n="key-created-title">API 密钥已生成</h3>
                        <button class="close-modal" onclick="closeModal('apiKeyCreatedModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p data-i18n="key-created-message">您的新 API 密钥如下，请妥善保管，此处仅显示一次：</p>
                        <div class="api-key-value" style="margin-top: 1rem; margin-bottom: 1rem;">
                            <span>${apiKey}</span>
                            <button class="copy-btn" onclick="copyCreatedApiKey('${apiKey}')" data-i18n="copy-btn">复制</button>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn btn-confirm" onclick="closeModal('apiKeyCreatedModal')" data-i18n="btn-got-it">我已复制</button>
                    </div>
                </div>
            `;
            
            modal.innerHTML = content;
            document.body.appendChild(modal);
            
            // 更新多语言文本
            updatePageText(currentLanguage);
        }

        function copyCreatedApiKey(apiKey) {
            const t = translations[currentLanguage];
            copyTextToClipboard(apiKey, t['copied-success']);
            // 复制API密钥后刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
        
        // 加载API密钥
        function loadApiKey() {
            // 从服务器获取活跃的API密钥
            getActiveApiKey().then(apiKey => {
                // 这个函数在下面定义
                displayApiKey(apiKey);
            });
        }
        
        // 获取活跃API密钥
        function getActiveApiKey() {
            return fetch('/api/web/key/active', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    if (response.status === 401) {
                        // 如果未登录，重定向到登录页面
                        window.location.href = 'login';
                        throw new Error('未登录');
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.api_key) {
                    return data.api_key;
                } else {
                    console.log('未找到有效的API密钥');
                    return null;
                }
            })
            .catch(error => {
                console.error('获取API密钥失败:', error);
                return null;
            });
        }
        
        // 显示API密钥
        function displayApiKey(apiKeyData) {
            const keyNameElem = document.getElementById('apiKeyName');
            const keyDateElem = document.getElementById('apiKeyDate');
            const keyValueElem = document.getElementById('apiKeyValue');
            const keyContainer = document.getElementById('apiKeyContainer');
            
            if (apiKeyData && apiKeyData.api_key) {
                // 有有效的API密钥
                keyNameElem.textContent = apiKeyData.name || '默认 API 密钥';
                
                const createdDate = new Date(apiKeyData.create_time);
                keyDateElem.textContent = currentLanguage === 'zh' 
                    ? `创建于 ${createdDate.toLocaleDateString()}` 
                    : `Created on ${createdDate.toLocaleDateString()}`;
                
                // 隐藏真实的API密钥，仅显示掩码
                keyValueElem.textContent = '••••••••••••••••••••••••••••••';
                
                // 存储实际的API密钥用于复制
                keyValueElem.dataset.apiKey = apiKeyData.api_key;
            } else {
                // 没有API密钥
                keyContainer.innerHTML = `
                    <div class="api-key-header">
                        <div class="api-key-name">${currentLanguage === 'zh' ? '暂无API密钥' : 'No API Key Available'}</div>
                    </div>
                    <div class="api-key-value">
                        <span>${currentLanguage === 'zh' ? '请点击下方按钮创建新的API密钥' : 'Please click the button below to create a new API key'}</span>
                    </div>
                `;
            }
        }

        // 充值功能
        function showRechargeModal() {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.id = 'rechargeModal';
            modal.style.display = 'flex';
            
            // 原始充值模态框内容 - 保留以后使用
            /*
            const content = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" data-i18n="recharge-title">充值 QAU</h3>
                        <button class="close-modal" onclick="closeModal('rechargeModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p data-i18n="recharge-message">请选择充值金额：</p>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem; color: #856404;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; margin-bottom: 0.5rem;">
                                <span data-i18n="recharge-warning">⚠️ 重要提示：充值后不可提现，请谨慎充值。</span>
                            </div>
                        </div>
                        <div class="payment-options">
                            <div class="payment-option" onclick="proceedToPayment(10)">
                                <span class="payment-amount">¥10</span>
                                <span class="payment-qau">100 QAU</span>
                            </div>
                            <div class="payment-option" onclick="proceedToPayment(49)">
                                <div class="payment-info">
                                    <span class="payment-amount">¥49</span>
                                    <span class="payment-qau">500 QAU</span>
                                </div>
                                <div class="payment-discount">
                                    <span class="original-price"><span data-i18n="original-price">原价</span> ¥50</span>
                                    <span class="discount-badge" data-i18n="discount-98">98折</span>
                                </div>
                            </div>
                            <div class="payment-option" onclick="proceedToPayment(95)">
                                <div class="payment-info">
                                    <span class="payment-amount">¥95</span>
                                    <span class="payment-qau">1000 QAU</span>
                                </div>
                                <div class="payment-discount">
                                    <span class="original-price"><span data-i18n="original-price">原价</span> ¥100</span>
                                    <span class="discount-badge" data-i18n="discount-95">95折</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn btn-cancel" onclick="closeModal('rechargeModal')" data-i18n="btn-cancel">取消</button>
                    </div>
                </div>
            `;
            */

            // 修改后的充值模态框内容 - 支付升级期间使用
            const content = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" data-i18n="recharge-title">充值 QAU</h3>
                        <button class="close-modal" onclick="closeModal('rechargeModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem; color: #721c24;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; margin-bottom: 0.5rem;">
                                <span>⚠️ 重要通知：因最近支付系统升级，暂时停止支付宝付款，请扫码添加客服企业微信充值或邮件联系客服。</span>
                            </div>
                        </div>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem; color: #856404;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; margin-bottom: 0.5rem;">
                                <span data-i18n="recharge-warning">⚠️ 重要提示：充值后不可提现，请谨慎充值。</span>
                            </div>
                        </div>
                        <div class="payment-options">
                            <!-- 暂时停用的充值选项 -->
                            <div class="payment-option" style="text-decoration: line-through; opacity: 0.5; cursor: not-allowed;">
                                <span class="payment-amount">¥10</span>
                                <span class="payment-qau">100 QAU</span>
                            </div>
                            <div class="payment-option" style="text-decoration: line-through; opacity: 0.5; cursor: not-allowed;">
                                <div class="payment-info">
                                    <span class="payment-amount">¥49</span>
                                    <span class="payment-qau">500 QAU</span>
                                </div>
                                <div class="payment-discount">
                                    <span class="original-price"><span data-i18n="original-price">原价</span> ¥50</span>
                                    <span class="discount-badge" data-i18n="discount-98">98折</span>
                                </div>
                            </div>
                            <div class="payment-option" style="text-decoration: line-through; opacity: 0.5; cursor: not-allowed;">
                                <div class="payment-info">
                                    <span class="payment-amount">¥95</span>
                                    <span class="payment-qau">1000 QAU</span>
                                </div>
                                <div class="payment-discount">
                                    <span class="original-price"><span data-i18n="original-price">原价</span> ¥100</span>
                                    <span class="discount-badge" data-i18n="discount-95">95折</span>
                                </div>
                            </div>
                        </div>

                        <!-- 客服联系方式 -->
                        <div style="text-align: center; margin-top: 2rem;">
                            <h4 style="margin-bottom: 1rem; color: #333;">联系客服充值</h4>
                            <div style="margin-bottom: 1rem;">
                                <img src="/src/images/Qr_code.png" alt="企业微信二维码" style="max-width: 200px; height: auto; border: 1px solid #ddd; border-radius: 8px;">
                                <p style="margin-top: 0.5rem; font-size: 14px; color: #666;">扫码添加客服企业微信</p>
                            </div>
                            <div style="margin-top: 1rem;">
                                <p style="font-size: 14px; color: #666;">或发送邮件至：</p>
                                <p style="font-weight: 600; color: #007bff;"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn btn-cancel" onclick="closeModal('rechargeModal')" data-i18n="btn-cancel">取消</button>
                    </div>
                </div>
            `;
            
            modal.innerHTML = content;
            document.body.appendChild(modal);
            
            // 更新多语言文本
            updatePageText(currentLanguage);
        }

        function proceedToPayment(amount) {
            // 关闭充值模态框
            closeModal('rechargeModal');
            
            // 显示加载中提示
            const loadingToast = showToast(currentLanguage === 'zh' ? '正在创建支付订单...' : 'Creating payment order...', 'info');
            
            // 向后端请求创建支付宝订单
            fetch(`/api/web/create-order?amount=${amount}`, {
                method: 'GET',
                credentials: 'include',
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.pay_url) {
                    // 跳转到支付宝支付页面
                    window.location.href = data.pay_url;
                } else {
                    throw new Error(data.message || '创建订单失败');
                }
            })
            .catch(error => {
                console.error('创建支付订单失败:', error);
                showToast(currentLanguage === 'zh' ? `创建订单失败: ${error.message}` : `Failed to create order: ${error.message}`, 'error');
            });
        }
        
        // 支付结果查询函数
        function checkPaymentResult(orderId) {
            return fetch(`/api/web/query-order?order_id=${orderId}`, {
                method: 'GET',
                credentials: 'include',
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    return data;
                } else {
                    throw new Error(data.message || '查询订单失败');
                }
            });
        }
        
        // 显示支付成功提示
        function showPaymentSuccess(qauAmount) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.id = 'paymentSuccessModal';
            modal.style.display = 'flex';
            
            const content = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" style="color: #2e7d32;">${currentLanguage === 'zh' ? '支付成功' : 'Payment Successful'}</h3>
                        <button class="close-modal" onclick="closeModal('paymentSuccessModal')">&times;</button>
                    </div>
                    <div class="modal-body" style="text-align: center;">
                        <div style="font-size: 3rem; color: #2e7d32; margin: 1rem 0;">✓</div>
                        <p>${currentLanguage === 'zh' ? `您已成功充值 ${qauAmount} QAU` : `You have successfully recharged ${qauAmount} QAU`}</p>
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn btn-confirm" onclick="refreshAfterPayment()">${currentLanguage === 'zh' ? '确定' : 'OK'}</button>
                    </div>
                </div>
            `;
            
            modal.innerHTML = content;
            document.body.appendChild(modal);
        }
        
        // 支付后刷新页面数据
        function refreshAfterPayment() {
            closeModal('paymentSuccessModal');
            
            // 刷新充值记录
            loadRechargeHistory(1);
            
            // 显示成功提示
            showToast(currentLanguage === 'zh' ? '充值成功!' : 'Recharge successful!', 'success');
        }

        // 充值记录功能
        function loadRechargeHistory(page) {
            // 显示加载中提示
            const loadingToast = showToast(currentLanguage === 'zh' ? '正在加载充值记录...' : 'Loading recharge history...', 'info');
            
            // 使用真实API获取充值记录
            fetch('/api/web/recharge/', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    if (response.status === 401) {
                        // 未登录，跳转到登录页
                        window.location.href = 'login';
                        throw new Error('未登录');
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 处理API返回的数据
                const startIdx = (page - 1) * 10;
                const endIdx = startIdx + 10;
                const paginatedData = {
                    records: data.slice(startIdx, endIdx),
                    totalPages: Math.ceil(data.length / 10),
                    currentPage: page
                };
                displayRechargeHistory(paginatedData);
            })
            .catch(error => {
                console.error('获取充值记录失败:', error);
                showToast(currentLanguage === 'zh' ? `获取充值记录失败: ${error.message}` : `Failed to load recharge history: ${error.message}`, 'error');
                
                // 显示空记录
                displayRechargeHistory({records: [], totalPages: 1, currentPage: 1});
            });
        }

        function displayRechargeHistory(data) {
            const tableBody = document.getElementById('rechargeHistory');
            tableBody.innerHTML = '';
            
            if (data.records.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `<td colspan="5" style="text-align: center;">${currentLanguage === 'zh' ? '暂无充值记录' : 'No recharge records'}</td>`;
                tableBody.appendChild(row);
                return;
            }
            
            // 添加充值记录行
            data.records.forEach(record => {
                // 格式化日期
                const date = new Date(record.create_time).toLocaleDateString();
                
                // 状态文本映射
                let statusText;
                if (currentLanguage === 'zh') {
                    statusText = record.status === 1 ? '成功' :
                                 record.status === 0 ? '处理中' :
                                 record.status === -1 ? '失败' : '未知';
                } else {
                    statusText = record.status === 1 ? 'Success' :
                                 record.status === 0 ? 'Processing' :
                                 record.status === -1 ? 'Failed' : 'Unknown';
                }

                // 充值方式文本映射
                let paymentMethodText;
                if (currentLanguage === 'zh') {
                    paymentMethodText = record.payment_method === 'alipay' ? '支付宝' :
                                       record.payment_method === 'activity' ? '活动赠送' : '未知';
                } else {
                    paymentMethodText = record.payment_method === 'alipay' ? 'Alipay' :
                                       record.payment_method === 'activity' ? 'Activity Gift' : 'Unknown';
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${date}</td>
                    <td>¥${record.payment_amount}</td>
                    <td>${record.recharge_credits}</td>
                    <td>${paymentMethodText}</td>
                    <td>${statusText}</td>
                `;
                tableBody.appendChild(row);
            });
            
            // 更新分页控件
            updatePagination(data.totalPages, data.currentPage);
        }

        function updatePagination(totalPages, currentPage) {
            const pagination = document.getElementById('rechargePagination');
            pagination.innerHTML = '';
            
            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = `page-btn ${currentPage === 1 ? 'disabled' : ''}`;
            prevBtn.textContent = '«';
            prevBtn.disabled = currentPage === 1;
            prevBtn.addEventListener('click', () => {
                if (currentPage > 1) loadRechargeHistory(currentPage - 1);
            });
            pagination.appendChild(prevBtn);
            
            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => {
                    loadRechargeHistory(i);
                });
                pagination.appendChild(pageBtn);
            }
            
            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = `page-btn ${currentPage === totalPages ? 'disabled' : ''}`;
            nextBtn.textContent = '»';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.addEventListener('click', () => {
                if (currentPage < totalPages) loadRechargeHistory(currentPage + 1);
            });
            pagination.appendChild(nextBtn);
        }

        // 加载最近任务列表
        function loadRecentTasks() {
            // 从API获取最近15个任务，不分类型
            fetch('/api/tasks?page=1&page_size=15', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.tasks) {
                    // 显示任务列表
                    displayRecentTasks(data.tasks);
                } else {
                    // 如果API返回错误或无数据，显示空任务列表
                    displayRecentTasks([]);
                    console.warn('获取最近任务列表失败:', data.message || '未知错误');
                }
            })
            .catch(error => {
                console.error('获取最近任务列表出错:', error);
                displayRecentTasks([]); // 出错时显示空任务列表
            });
        }



        function displayRecentTasks(tasks) {
            const tasksList = document.getElementById('recentTasksList');
            tasksList.innerHTML = '';

            if (tasks.length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `
                    <td colspan="6" style="text-align: center; padding: 2rem; color: #666;">
                        ${currentLanguage === 'zh' ? '暂无任务' : 'No tasks'}
                    </td>
                `;
                tasksList.appendChild(emptyRow);
                return;
            }

            // 添加任务行
            tasks.forEach(task => {
                const taskRow = document.createElement('tr');

                // 获取任务名称（优先使用job_name，没有则使用task_id）
                const taskName = task.job_name || `任务 ${task.task_id}`;

                // 获取日期（使用created_at，格式化为YYYY-MM-DD HH:mm）
                const taskDate = task.created_at ?
                    new Date(task.created_at).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    }) : '';

                // 获取任务类型显示名称
                let taskTypeDisplay;
                if (currentLanguage === 'zh') {
                    taskTypeDisplay = task.task_type === 'autodock' ? 'AutoDock' :
                                     task.task_type === 'diffdock' ? 'DiffDock' :
                                     task.task_type === 'protenix' ? 'Protenix' : task.task_type;
                } else {
                    taskTypeDisplay = task.task_type === 'autodock' ? 'AutoDock' :
                                     task.task_type === 'diffdock' ? 'DiffDock' :
                                     task.task_type === 'protenix' ? 'Protenix' : task.task_type;
                }

                // 获取状态文本
                let statusText;
                if (currentLanguage === 'zh') {
                    statusText = task.status === 'completed' ? '已完成' :
                                task.status === 'pending' ? '等待中' :
                                task.status === 'running' ? '进行中' :
                                task.status === 'failed' ? '失败' : '未知';
                } else {
                    statusText = task.status === 'completed' ? 'Completed' :
                                task.status === 'pending' ? 'Pending' :
                                task.status === 'running' ? 'Running' :
                                task.status === 'failed' ? 'Failed' : 'Unknown';
                }

                // 获取QAU消耗显示
                let qauCostDisplay;
                if (task.status === 'completed' && task.cost_qau !== undefined && task.cost_qau !== null) {
                    qauCostDisplay = `<span class="qau-cost-cell">${task.cost_qau}</span>`;
                } else {
                    qauCostDisplay = `<span class="qau-cost-pending">-</span>`;
                }

                taskRow.innerHTML = `
                    <td><span class="task-id-cell">${task.task_id}</span></td>
                    <td>${taskName}</td>
                    <td><span class="task-type-cell">${taskTypeDisplay}</span></td>
                    <td>${taskDate}</td>
                    <td><span class="task-status-cell status-${task.status}">${statusText}</span></td>
                    <td>${qauCostDisplay}</td>
                `;

                // 任务列表仅展示，不添加点击跳转功能

                tasksList.appendChild(taskRow);
            });
        }

        // 显示删除API密钥确认对话框
        function showDeleteKeyModal() {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.id = 'deleteKeyModal';
            modal.style.display = 'flex';
            
            const content = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" data-i18n="delete-key-title">删除 API 密钥</h3>
                        <button class="close-modal" onclick="closeModal('deleteKeyModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p data-i18n="delete-key-message">确定要删除此 API 密钥吗？删除后将无法恢复。</p>
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn btn-cancel" onclick="closeModal('deleteKeyModal')" data-i18n="btn-cancel">取消</button>
                        <button class="modal-btn btn-confirm" style="background: #e74c3c;" onclick="deleteApiKey()" data-i18n="btn-delete">删除</button>
                    </div>
                </div>
            `;
            
            modal.innerHTML = content;
            document.body.appendChild(modal);
            
            // 更新多语言文本
            updatePageText(currentLanguage);
        }
        
        // 删除API密钥
        function deleteApiKey() {
            // 获取API密钥
            const apiKeyElement = document.getElementById('apiKeyValue');
            const apiKey = apiKeyElement.dataset.apiKey;
            const t = translations[currentLanguage];
            
            if (!apiKey) {
                showToast(t['no-api-key'], 'error');
                closeModal('deleteKeyModal');
                return;
            }
            
            // 发送请求删除API密钥
            fetch('/api/web/key/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ api_key: apiKey })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 处理响应
                closeModal('deleteKeyModal');
                
                if (data.success) {
                    // 显示删除成功提示
                    showToast(t['key-deleted-success'], 'success');
                    // 重新加载API密钥信息
                    loadApiKey();
                } else {
                    throw new Error(data.message || t['delete-key-failed']);
                }
            })
            .catch(error => {
                console.error('删除API密钥失败:', error);
                showToast(`${t['delete-key-failed']}: ${error.message}`, 'error');
                closeModal('deleteKeyModal');
            });
        }


        // 修改checkActivityStatus函数，调用后端的/activity/status接口
        function checkActivityStatus() {
            const t = translations[currentLanguage];
            const activityContainer = document.querySelector('.activity-recharge-container');
    
            fetch('/activity/status', {
                method: 'GET',
                credentials: 'include'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('活动状态查询结果:', data);
                if (!activityContainer) return;
                if (data.success && data.claimed) {
                    // 已领取，显示已领取状态，不隐藏
                    showClaimedActivityState();
                } else {
                    // 未领取，恢复活动区域内容并显示
                    restoreActivityAreaContent();
                    activityContainer.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('checkActivityStatus 查询失败:', error);
                // 失败时恢复活动区域内容并显示
                restoreActivityAreaContent();
                const activityContainer = document.querySelector('.activity-recharge-container');
                if (activityContainer) {
                    activityContainer.style.display = 'block';
                }
            });
        }

        // 新增：显示已领取状态的函数
        function showClaimedActivityState() {
            const activityContainer = document.querySelector('.activity-recharge-container');
            if (!activityContainer) return;
            
            const t = translations[currentLanguage];
            
            // 恢复原样式类名
            activityContainer.className = 'activity-recharge-container';
            activityContainer.style.display = 'block';
            
            // 显示已领取状态
            activityContainer.innerHTML = `
                <div class="activity-header">
                    <div class="activity-icon">✅</div>
                    <div>
                        <div class="activity-title" data-i18n="activity-title">${t['activity-title']}</div>
                        <div class="activity-subtitle" data-i18n="activity-claimed-text">${t['activity-claimed-text']}</div>
                    </div>
                </div>
                
                <div class="activity-input-group">
                    <div class="activity-input-wrapper">
                        <input type="text" 
                            id="activitySlogan" 
                            class="activity-input" 
                            placeholder="${currentLanguage === 'zh' ? '活动已完成' : 'Activity completed'}"
                            disabled
                            style="background-color: #f5f5f5; color: #999;">
                    </div>
                    <button class="activity-btn" disabled style="background: linear-gradient(135deg, #cbd5e0 0%, #a0aec0 100%); cursor: not-allowed; transform: none; box-shadow: none;">
                        <span data-i18n="activity-claimed-text">${t['activity-claimed-text']}</span>
                    </button>
                </div>
                
                <div class="activity-hint" style="background: rgba(34, 139, 34, 0.08); border-left-color: #228B22;">
                    <span data-i18n="activity-claimed-hint">${t['activity-claimed-hint']}</span>
                </div>
            `;
        }


       // 修改活动充值函数，调用后端的/activity接口
       function claimActivityRecharge() {
        const sloganInput = document.getElementById('activitySlogan');
        const activityBtn = document.getElementById('activityBtn');
        
        // 如果按钮是禁用状态，直接返回
        if (activityBtn.disabled) {
            return;
        }
        
        const inputValue = sloganInput.value.trim();
        const t = translations[currentLanguage];
        
        // 验证口号不能为空
        if (!inputValue) {
            showToast(t['activity-slogan-incorrect'], 'error');
            return;
        }
        
        // 验证口号（移除所有空格后比较）
        const normalizedInput = inputValue.replace(/\s+/g, '');
        const expectedSlogan = '量子未来无限可能';
        
        if (normalizedInput !== expectedSlogan) {
            showToast(t['activity-slogan-incorrect'], 'error');
            return;
        }
        
        // 禁用按钮，防止重复点击
        activityBtn.disabled = true;
        activityBtn.innerHTML = `<span>${t['activity-claiming']}</span>`;
        
        // 调用后端的活动充值接口
        fetch('/activity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                slogan: inputValue
            })
        })
        .then(response => {
            if (!response.ok) {
                // 根据HTTP状态码处理不同错误
                if (response.status === 409) {
                    throw new Error(t['activity-already-claimed']);
                } else if (response.status === 400) {
                    throw new Error(t['activity-slogan-incorrect']);
                } else if (response.status === 401) {
                    throw new Error('请先登录后再参与活动');
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('活动充值结果:', data);
            
            if (data.success) {
                // 领取成功
                showToast(t['activity-claim-success'], 'success', 5000);
                
                // 清空输入框
                sloganInput.value = '';
                
                // 刷新余额
                loadProfileData();
                
                // 刷新充值记录
                if (typeof loadRechargeHistory === 'function') {
                    loadRechargeHistory(1);
                }
                
                // 显示已领取状态（不是成功状态）
                showClaimedActivityState();
            } else {
                throw new Error(data.message || t['activity-claim-failed']);
            }
        })
        .catch(error => {
            console.error('活动充值失败:', error);
            showToast(error.message || t['activity-claim-failed'], 'error');
            
            // 如果是已经领取过的错误，显示已领取状态
            if (error.message === t['activity-already-claimed']) {
                showClaimedActivityState();
            } else {
                // 恢复按钮状态（仅当不是已领取错误时）
                activityBtn.disabled = false;
                activityBtn.innerHTML = `<span data-i18n="claim-activity-btn">${t['claim-activity-btn']}</span>`;
            }
        });
    }


        // 初始化活动区域
        function initActivityArea() {
            const activityContainer = document.querySelector('.activity-recharge-container');
            if (activityContainer) {
                // 添加加载状态，显示正在检查
                activityContainer.style.display = 'block';
                activityContainer.innerHTML = `
                    <div style="padding: 1rem; text-align: center; color: #666;">
                        <div style="font-size: 1rem;">正在检查活动状态...</div>
                    </div>
                `;
            }
        }

        // 恢复活动区域的原始内容
        function restoreActivityAreaContent() {
            const activityContainer = document.querySelector('.activity-recharge-container');
            if (!activityContainer) return;
            
            const t = translations[currentLanguage];
            
            // 恢复活动区域的完整HTML内容
            activityContainer.innerHTML = `
                <div class="activity-header">
                    <div class="activity-icon">🎁</div>
                    <div class="activity-text">
                        <h3 data-i18n="activity-title">${t['activity-title']}</h3>
                        <p data-i18n="activity-subtitle">${t['activity-subtitle']}</p>
                    </div>
                </div>
                <div class="activity-hint">
                    <span data-i18n="activity-hint">${t['activity-hint']}</span>
                </div>
                <div class="activity-input-wrapper">
                    <input type="text" id="activitySlogan" class="activity-input" placeholder="${t['activity-slogan-placeholder'] || '输入活动口号'}" maxlength="20">
                    <button id="activityBtn" class="activity-btn" onclick="claimActivityRecharge()">
                        <span data-i18n="claim-activity-btn">${t['claim-activity-btn']}</span>
                    </button>
                </div>
            `;
        }


    </script>
{% endblock %}
