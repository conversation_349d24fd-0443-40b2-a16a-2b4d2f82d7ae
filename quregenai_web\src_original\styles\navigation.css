/**
 * Navigation Component Styles
 * 导航栏组件样式
 * 
 * 包含: 顶部导航栏、侧边导航栏、语言切换、用户信息
 * 依赖: base.css
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

/* ===== CSS变量定义 ===== */
:root {
    --nav-height: 60px;
    --sidebar-width: 280px;
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --bg-color: #f5f7fa;
    --white: #ffffff;
    --text-color: #333;
    --text-muted: #666;
    --border-color: #e1e5e9;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-sidebar: 2px 0 10px rgba(0, 0, 0, 0.05);
    --transition: all 0.2s ease;
}

/* ===== 顶部导航栏 ===== */
.header {
    background: var(--white);
    height: var(--nav-height);
    box-shadow: var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

/* Logo样式 */
.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* ===== 用户信息区域 ===== */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 用户头像 */
.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
}

/* 用户详细信息 */
.user-details {
    display: flex;
    flex-direction: column;
}

.username {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.login-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 退出登录按钮 */
.logout-btn {
    background: #e74c3c;
    color: var(--white);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s ease;
}

.logout-btn:hover {
    background: #c0392b;
}

/* ===== 语言切换按钮 ===== */
.language-switcher {
    display: flex;
    gap: 0.5rem;
    margin-right: 1rem;
}

.language-btn {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.language-btn:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.language-btn.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

/* ===== 主容器布局 ===== */
.main-container {
    display: flex;
    height: calc(100vh - var(--nav-height));
    margin-top: var(--nav-height);
}

/* ===== 侧边导航栏 ===== */
.sidebar {
    width: var(--sidebar-width);
    background: var(--white);
    box-shadow: var(--shadow-sidebar);
    padding: 2rem 0;
    overflow-y: auto;
    max-height: calc(100vh - var(--nav-height));
}

/* 侧边栏标题 */
.sidebar__title {
    padding: 0 2rem;
    margin-bottom: 2rem;
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 600;
    position: sticky;
    top: 0;
    background: var(--white);
    z-index: 10;
    padding-top: 1rem;
    padding-bottom: 1rem;
}

/* 侧边栏列表 */
.sidebar__list {
    list-style: none;
    padding-bottom: 1rem;
}

.sidebar__item {
    margin-bottom: 0.5rem;
}

/* 侧边栏链接 */
.sidebar__link {
    display: flex;
    align-items: center;
    padding: 1rem 2rem;
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
    border-left: 4px solid transparent;
}

.sidebar__link:hover {
    background-color: #f8f9fa;
    color: var(--text-color);
    border-left-color: #8fa8f3;
}

.sidebar__link--active {
    background-color: #8fa8f3;
    color: var(--white);
    border-left-color: #7b92f0;
}

/* 侧边栏图标 */
.sidebar__icon {
    width: 28px;
    height: 28px;
    margin-right: 1rem;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar__icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 侧边栏内容 */
.sidebar__content {
    flex: 1;
}

.sidebar__name {
    font-weight: 500;
}

.sidebar__description {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

/* 页面内容区域 */
.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* ===== 自定义滚动条 ===== */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: fixed;
        bottom: 0;
        top: var(--nav-height);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 999;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .content {
        padding: 1rem;
    }

    .language-switcher {
        margin-right: 0.5rem;
    }

    .language-btn {
        padding: 0.2rem 0.6rem;
        font-size: 0.7rem;
    }
}

/* ===== 布局特定样式 ===== */
.layout-with-nav {
    height: 100vh;
    overflow: hidden;
}

/* ===== 兼容旧版类名 ===== */
/* 为了向后兼容，保留原有的类名 */
.sidebar-title {
    padding: 0 2rem;
    margin-bottom: 2rem;
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 600;
    position: sticky;
    top: 0;
    background: var(--white);
    z-index: 10;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.module-list {
    list-style: none;
    padding-bottom: 1rem;
}

.module-item {
    margin-bottom: 0.5rem;
}

.module-link {
    display: flex;
    align-items: center;
    padding: 1rem 2rem;
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
    border-left: 4px solid transparent;
}

.module-link:hover {
    background-color: #f8f9fa;
    color: var(--text-color);
    border-left-color: #8fa8f3;
}

.module-link.active {
    background-color: #8fa8f3;
    color: var(--white);
    border-left-color: #7b92f0;
}

.module-icon {
    width: 28px;
    height: 28px;
    margin-right: 1rem;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
}

.module-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.module-name {
    font-weight: 500;
    font-size: 1rem;
}

.module-description {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}
