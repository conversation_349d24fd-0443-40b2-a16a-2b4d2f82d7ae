<!--
    Sidebar Component
    侧边导航栏组件

    功能: 显示功能模块列表，支持激活状态
    参数: current_page - 当前页面标识，用于设置激活状态
    依赖: navigation.css
    使用: {# include 'components/sidebar.html' #}
-->
<nav class="sidebar">
    <!-- 侧边栏标题 -->
    <h2 class="sidebar-title" data-i18n="sidebar-title">功能模块</h2>

    <!-- 功能模块列表 -->
    <ul class="module-list">
        <!-- AutoDock 模块 -->
        <li class="module-item">
            <a href="autodock" class="module-link {{ 'active' if current_page == 'autodock' else '' }}">
                <div class="module-icon">
                    <img src="/src/images/autodock-cube.svg" alt="AutoDock">
                </div>
                <div>
                    <div class="module-name">AutoDock</div>
                    <div class="module-description" data-i18n="autodock-desc">高通量筛选与分子对接</div>
                </div>
            </a>
        </li>
        
        <!-- DiffDock 模块 -->
        <li class="module-item">
            <a href="diffdock" class="module-link {{ 'active' if current_page == 'diffdock' else '' }}">
                <div class="module-icon">
                    <img src="/src/images/diffdock-tetrahedral-methane.svg" alt="DiffDock">
                </div>
                <div>
                    <div class="module-name">DiffDock</div>
                    <div class="module-description" data-i18n="diffdock-desc">口袋预测与分子对接</div>
                </div>
            </a>
        </li>

        <!-- Protenix 模块 -->
        <li class="module-item">
            <a href="protenix" class="module-link {{ 'active' if current_page == 'protenix' else '' }}">
                <div class="module-icon">
                    <img src="/src/images/protenix-protein.svg" alt="Protenix">
                </div>
                <div>
                    <div class="module-name">Protenix</div>
                    <div class="module-description" data-i18n="protenix-desc">蛋白质结构预测</div>
                </div>
            </a>
        </li>

        <!-- MolMap 模块 -->
        <li class="module-item">
            <a href="molmap" class="module-link {{ 'active' if current_page == 'molmap' else '' }}">
                <div class="module-icon">🧪</div>
                <div>
                    <div class="module-name">MolMap</div>
                    <div class="module-description" data-i18n="molmap-desc">ADMET 性质预测</div>
                </div>
            </a>
        </li>

        <!-- PocketVina 模块 -->
        <li class="module-item">
            <a href="pocketvina" class="module-link {{ 'active' if current_page == 'pocketvina' else '' }}">
                <div class="module-icon">🎯</div>
                <div>
                    <div class="module-name">PocketVina</div>
                    <div class="module-description" data-i18n="pocketvina-desc">全自动口袋发现与高通量筛选</div>
                </div>
            </a>
        </li>

        <!-- Raman Spectral 模块 -->
        <li class="module-item">
            <a href="raman" class="module-link {{ 'active' if current_page == 'raman' else '' }}">
                <div class="module-icon">⚡</div>
                <div>
                    <div class="module-name">Raman Spectral</div>
                    <div class="module-description" data-i18n="raman-desc">低成本光谱分析检测</div>
                </div>
            </a>
        </li>
        
        <!-- 量子计算任务 模块 -->
        <li class="module-item">
            <a href="quantum_tasks" class="module-link {{ 'active' if current_page == 'quantum_tasks' else '' }}">
                <div class="module-icon">⚛️</div>
                <div>
                    <div class="module-name" data-i18n="quantum-tasks-name">量子计算任务</div>
                    <div class="module-description" data-i18n="quantum-tasks-desc">量子计算任务管理</div>
                </div>
            </a>
        </li>

        <!-- 用户资料 模块 -->
        <li class="module-item">
            <a href="userprofile" class="module-link {{ 'active' if current_page == 'userprofile' else '' }}">
                <div class="module-icon">👤</div>
                <div>
                    <div class="module-name" data-i18n="userprofile-name">我的（API KEY申请）</div>
                    <div class="module-description" data-i18n="userprofile-desc">账户管理</div>
                </div>
            </a>
        </li>
    </ul>
</nav>
