<template>
  <div class="home-container">
    <section class="welcome-section">
      <h1 class="welcome-title">{{ t('home.welcome-title') }}</h1>
      <p class="welcome-text">{{ t('home.welcome-text') }}</p>
    </section>

    <section class="feature-cards">
      <div class="feature-card autodock" @click="navigateTo('autodock')">
        <h3>AutoDock</h3>
        <p>{{ t('modules.autodock-detail') }}</p>
        <span class="feature-badge">{{ t('modules.classic-algorithm') }}</span>
      </div>

      <div class="feature-card diffdock" @click="navigateTo('diffdock')">
        <h3>DiffDock</h3>
        <p>{{ t('modules.diffdock-detail') }}</p>
        <span class="feature-badge">{{ t('modules.ai-driven') }}</span>
      </div>

      <div class="feature-card protenix" @click="navigateTo('protenix')">
        <h3>Protenix</h3>
        <p>{{ t('modules.protenix-detail') }}</p>
        <span class="feature-badge">{{ t('modules.structure-prediction') }}</span>
      </div>

      <div class="feature-card quantum" @click="navigateTo('quantum_tasks')">
        <h3>{{ t('modules.quantum-computing') }}</h3>
        <p>{{ t('modules.quantum-detail') }}</p>
        <span class="feature-badge">{{ t('modules.quantum-acceleration') }}</span>
      </div>

      <div class="feature-card molmap" @click="navigateTo('molmap')">
        <h3>MolMap</h3>
        <p>{{ t('modules.molmap-detail') }}</p>
        <span class="feature-badge">{{ t('modules.admet-prediction') }}</span>
      </div>

      <div class="feature-card pocketvina" @click="navigateTo('pocketvina')">
        <h3>PocketVina</h3>
        <p>{{ t('modules.pocketvina-detail') }}</p>
        <span class="feature-badge">{{ t('modules.auto-pocket-docking') }}</span>
      </div>

      <div class="feature-card raman" @click="navigateTo('raman')">
        <h3>Raman Spectral</h3>
        <p>{{ t('modules.raman-detail') }}</p>
        <span class="feature-badge">{{ t('modules.spectral-analysis') }}</span>
      </div>
    </section>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { t, currentLanguage } from '../utils/i18n'
import { isAuthenticated } from '../utils/auth'

const router = useRouter()

// 响应式数据
const isLoggedIn = ref(false)

// 生命周期
onMounted(() => {
  checkAuthStatus()
})

// 检查认证状态
const checkAuthStatus = () => {
  if (!isAuthenticated()) {
    router.push('/login')
    return
  }
  isLoggedIn.value = true
}

// 导航到功能页面
const navigateTo = (tool) => {
  if (!isLoggedIn.value) {
    router.push('/login')
    return
  }
  router.push(`/${tool}`)
}
</script>

<style scoped>
/* Home Page Styles */
.home-container {
  /* 移除不必要的样式，让Layout组件处理整体布局 */
}

.welcome-section {
  background: #ffffff;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.welcome-title {
  font-size: 1.875rem;
  color: #333;
  margin-bottom: 1rem;
}

.welcome-text {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
  white-space: pre-line; /* 保持换行格式 */
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: #ffffff;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e1e5e9;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.autodock .feature-badge {
  background: #e8f5e8;
  color: #2e7d32;
}

.diffdock .feature-badge {
  background: #fff3e0;
  color: #f57c00;
}

.protenix .feature-badge {
  background: #fce4ec;
  color: #c2185b;
}

.quantum .feature-badge {
  background: #f3e5f5;
  color: #7b1fa2;
}

.molmap .feature-badge {
  background: #e8f5e8;
  color: #2e7d32;
}

.pocketvina .feature-badge {
  background: #fff9c4;
  color: #f9a825;
}

.raman .feature-badge {
  background: #e1f5fe;
  color: #0277bd;
}

@media (max-width: 768px) {
  .feature-cards {
    grid-template-columns: 1fr;
  }
  
  .welcome-section {
    padding: 1.5rem;
  }
  
  .welcome-title {
    font-size: 1.5rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
}
</style>