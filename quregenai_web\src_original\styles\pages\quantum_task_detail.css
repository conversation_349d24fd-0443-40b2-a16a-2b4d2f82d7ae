/* 页面特定样式 - 量子计算任务详情 */

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}
h1 {
    color: #333;
    margin: 0;
}
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    margin-left: 10px;
}
.btn-primary {
    background-color: #007bff;
    color: white;
}
.btn-primary:hover {
    background-color: #0056b3;
}
.btn-secondary {
    background-color: #6c757d;
    color: white;
}
.btn-secondary:hover {
    background-color: #545b62;
}
.btn-success {
    background-color: #28a745;
    color: white;
}
.btn-success:hover {
    background-color: #218838;
}
.btn-danger {
    background-color: #dc3545;
    color: white;
}
.btn-danger:hover {
    background-color: #c82333;
}
.task-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 30px;
}
.info-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}
.info-card h3 {
    margin-top: 0;
    color: #333;
    font-size: 16px;
}
h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}
.info-item {
    margin-bottom: 15px;
}
.info-label {
    font-weight: 600;
    color: #555;
    display: inline-block;
    width: 100px;
}
.info-value {
    color: #333;
}
.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}
.status-pending { background-color: #fff3cd; color: #856404; }
.status-running { background-color: #d1ecf1; color: #0c5460; }
.status-completed { background-color: #d4edda; color: #155724; }
.status-failed { background-color: #f8d7da; color: #721c24; }
.status-cancelled { background-color: #e2e3e5; color: #383d41; }
.progress-section {
    margin-bottom: 20px;
}
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}
.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 12px;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}
.result-section {
    margin-bottom: 20px;
}
.result-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 15px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    white-space: pre-wrap;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.4;
    max-height: 600px;
    overflow-y: auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}
.error-content {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}
.error-message {
    text-align: center;
    padding: 40px;
    color: #dc3545;
}
.timeline {
    margin-bottom: 20px;
}
.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
}
.timeline-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 15px;
}
.timeline-icon.submitted { background-color: #6c757d; }
.timeline-icon.started { background-color: #007bff; }
.timeline-icon.completed { background-color: #28a745; }
.timeline-icon.failed { background-color: #dc3545; }
.timeline-text {
    flex: 1;
}
.timeline-time {
    color: #666;
    font-size: 14px;
}

/* 量子结果展示样式 */
.quantum-analysis {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 12px;
    margin: 15px 0;
}

/* 量子态进度条样式已注释掉，改为直接列表显示
.quantum-state-bar {
    background: #e9ecef;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    margin: 5px 0;
    position: relative;
}

.quantum-state-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.5s ease;
    border-radius: 10px;
}
*/

.quantum-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
    margin-top: 12px;
}

.quantum-metric {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 8px;
    text-align: center;
}

.quantum-metric-value {
    font-size: 1.5em;
    font-weight: bold;
    color: #fff;
}

.quantum-metric-label {
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5px;
}

@media (max-width: 768px) {
    .task-info {
        grid-template-columns: 1fr;
    }
    .header {
        flex-direction: column;
        align-items: flex-start;
    }
    .header h1 {
        margin-bottom: 10px;
    }
}