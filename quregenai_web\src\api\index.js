import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // 使用Vite环境变量
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  withCredentials: true // 允许跨域请求携带凭证
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage中获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response && error.response.status === 401) {
      // 未授权，可能需要登录
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('mobile')
      localStorage.removeItem('user_id')
      localStorage.removeItem('lastLoginTime')
      // 可以添加重定向到登录页的逻辑
    }
    return Promise.reject(error)
  }
)

export default api 