/**
 * Common Utilities
 * 通用工具函数
 * 
 * 功能: 通用工具函数、辅助方法
 * 依赖: 无
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

(function() {
    'use strict';

    // ===== DOM 工具函数 =====
    const DOM = {
        // 查询单个元素
        query: function(selector, context = document) {
            return context.querySelector(selector);
        },

        // 查询多个元素
        queryAll: function(selector, context = document) {
            return Array.from(context.querySelectorAll(selector));
        },

        // 添加类名
        addClass: function(element, className) {
            if (element && className) {
                element.classList.add(className);
            }
        },

        // 移除类名
        removeClass: function(element, className) {
            if (element && className) {
                element.classList.remove(className);
            }
        },

        // 切换类名
        toggleClass: function(element, className) {
            if (element && className) {
                element.classList.toggle(className);
            }
        },

        // 检查是否有类名
        hasClass: function(element, className) {
            return element && className && element.classList.contains(className);
        },

        // 设置属性
        setAttr: function(element, name, value) {
            if (element && name) {
                element.setAttribute(name, value);
            }
        },

        // 获取属性
        getAttr: function(element, name) {
            return element && name ? element.getAttribute(name) : null;
        },

        // 显示元素
        show: function(element) {
            if (element) {
                element.style.display = '';
                this.removeClass(element, 'u-hidden');
            }
        },

        // 隐藏元素
        hide: function(element) {
            if (element) {
                this.addClass(element, 'u-hidden');
            }
        }
    };

    // ===== 字符串工具函数 =====
    const String = {
        // 首字母大写
        capitalize: function(str) {
            return str ? str.charAt(0).toUpperCase() + str.slice(1) : '';
        },

        // 转换为驼峰命名
        toCamelCase: function(str) {
            return str ? str.replace(/-([a-z])/g, (g) => g[1].toUpperCase()) : '';
        },

        // 转换为短横线命名
        toKebabCase: function(str) {
            return str ? str.replace(/([A-Z])/g, '-$1').toLowerCase() : '';
        },

        // 截断字符串
        truncate: function(str, length = 50, suffix = '...') {
            if (!str || str.length <= length) return str;
            return str.substring(0, length) + suffix;
        },

        // 移除HTML标签
        stripHtml: function(str) {
            return str ? str.replace(/<[^>]*>/g, '') : '';
        }
    };

    // ===== 数组工具函数 =====
    const Array = {
        // 数组去重
        unique: function(arr) {
            return arr ? [...new Set(arr)] : [];
        },

        // 数组分块
        chunk: function(arr, size) {
            if (!arr || !size) return [];
            const chunks = [];
            for (let i = 0; i < arr.length; i += size) {
                chunks.push(arr.slice(i, i + size));
            }
            return chunks;
        },

        // 数组随机排序
        shuffle: function(arr) {
            if (!arr) return [];
            const result = [...arr];
            for (let i = result.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [result[i], result[j]] = [result[j], result[i]];
            }
            return result;
        }
    };

    // ===== 对象工具函数 =====
    const Object = {
        // 深度克隆
        deepClone: function(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepClone(item));
            if (typeof obj === 'object') {
                const cloned = {};
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        cloned[key] = this.deepClone(obj[key]);
                    }
                }
                return cloned;
            }
        },

        // 合并对象
        merge: function(target, ...sources) {
            if (!target) return {};
            sources.forEach(source => {
                if (source) {
                    for (const key in source) {
                        if (source.hasOwnProperty(key)) {
                            target[key] = source[key];
                        }
                    }
                }
            });
            return target;
        }
    };

    // ===== 时间工具函数 =====
    const Time = {
        // 格式化时间
        format: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
            if (!date) return '';
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            const hours = String(d.getHours()).padStart(2, '0');
            const minutes = String(d.getMinutes()).padStart(2, '0');
            const seconds = String(d.getSeconds()).padStart(2, '0');

            return format
                .replace('YYYY', year)
                .replace('MM', month)
                .replace('DD', day)
                .replace('HH', hours)
                .replace('mm', minutes)
                .replace('ss', seconds);
        },

        // 相对时间
        relative: function(date) {
            if (!date) return '';
            const now = new Date();
            const target = new Date(date);
            const diff = now - target;
            const seconds = Math.floor(diff / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (days > 0) return `${days}天前`;
            if (hours > 0) return `${hours}小时前`;
            if (minutes > 0) return `${minutes}分钟前`;
            return '刚刚';
        }
    };

    // ===== URL 工具函数 =====
    const URL = {
        // 获取查询参数
        getParam: function(name) {
            const params = new URLSearchParams(window.location.search);
            return params.get(name);
        },

        // 设置查询参数
        setParam: function(name, value) {
            const url = new URL(window.location);
            url.searchParams.set(name, value);
            window.history.replaceState({}, '', url);
        },

        // 移除查询参数
        removeParam: function(name) {
            const url = new URL(window.location);
            url.searchParams.delete(name);
            window.history.replaceState({}, '', url);
        }
    };

    // ===== 存储工具函数 =====
    const Storage = {
        // 本地存储
        local: {
            set: function(key, value) {
                try {
                    localStorage.setItem(key, JSON.stringify(value));
                } catch (e) {
                    console.error('localStorage set error:', e);
                }
            },

            get: function(key, defaultValue = null) {
                try {
                    const value = localStorage.getItem(key);
                    return value ? JSON.parse(value) : defaultValue;
                } catch (e) {
                    console.error('localStorage get error:', e);
                    return defaultValue;
                }
            },

            remove: function(key) {
                try {
                    localStorage.removeItem(key);
                } catch (e) {
                    console.error('localStorage remove error:', e);
                }
            }
        },

        // 会话存储
        session: {
            set: function(key, value) {
                try {
                    sessionStorage.setItem(key, JSON.stringify(value));
                } catch (e) {
                    console.error('sessionStorage set error:', e);
                }
            },

            get: function(key, defaultValue = null) {
                try {
                    const value = sessionStorage.getItem(key);
                    return value ? JSON.parse(value) : defaultValue;
                } catch (e) {
                    console.error('sessionStorage get error:', e);
                    return defaultValue;
                }
            },

            remove: function(key) {
                try {
                    sessionStorage.removeItem(key);
                } catch (e) {
                    console.error('sessionStorage remove error:', e);
                }
            }
        }
    };

    // ===== 防抖和节流 =====
    const Throttle = {
        // 防抖
        debounce: function(func, wait, immediate = false) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            };
        },

        // 节流
        throttle: function(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    };

    // ===== 全局暴露 =====
    window.Utils = {
        DOM,
        String,
        Array,
        Object,
        Time,
        URL,
        Storage,
        Throttle
    };

})();
