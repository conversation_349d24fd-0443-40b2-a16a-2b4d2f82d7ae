<template>
  <div class="layout" :class="{ 'has-nav': !isAuthPage }">
    <Navbar v-if="!isAuthPage" />
    <div class="main-container" v-if="!isAuthPage">
      <SideBar />
      <main class="content">
        <slot></slot>
      </main>
    </div>
    <div v-else class="auth-content">
      <slot></slot>
    </div>
    <Footer v-if="!isAuthPage" />
  </div>
</template>

<script>
import Navbar from './Navbar.vue'
import Footer from './Footer.vue'
import SideBar from "./SideBar.vue";
import { useRoute } from 'vue-router'
import { computed } from 'vue'

export default {
  name: 'LayoutComponent',
  components: {
    Navbar,
    Footer,
    SideBar
  },
  setup() {
    const route = useRoute()
    
    // 判断当前页面是否为登录、注册或找回密码页面
    const isAuthPage = computed(() => {
      const authPages = ['/login', '/register', '/reset-password', '/terms-of-service']
      return authPages.includes(route.path)
    })
    
    return {
      isAuthPage
    }
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.layout {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: #f5f7fa;
}

.layout.has-nav {
  height: 100vh;
  overflow: hidden;
}

.main-container {
  display: flex;
  height: calc(100vh - 60px);
  margin-top: 60px;
}

.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  /* 移除固定的margin-left，让flex布局自动处理 */
}

.auth-content {
  width: 100%;
  min-height: 100vh;
  /* 移除flex布局，让内容自然流动 */
}

@media (max-width: 768px) {
  .content {
    padding: 1rem;
  }
}
</style>