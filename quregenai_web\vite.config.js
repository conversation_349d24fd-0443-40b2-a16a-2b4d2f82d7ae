import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  base: '', // 添加此行，设置基本路径为/quregenai/
  build: {
    outDir: 'dist' // 打包输出目录
  },
  server: {
    port: 8080,
    cors: true,
    proxy: {
      '/api/login': {
        target: 'http://120.26.248.92',
        // target: 'http://127.0.0.1:8083',
        changeOrigin: true,
        secure: false,
        ws: true
      },
      '/api': {
        target: 'http://120.26.248.92',
        // target: 'http://127.0.0.1:33322',
        changeOrigin: true,
        secure: false,
        ws: true
      }
    }
  }
})