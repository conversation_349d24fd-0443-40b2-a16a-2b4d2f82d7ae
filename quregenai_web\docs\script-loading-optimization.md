# JS库加载优化文档

## 问题描述

在重构过程中发现了以下问题：

1. **重复的JS库加载逻辑**：
   - `AutoDock.vue` 中有加载 jQuery、3Dmol.js、SmilesDrawer 的代码
   - `useMoleculeViewer.js` 中也有加载 3Dmol.js 的代码
   - 两处都定义了 `loadScript` 函数

2. **函数作用域问题**：
   - Vue文件中调用 `loadScript` 但没有定义这个函数
   - 会导致运行时错误

3. **代码重复**：
   - 相同的脚本加载逻辑在多个地方重复
   - 维护困难，容易出现不一致

## 优化方案

### 1. 统一脚本加载逻辑

将所有JS库加载逻辑统一到 `useMoleculeViewer.js` 中：

```javascript
// 新增 loadAllScripts 方法
const loadAllScripts = async () => {
  console.log('开始加载必需的JS库...')
  
  try {
    // 按顺序加载JS库，避免重复加载
    if (typeof window.$ === 'undefined') {
      await loadScript('./src/assets/scripts/jquery.js')
      console.log('jQuery 加载完成')
    }
    
    if (typeof window.$3Dmol === 'undefined') {
      await loadScript('./src/assets/scripts/3dmol.js')
      console.log('3Dmol.js 加载完成')
    }
    
    if (typeof window.SmilesDrawer === 'undefined') {
      await loadScript('./src/assets/scripts/smiles-drawer.min.js')
      console.log('SmilesDrawer 加载完成')
    }
    
    return true
  } catch (error) {
    console.error('加载JS库时出错:', error)
    throw new Error('加载必需的JS库失败: ' + error.message)
  }
}
```

### 2. 更新初始化流程

修改 `initializeViewer` 方法，确保在创建查看器前加载所有必需的库：

```javascript
const initializeViewer = async () => {
  console.log('初始化3Dmol查看器...')
  
  try {
    if (!viewerContainer.value) {
      throw new Error('查看器容器未找到')
    }
    
    // 确保所有必需的库都已加载
    await loadAllScripts()
    
    // 创建3Dmol查看器
    viewer.value = window.$3Dmol.createViewer(viewerContainer.value, {
      defaultcolors: window.$3Dmol.rasmolElementColors,
      backgroundColor: 0x000000
    })
    
    // ... 其余初始化代码
  } catch (error) {
    // ... 错误处理
  }
}
```

### 3. 简化Vue组件

移除Vue组件中重复的脚本加载代码：

```javascript
// 移除前
onMounted(async () => {
  // 按顺序加载JS库
  await loadScript('./src/assets/scripts/jquery.js');
  await loadScript('./src/assets/scripts/3dmol.js');
  await loadScript('./src/assets/scripts/smiles-drawer.min.js');
  
  // 初始化分子查看器
  const viewerInitialized = initializeViewer();
  // ...
});

// 优化后
onMounted(async () => {
  // 初始化分子查看器（包含JS库加载）
  const viewerInitialized = await initializeViewer();
  // ...
});
```

## 优化效果

### 1. 代码简化
- 移除了重复的脚本加载逻辑
- Vue组件代码更简洁
- 单一职责原则：脚本加载逻辑集中在 composable 中

### 2. 错误修复
- 修复了 `loadScript` 函数未定义的问题
- 避免了运行时错误

### 3. 性能优化
- 避免重复加载相同的脚本
- 通过检查全局变量避免不必要的网络请求

### 4. 维护性提升
- 脚本加载逻辑集中管理
- 更容易添加新的脚本或修改加载顺序

## 测试验证

添加了测试工具 `scriptLoadingTest.js` 来验证优化效果：

```javascript
// 在浏览器控制台运行
testScriptLoading()
```

测试内容包括：
- 检查全局变量是否正确加载
- 检查脚本标签是否存在
- 检查是否有重复加载的脚本

## 使用说明

1. **开发环境测试**：
   - 打开浏览器控制台
   - 运行 `testScriptLoading()` 验证脚本加载
   - 检查是否有错误或警告

2. **添加新脚本**：
   - 在 `loadAllScripts` 方法中添加新的脚本加载逻辑
   - 遵循相同的模式：检查全局变量 → 加载脚本 → 记录日志

3. **修改脚本路径**：
   - 只需在 `useMoleculeViewer.js` 中修改路径
   - 所有使用该 composable 的组件都会自动更新

## 注意事项

1. **加载顺序**：某些库可能依赖其他库，需要注意加载顺序
2. **错误处理**：脚本加载失败会抛出异常，需要适当处理
3. **缓存**：浏览器会缓存脚本，开发时可能需要清除缓存测试
4. **异步加载**：所有脚本加载都是异步的，确保在使用前等待加载完成
