/**
 * Home Page Styles
 * 主页页面样式
 * 
 * 包含: 欢迎区域、功能卡片等主页特有样式
 * 依赖: base.css, navigation.css
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

/* ===== 欢迎区域 ===== */
.welcome-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.welcome-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1rem;
}

.welcome-text {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 2rem;
    white-space: pre-line; /* 保持换行格式 */
}

/* ===== 功能卡片网格 ===== */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* ===== 功能卡片样式 ===== */
.feature-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #e1e5e9;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

/* ===== 功能标签 ===== */
.feature-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: #e3f2fd;
    color: #1976d2;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== 不同功能的标签颜色 ===== */
.autodock .feature-badge {
    background: #e8f5e8;
    color: #2e7d32;
}

.diffdock .feature-badge {
    background: #fff3e0;
    color: #f57c00;
}

.protenix .feature-badge {
    background: #fce4ec;
    color: #c2185b;
}

.quantum .feature-badge {
    background: #f3e5f5;
    color: #7b1fa2;
}

.molmap .feature-badge {
    background: #e8f5e8;
    color: #2e7d32;
}

.pocketvina .feature-badge {
    background: #fff9c4;
    color: #f9a825;
}

.raman .feature-badge {
    background: #e1f5fe;
    color: #0277bd;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .welcome-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .welcome-title {
        font-size: 1.5rem;
    }

    .welcome-text {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .feature-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-card h3 {
        font-size: 1.125rem;
    }

    .feature-card p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .welcome-section {
        padding: 1rem;
    }

    .welcome-title {
        font-size: 1.25rem;
    }

    .feature-card {
        padding: 1rem;
    }
}

/* ===== 动画效果 ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.welcome-section {
    animation: fadeInUp 0.6s ease-out;
}

.feature-card {
    animation: fadeInUp 0.6s ease-out;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }
.feature-card:nth-child(7) { animation-delay: 0.7s; }

/* ===== 无障碍支持 ===== */
.feature-card:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .feature-card,
    .welcome-section {
        animation: none;
    }
    
    .feature-card:hover {
        transform: none;
    }
}
