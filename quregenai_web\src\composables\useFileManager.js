// 文件管理 Composable
import { ref, computed } from 'vue'

export function useFileManager(uploadedFiles) {
  // 状态管理
  const selectedProteinFile = ref(null)
  const selectedLigandFiles = ref([])
  const ligandInputMode = ref('file') // 'file' 或 'smiles'
  const smilesInput = ref('')
  const smilesFiles = ref([])

  // 计算属性 - 过滤文件类型
  const filteredProteinFiles = computed(() => {
    return uploadedFiles.value.filter(file =>
      ['pdb', 'pdbqt'].includes(file.type.toLowerCase())
    )
  })

  const filteredLigandFiles = computed(() => {
    return uploadedFiles.value.filter(file =>
      ['sdf', 'mol2', 'pdbqt'].includes(file.type.toLowerCase())
    )
  })
  
  // 选择蛋白质文件
  const selectProteinFile = (fileId) => {
    selectedProteinFile.value = fileId
    console.log('选择蛋白质文件:', fileId)
  }
  
  // 切换配体文件选择
  const toggleLigandFileSelection = (fileId) => {
    const index = selectedLigandFiles.value.indexOf(fileId)
    if (index === -1) {
      selectedLigandFiles.value.push(fileId)
    } else {
      selectedLigandFiles.value.splice(index, 1)
    }
    console.log('配体文件选择状态:', selectedLigandFiles.value)
  }
  
  // 切换配体输入模式
  const switchLigandTab = (mode) => {
    ligandInputMode.value = mode
    console.log('切换配体输入模式:', mode)
  }
  
  // 处理 SMILES 输入
  const processSmilesInput = () => {
    if (!smilesInput.value.trim()) {
      return { success: false, message: '请输入 SMILES 字符串' }
    }
    
    try {
      // 解析 SMILES 输入（支持多行和逗号分隔）
      const smilesLines = smilesInput.value
        .split(/[\n,]/)
        .map(line => line.trim())
        .filter(line => line.length > 0)
      
      if (smilesLines.length === 0) {
        return { success: false, message: '未找到有效的 SMILES 字符串' }
      }
      
      // 创建 SMILES 文件对象
      const newSmilesFiles = smilesLines.map((smiles, index) => ({
        id: `smiles_${Date.now()}_${index}`,
        name: `SMILES_${index + 1}`,
        type: 'smiles',
        content: smiles,
        size: smiles.length,
        uploadTime: new Date().toISOString()
      }))
      
      // 添加到 SMILES 文件列表
      smilesFiles.value.push(...newSmilesFiles)
      
      // 清空输入
      smilesInput.value = ''
      
      return { 
        success: true, 
        message: `成功添加 ${newSmilesFiles.length} 个 SMILES 分子`,
        files: newSmilesFiles
      }
    } catch (error) {
      console.error('处理 SMILES 输入时出错:', error)
      return { success: false, message: '处理 SMILES 输入时出错: ' + error.message }
    }
  }
  
  // 删除 SMILES 文件
  const deleteSmilesFile = (fileId) => {
    const index = smilesFiles.value.findIndex(f => f.id === fileId)
    if (index !== -1) {
      smilesFiles.value.splice(index, 1)
      console.log('删除 SMILES 文件:', fileId)
    }
  }
  
  // 清空所有 SMILES 文件
  const clearAllSmilesFiles = () => {
    smilesFiles.value = []
    console.log('清空所有 SMILES 文件')
  }
  
  // 处理 CSV 文件上传
  const handleCsvUpload = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const csvContent = e.target.result
          const result = parseCsvContent(csvContent)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsText(file)
    })
  }
  
  // 解析 CSV 内容
  const parseCsvContent = (csvContent) => {
    try {
      const lines = csvContent.split('\n').filter(line => line.trim())
      
      if (lines.length === 0) {
        throw new Error('CSV 文件为空')
      }
      
      // 检测分隔符
      const firstLine = lines[0]
      const separators = [',', '\t', ';', '|']
      let separator = ','
      
      for (const sep of separators) {
        if (firstLine.includes(sep)) {
          separator = sep
          break
        }
      }
      
      // 解析数据
      const smilesData = []
      let hasHeader = false
      
      // 检测是否有标题行
      const firstRowCells = lines[0].split(separator)
      if (firstRowCells.some(cell => 
        cell.toLowerCase().includes('smiles') || 
        cell.toLowerCase().includes('name') ||
        cell.toLowerCase().includes('id')
      )) {
        hasHeader = true
      }
      
      const dataLines = hasHeader ? lines.slice(1) : lines
      
      dataLines.forEach((line, index) => {
        const cells = line.split(separator).map(cell => cell.trim().replace(/['"]/g, ''))
        
        if (cells.length > 0 && cells[0]) {
          // 假设第一列是 SMILES，第二列是名称（如果存在）
          const smiles = cells[0]
          const name = cells[1] || `Molecule_${index + 1}`
          
          // 简单验证 SMILES 格式
          if (smiles.length > 0 && /^[A-Za-z0-9\[\]()=#+\-@\/\\\.]+$/.test(smiles)) {
            smilesData.push({
              id: `csv_smiles_${Date.now()}_${index}`,
              name: name,
              type: 'smiles',
              content: smiles,
              size: smiles.length,
              uploadTime: new Date().toISOString(),
              source: 'csv'
            })
          }
        }
      })
      
      if (smilesData.length === 0) {
        throw new Error('未找到有效的 SMILES 数据')
      }
      
      // 添加到 SMILES 文件列表
      smilesFiles.value.push(...smilesData)
      
      return {
        success: true,
        message: `从 CSV 文件中成功导入 ${smilesData.length} 个 SMILES 分子`,
        count: smilesData.length,
        files: smilesData
      }
    } catch (error) {
      console.error('解析 CSV 文件时出错:', error)
      throw new Error('解析 CSV 文件失败: ' + error.message)
    }
  }
  
  // 验证选择的文件
  const validateFileSelection = () => {
    const errors = []
    
    // 检查蛋白质文件
    if (!selectedProteinFile.value) {
      errors.push('请选择一个蛋白质文件（PDB 或 PDBQT 格式）')
    }
    
    // 检查配体文件
    if (ligandInputMode.value === 'file') {
      if (selectedLigandFiles.value.length === 0) {
        errors.push('请至少选择一个配体文件（SDF、MOL2 或 PDBQT 格式）')
      }
    } else if (ligandInputMode.value === 'smiles') {
      if (smilesFiles.value.length === 0) {
        errors.push('请添加至少一个 SMILES 分子')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
  
  // 获取选中的文件信息
  const getSelectedFilesInfo = () => {
    const proteinFile = uploadedFiles.value.find(f => f.id === selectedProteinFile.value)
    const ligandFiles = ligandInputMode.value === 'file' 
      ? uploadedFiles.value.filter(f => selectedLigandFiles.value.includes(f.id))
      : smilesFiles.value
    
    return {
      protein: proteinFile,
      ligands: ligandFiles,
      mode: ligandInputMode.value
    }
  }
  
  // 重置选择
  const resetSelection = () => {
    selectedProteinFile.value = null
    selectedLigandFiles.value = []
    smilesFiles.value = []
    smilesInput.value = ''
    ligandInputMode.value = 'file'
  }
  
  // 获取文件统计信息
  const getFileStats = () => {
    return {
      totalFiles: uploadedFiles.value.length,
      proteinFiles: filteredProteinFiles.value.length,
      ligandFiles: filteredLigandFiles.value.length,
      smilesFiles: smilesFiles.value.length,
      selectedProtein: selectedProteinFile.value ? 1 : 0,
      selectedLigands: ligandInputMode.value === 'file' 
        ? selectedLigandFiles.value.length 
        : smilesFiles.value.length
    }
  }
  
  return {
    // 状态
    selectedProteinFile,
    selectedLigandFiles,
    ligandInputMode,
    smilesInput,
    smilesFiles,
    
    // 计算属性
    filteredProteinFiles,
    filteredLigandFiles,
    
    // 方法
    selectProteinFile,
    toggleLigandFileSelection,
    switchLigandTab,
    processSmilesInput,
    deleteSmilesFile,
    clearAllSmilesFiles,
    handleCsvUpload,
    parseCsvContent,
    validateFileSelection,
    getSelectedFilesInfo,
    resetSelection,
    getFileStats
  }
}
