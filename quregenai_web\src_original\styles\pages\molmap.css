.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page-title {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-subtitle {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.molmap-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.form-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
    font-family: 'Courier New', monospace;
}

.smiles-input-tabs {
    display: flex;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e1e5e9;
}

.tab-button {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    font-weight: 500;
}

.tab-button.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-content {
    display: none;
    margin-top: 1rem;
}

.tab-content.active {
    display: block;
}

.smiles-examples {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border-left: 4px solid #667eea;
}

.smiles-examples h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.example-smiles {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.example-smiles-item {
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e1e5e9;
}

.example-smiles-item:hover {
    background: #667eea;
    color: white;
}

.validation-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    display: none;
}

.validation-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.validation-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.submit-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.validate-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    margin-top: 0.5rem;
    transition: background-color 0.2s ease;
}

.validate-btn:hover {
    background: #5a6268;
}

.results-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    display: none;
}

.results-section.show {
    display: block;
}

.task-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.task-info h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.task-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.task-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e1e5e9;
    gap: 0.5rem; /* 添加标签和值之间的间距控制 */
}

.task-detail-item:last-child {
    border-bottom: none;
}

.help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.5rem;
}

.notification {
    position: fixed;
    top: 80px;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: gray;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #28a745;
}

.notification.error {
    background: #dc3545;
}

.notification.info {
    background: #17a2b8;
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .content {
        padding: 1rem;
    }

    .task-details {
        grid-template-columns: 1fr;
    }
}

/* 结果显示区域样式 */
.results-container {
    margin-top: 2rem;
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    display: none;
}

.results-container.show {
    display: block;
}

.molecule-results {
    display: grid;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.molecule-item {
    background: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.molecule-item:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.molecule-item.loading {
    opacity: 0.6;
    cursor: wait;
}

.molecule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.molecule-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.download-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s ease;
}

.download-btn:hover {
    background: #5a6fd8;
}

.download-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.smiles-display {
    font-family: 'Courier New', monospace;
    background: white;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    color: #666;
    border: 1px solid #e1e5e9;
    word-break: break-all;
}

/* CSV表格样式 */
.csv-table-container {
    margin-top: 1rem;
    max-height: 400px;
    overflow-y: auto;
    border-radius: 0.5rem;
    border: 1px solid #e1e5e9;
    display: none;
}

.csv-table-container.show {
    display: block;
}

.csv-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.csv-table th,
.csv-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.csv-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.csv-table tbody tr:hover {
    background: #f8f9fa;
}

.csv-table td {
    color: #666;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.task-status-indicator {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-status-indicator.completed {
    background: #d4edda;
    color: #155724;
}

.task-status-indicator.running {
    background: #d1ecf1;
    color: #0c5460;
}

.task-status-indicator.failed {
    background: #f8d7da;
    color: #721c24;
}


/* 任务历史区域样式 */
.section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e1e5e9;
}

.section-header h2 {
    color: #333;
    margin: 0;
    font-size: 1.5rem;
}

.btn {
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-refresh-green {
    background: #28a745;
    color: white;
}

.btn-refresh-green:hover {
    background: #218838;
    transform: translateY(-1px);
}

.task-history-container {
    min-height: 300px;
}

.tasks-list {
    display: grid;
    gap: 1rem;
}

.task-item {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.task-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.task-title {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.task-id {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #666;
}

.task-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-status.completed {
    background: #d4edda;
    color: #155724;
}

.task-status.running {
    background: #d1ecf1;
    color: #0c5460;
}

.task-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.task-status.pending {
    background: #fff3cd;
    color: #856404;
}

.task-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.task-detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e1e5e9;
}

.task-detail-label {
    color: #666;
    font-size: 0.875rem;
    min-width: 80px; /* 设置标签最小宽度，保持对齐 */
    flex-shrink: 0; /* 防止标签被压缩 */
}

.task-detail-value {
    color: #333;
    font-weight: 500;
    font-size: 0.875rem;
    text-align: right; /* 右对齐值 */
    flex: 1; /* 让值占据剩余空间 */
}

.task-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

.btn-view-results {
    background: #667eea;
    color: white;
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

.btn-view-results:hover {
    background: #5a6fd8;
}

.btn-view-results:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 分页样式 */
.pagination-container {
    margin-top: 2rem;
    border-top: 1px solid #e1e5e9;
    padding-top: 1.5rem;
}

.pagination-info {
    text-align: center;
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #666;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #667eea;
    color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #666;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    min-width: 2.5rem;
    text-align: center;
    transition: all 0.2s ease;
}

.page-number-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
    color: #667eea;
}

.page-number-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.loading-tasks {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-tasks {
    text-align: center;
    padding: 3rem;
    color: #666;
}


/* ADMET表格样式 */
.admet-table-container {
    margin-top: 1rem;
    max-height: 500px;
    overflow-y: auto;
    border-radius: 0.5rem;
    border: 1px solid #e1e5e9;
}

.admet-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.admet-table th,
.admet-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.admet-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.admet-table tbody tr:hover {
    background: #f8f9fa;
}

/* 属性类型行样式 */
.admet-table .cyp-row {
    background: #fff3cd;
}

.admet-table .toxicity-row {
    background: #f8d7da;
}

.admet-table .absorption-row {
    background: #d1ecf1;
}

.admet-table .property-row {
    background: #d4edda;
}

.admet-table .cyp-row:hover,
.admet-table .toxicity-row:hover,
.admet-table .absorption-row:hover,
.admet-table .property-row:hover {
    background: #e9ecef;
}

/* 属性名称列 */
.property-name {
    font-weight: 600;
    color: #495057;
    font-family: 'Courier New', monospace;
}

/* 属性描述列 */
.property-description {
    color: #666;
    max-width: 300px;
    word-wrap: break-word;
}

/* 属性数值列 */
.property-value {
    font-weight: 600;
    color: #333;
    font-family: 'Courier New', monospace;
}

/* 属性单位列 */
.property-unit {
    color: #666;
    font-size: 0.8rem;
}

/* 分子详情区域 */
.molecule-details {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e1e5e9;
}

/* 查看详情按钮 */
.view-details-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.view-details-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.view-details-btn.active {
    background: #28a745;
}

/* ADMET属性数量显示 */
.admet-count {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.5rem;
}

/* 分子信息区域 */
.molecule-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}


/* 分子选择器样式 */
.molecule-selector {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e1e5e9;
}

.molecule-selector h4 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1rem;
}

.molecule-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.molecule-tab {
    background: #e9ecef;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.molecule-tab:hover {
    background: #dee2e6;
    border-color: #adb5bd;
}

.molecule-tab.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

/* 分子项显示/隐藏 */
.molecule-item.hidden {
    display: none;
}

.molecule-item.active {
    display: block;
}

/* 在现有的CSS中添加下载按钮样式 */
.btn-download-all {
    background: #10b981 !important;
    border-color: #10b981 !important;
    color: white;
    margin-left: 0.5rem;
}

.btn-download-all:hover {
    background: #059669 !important;
    border-color: #059669 !important;
    transform: translateY(-1px);
}

.btn-download-all:disabled {
    background: #ccc !important;
    border-color: #ccc !important;
    cursor: not-allowed;
    transform: none;
}