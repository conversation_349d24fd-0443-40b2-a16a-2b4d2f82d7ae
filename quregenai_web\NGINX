server {
    listen 8090;
    server_name _ 127.0.0.1 *************;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # 前端项目访问路径
    location /quregenai/ {
        alias /workspace/quregenai_web/dist/;  # 替换为你的项目打包后的实际路径
        try_files $uri $uri/ /quregenai/index.html;  # 处理前端路由
        index index.html;
    }

    # API代理
    location /api/login/ {
        proxy_pass http://127.0.0.1:8083/api/login/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}