/* 优化批量SMILES显示样式 */
.batch-molecules-container { min-height: 500px !important; gap: 1.5rem !important; background: #f8fafc !important; }
.molecules-panel { flex: 2 !important; border-radius: 8px 0 0 8px !important; }
.structure-panel { width: 450px !important; background: white !important; border-radius: 0 8px 8px 0 !important; }
#structure2d { width: 400px !important; height: 300px !important; margin: 1.5rem !important; border: 2px solid #d1d5db !important; border-radius: 8px !important; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important; }
.molecule-item { padding: 1rem !important; margin-bottom: 0.75rem !important; border-radius: 8px !important; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important; }
.molecule-smiles { font-size: 0.8rem !important; line-height: 1.4 !important; background: #f3f4f6 !important; padding: 0.5rem !important; border-radius: 4px !important; border: 1px solid #e5e7eb !important; }
.molecule-name { font-weight: 600 !important; color: #1f2937 !important; margin-bottom: 0.5rem !important; font-size: 1rem !important; }
.molecule-item:hover { background: #f8fafc !important; border-color: #3b82f6 !important; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important; transform: translateY(-1px) !important; }
.molecule-item.selected { background: #eff6ff !important; border-color: #3b82f6 !important; box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important; }
.molecules-list { padding: 1rem !important; }
