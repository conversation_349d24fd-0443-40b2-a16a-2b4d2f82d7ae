.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page-title {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-subtitle {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.pocketvina-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.form-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
    font-family: 'Courier New', monospace;
}

.help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.5rem;
}

.smiles-examples {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border-left: 4px solid #4f46e5;
}

.smiles-examples h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.example-smiles {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.example-smiles-item {
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e1e5e9;
}

.example-smiles-item:hover {
    background: #4f46e5;
    color: white;
}

.file-upload-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.file-drop-zone {
    border: 2px dashed #cbd5e1;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #f8fafc;
    cursor: pointer;
}

.file-drop-zone:hover {
    border-color: #4f46e5;
    background: #f1f5f9;
}

.file-drop-zone.dragover {
    border-color: #4f46e5;
    background: #eef2ff;
}

.file-drop-zone-icon {
    font-size: 3rem;
    color: #94a3b8;
    margin-bottom: 1rem;
}

.file-drop-zone-text {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.file-drop-zone-hint {
    color: #94a3b8;
    font-size: 0.875rem;
}

.file-input {
    display: none;
}

.uploaded-files {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 0.75rem;
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
}

.uploaded-files h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.file-list {
    list-style: none;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.file-item:hover {
    border-color: #4f46e5;
    background: #f8fafc;
}

.file-item.selected {
    border-color: #4f46e5;
    background: #eef2ff;
}

.file-item input[type="checkbox"] {
    margin-right: 0.75rem;
}

.file-name {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #333;
}

.file-size {
    font-size: 0.75rem;
    color: #666;
    margin-left: 0.5rem;
}

.remove-file {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.remove-file:hover {
    background: #dc2626;
}

.submit-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.notification {
    position: fixed;
    top: 80px;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #10b981;
}

.notification.error {
    background: #ef4444;
}

.notification.info {
    background: #3b82f6;
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .content {
        padding: 1rem;
    }

    .file-upload-section {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}


.section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
}

.section-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tasks-container {
    margin-top: 1rem;
}

.tasks-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.tasks-header h3 {
    margin: 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 600;
}

.refresh-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.refresh-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.refresh-btn:active {
    transform: translateY(0);
}

.tasks-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.task-item {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.task-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-name {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.task-status {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.task-status.completed {
    background: #d1fae5;
    color: #065f46;
}

.task-status.running {
    background: #fef3c7;
    color: #92400e;
}

.task-status.failed {
    background: #fee2e2;
    color: #991b1b;
}

.task-status.pending {
    background: #e0e7ff;
    color: #3730a3;
}

.task-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.task-info-item {
    display: flex;
    flex-direction: column;
}

.task-info-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.task-info-value {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.task-action-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.task-action-btn:hover {
    background: #3730a3;
}

.task-action-btn.secondary {
    background: #6b7280;
}

.task-action-btn.secondary:hover {
    background: #4b5563;
}

.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pagination-container {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    font-size: 0.875rem;
    color: #6b7280;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #e5e7eb;
    color: #374151;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    min-width: 2.5rem;
    text-align: center;
}

.page-number:hover {
    background: #e5e7eb;
    color: #374151;
}

.page-number.active {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
}