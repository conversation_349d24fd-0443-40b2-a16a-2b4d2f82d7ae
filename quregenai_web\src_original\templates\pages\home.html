<!-- 
    Home Page Template
    主页页面模板
    
    功能: 显示平台欢迎信息和功能模块卡片
    继承: layout_with_nav.html
    特有样式: pages/home.css
    特有脚本: 无（使用通用导航功能）
-->
{% extends "layout_with_nav.html" %}

{% block title %}QureGenAI 药物设计平台 - 主页{% endblock %}

{% block extra_css %}
{{ super() }}
<link rel="stylesheet" href="/src/styles/pages/home.css">
{% endblock %}

{% block content %}
<!-- 欢迎区域 -->
<section class="welcome-section">
    <h1 class="welcome-title" data-i18n="welcome-title">欢迎使用QureGenAI 药物设计平台</h1>
    <p class="welcome-text" data-i18n="welcome-text">
        欢迎来到 QureGenAI！

我们是您AI与量子计算驱动的药物发现引擎。您在此可无缝调用顶尖工具：

前沿AI模型：DiffDock（精确分子对接）、AlphaFold（蛋白结构预测）等，赋能分子设计、靶点识别。
量子计算加速：突破传统计算瓶颈，探索更广阔药物空间。
灵活使用：支持网页操作、API集成及MCP服务，无缝融入您的工作流。
我们持续进化：模型库将不断加入尖端工具，助您始终掌握科技前沿。

精准付费，绝不浪费：采用按秒计费模式，真正按用量付费，尤其适合大模型调用，高效灵活。

即刻开始，让QureGenAI加速您的突破！
    </p>
</section>

<!-- 功能模块卡片 -->
<section class="feature-cards">
    <!-- AutoDock 卡片 -->
    <div class="feature-card autodock" tabindex="0" role="button" aria-label="进入AutoDock模块">
        <h3>AutoDock</h3>
        <p data-i18n="autodock-detail">AutoDock是一套自动化的分子对接软件，用于快速大批量预测小分子配体与蛋白质受体的结合模式和结合亲和力。</p>
        <span class="feature-badge" data-i18n="classic-algorithm">经典算法</span>
    </div>

    <!-- DiffDock 卡片 -->
    <div class="feature-card diffdock" tabindex="0" role="button" aria-label="进入DiffDock模块">
        <h3>DiffDock</h3>
        <p data-i18n="diffdock-detail">DiffDock是基于扩散模型的新一代扩散分子对接方法，无需指定蛋白质口袋位置，更准确地预测蛋白质-配体复合物的三维结构。</p>
        <span class="feature-badge" data-i18n="ai-driven">AI驱动</span>
    </div>

    <!-- Protenix 卡片 -->
    <div class="feature-card protenix" tabindex="0" role="button" aria-label="进入Protenix模块">
        <h3>Protenix</h3>
        <p data-i18n="protenix-detail">Protenix是先进的蛋白质结构预测平台，能够从氨基酸序列或者结构文件预测蛋白质的三维结构和相互作用等功能。</p>
        <span class="feature-badge" data-i18n="structure-prediction">结构预测</span>
    </div>

    <!-- 量子计算 卡片 -->
    <div class="feature-card quantum" tabindex="0" role="button" aria-label="进入量子计算任务模块">
        <h3 data-i18n="quantum-computing">量子计算</h3>
        <p data-i18n="quantum-detail">利用量子计算的强大能力加速药物发现过程，突破传统计算瓶颈，探索更广阔的分子空间和复杂的量子化学计算。</p>
        <span class="feature-badge" data-i18n="quantum-acceleration">量子加速</span>
    </div>

    <!-- MolMap 卡片 -->
    <div class="feature-card molmap" tabindex="0" role="button" aria-label="进入MolMap模块">
        <h3>MolMap</h3>
        <p data-i18n="molmap-detail">MolMap是先进的分子性质预测平台，能够快速准确地预测小分子的ADMET性质，包括吸收、分布、代谢、排泄和毒性等关键药物性质。</p>
        <span class="feature-badge" data-i18n="admet-prediction">ADMET预测</span>
    </div>

    <!-- PocketVina 卡片 -->
    <div class="feature-card pocketvina" tabindex="0" role="button" aria-label="进入PocketVina模块">
        <h3>PocketVina</h3>
        <p data-i18n="pocketvina-detail">PocketVina是新一代全自动口袋寻找与高通量筛选工具，无需手动指定结合位点，自动识别蛋白质表面的潜在结合口袋，支持多个分子和蛋白质文件的高通量筛选分析。</p>
        <span class="feature-badge" data-i18n="auto-pocket-docking">全自动口袋对接</span>
    </div>

    <!-- Raman Spectral 卡片 -->
    <div class="feature-card raman" tabindex="0" role="button" aria-label="进入Raman光谱分析模块">
        <h3>Raman Spectral</h3>
        <p data-i18n="raman-detail">Raman光谱检测是一种低成本、高效的光谱分析技术，广泛应用于中草药成分含量检测、农作物农药残留检测、药物成分分析等领域，为食品安全和药物质量控制提供可靠的技术支持。</p>
        <span class="feature-badge" data-i18n="spectral-analysis">光谱分析</span>
    </div>
</section>
{% endblock %}

{% block extra_js %}
{{ super() }}
<!-- 主页特有的JavaScript逻辑 -->
<script>
// 功能卡片点击和键盘导航
document.addEventListener('DOMContentLoaded', function() {
    // 功能卡片点击跳转（这个逻辑已经在navigation.js中实现）
    // 这里可以添加主页特有的其他逻辑

    // 键盘导航支持
    document.addEventListener('keydown', function(e) {
        if (e.target.matches('.feature-card') && (e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            e.target.click();
        }
    });

    // 添加焦点管理
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.addEventListener('keydown', function(e) {
            let targetIndex;

            switch(e.key) {
                case 'ArrowRight':
                case 'ArrowDown':
                    e.preventDefault();
                    targetIndex = (index + 1) % featureCards.length;
                    featureCards[targetIndex].focus();
                    break;
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    targetIndex = (index - 1 + featureCards.length) % featureCards.length;
                    featureCards[targetIndex].focus();
                    break;
                case 'Home':
                    e.preventDefault();
                    featureCards[0].focus();
                    break;
                case 'End':
                    e.preventDefault();
                    featureCards[featureCards.length - 1].focus();
                    break;
            }
        });
    });
});
</script>
{% endblock %}
