<template>
  <div class="autodock-container">
    <!-- 通知组件 -->
    <div class="notifications-container">
      <transition-group name="notification">
        <div 
          v-for="notification in notifications" 
          :key="notification.id" 
          :class="['notification', `notification-${notification.type}`]"
          :style="getNotificationStyle(notification.type)">
          {{ notification.message }}
        </div>
      </transition-group>
    </div>

    <!-- 主体容器 -->
    <div class="main-container">
      <!-- 页面主体内容 -->
      <main class="content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">
            <span class="page-icon"><img src="../assets/images/autodock-cube.svg" alt="AutoDock"></span>
            AutoDock 自动分子对接
          </h1>
          <p class="page-subtitle">
            AutoDock是一套应用广泛的开源分子对接软件套件，用于预测小分子配体（如候选药物）与已知三维结构的生物大分子（通常是蛋白质受体）的结合模式和亲和力。
            它通过模拟配体在受体活性位点的各种可能构象和朝向，并利用能量打分函数评估结合强度，从而帮助科研人员理解分子间的相互作用机制，并广泛应用于药物发现和虚拟筛选等领域。
            该套件主要包含AutoDock程序（执行对接计算，采用拉马克遗传算法和经验自由能打分函数）和AutoGrid程序（预先计算格点图，用于表示受体）。
          </p>
        </div>

        <!-- 对接口袋识别和分析指南 -->
        <div class="docking-pocket-guide">
          <h3 class="guide-title">🎯 对接口袋识别与分析指南</h3>
          
          <div class="guide-section">
            <div class="guide-card">
              <div class="card-header">
                <h4 class="card-title">
                  <span class="card-icon">🔍</span>
                  步骤一：蛋白质结构分析
                </h4>
              </div>
              <div class="card-content">
                <ul>
                  <li><strong>上传蛋白质文件：</strong>首先上传PDB或PDBQT格式的蛋白质结构文件</li>
                  <li><strong>观察整体结构：</strong>在3D查看器中旋转、缩放，熟悉蛋白质的整体折叠结构</li>
                  <li><strong>寻找潜在口袋：</strong>寻找蛋白质表面的凹陷区域，这些通常是配体结合位点</li>
                  <li><strong>关注活性位点：</strong>如果已知活性中心残基，重点观察这些区域</li>
                </ul>
              </div>
            </div>

            <div class="guide-card">
              <div class="card-header">
                <h4 class="card-title">
                  <span class="card-icon">📍</span>
                  步骤二：口袋中心点确定
                </h4>
              </div>
              <div class="card-content">
                <ul>
                  <li><strong>点击原子选择：</strong>在3D查看器中点击口袋内的关键原子（如活性位点残基）</li>
                  <li><strong>查看原子信息：</strong>选中的原子信息会显示在下方，包括残基类型和坐标</li>
                  <li><strong>设置中心点：</strong>点击原子信息条目可直接将该坐标设为对接中心</li>
                  <li><strong>多原子平均：</strong>选择多个原子时，系统会自动计算几何中心</li>
                </ul>
              </div>
            </div>

            <div class="guide-card">
              <div class="card-header">
                <h4 class="card-title">
                  <span class="card-icon">📏</span>
                  步骤三：对接盒子尺寸设置
                </h4>
              </div>
              <div class="card-content">
                <ul>
                  <li><strong>估算口袋大小：</strong>观察目标口袋的大致尺寸范围</li>
                  <li><strong>考虑配体尺寸：</strong>确保对接盒子足够容纳配体分子的各种构象</li>
                  <li><strong>推荐尺寸：</strong>
                    <ul>
                      <li>小分子配体：15-20 Å</li>
                      <li>肽段配体：20-25 Å</li>
                      <li>大分子配体：25-30 Å</li>
                    </ul>
                  </li>
                </ul>
              </div>
            </div>

            <div class="guide-card">
              <div class="card-header">
                <h4 class="card-title">
                  <span class="card-icon">💡</span>
                  专业建议与注意事项
                </h4>
              </div>
              <div class="card-content">
                <div class="tips-grid">
                  <div class="tip-item">
                    <strong>🎪 常见口袋类型：</strong>
                    <ul>
                      <li>酶活性位点（通常较深且狭窄）</li>
                      <li>蛋白质-蛋白质相互作用界面（通常较浅且宽阔）</li>
                      <li>变构调节位点（远离活性中心）</li>
                    </ul>
                  </div>
                  
                  <div class="tip-item">
                    <strong>⚠️ 注意事项：</strong>
                    <ul>
                      <li>避免将对接盒子设置得过大，会增加计算时间</li>
                      <li>确保盒子完全包含目标结合位点</li>
                      <li>考虑蛋白质柔性，为侧链运动留出空间</li>
                      <li>如有已知结合物结构，可参考其结合模式</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分子查看器区域 -->
        <section class="molecule-viewer-section">
          <h2 class="section-title">分子结构查看器</h2>
          <div id="loading-indicator" v-show="loadingVisible">加载中...</div>
          <div class="error-message" v-if="errorMessage">{{ errorMessage }}</div>
          <div class="viewer-container">
            <div class="viewer-section">
              <div ref="viewerContainer" class="molecule-viewer"></div>
            </div>
            
            <div class="controls-section">
              <div class="upload-section">
                <label for="file-input" class="upload-btn">
                  点击上传结构文件
                </label>
                <input type="file" id="file-input" multiple accept=".pdb,.sdf,.pdbqt" @change="handleFileUploadEvent">
                <div class="file-type-info">
                  <strong>支持的文件格式：</strong><br>
                  • PDB文件 - 蛋白质结构（卡通渲染）<br>
                  • SDF文件 - 小分子结构（球棍模型）<br>
                  • PDBQT文件 - 电荷结构文件<br>
                </div>
              </div>

              <div id="file-list-container">
                <h3>已上传文件 <span v-if="uploadedFiles.length">({{ uploadedFiles.length }})</span></h3>
                <div v-if="isLoading" class="loading-indicator">正在上传文件...</div>
                <div v-else-if="errorMessage" class="error-message">{{ errorMessage }}</div>
                <div v-else-if="uploadedFiles.length === 0" class="empty-message">暂无文件</div>
                <ul v-else class="file-list">
                  <li v-for="file in uploadedFiles" :key="file.id" class="file-item">
                    <span class="file-icon">{{ getFileTypeIcon(file.type) }}</span>
                    <div class="file-details">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-info">{{ file.type.toUpperCase() }} • {{ formatFileSize(file.size) }}</span>
                    </div>
                    <button class="delete-file-btn" @click="deleteFile(file.id)" title="删除文件">×</button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 原子信息列表区域 -->
          <div class="atom-info-section">
            <div class="atom-info-header">
              <h3>已选择的原子信息</h3>
              <button class="clear-atoms-btn" @click="clearSelectedAtoms">一键清除</button>
            </div>
            
            <!-- 使用说明 -->
            <div class="atom-selection-guide" style="background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem;">
              <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                <div style="font-size: 1.25rem; color: #0284c7; margin-top: 0.125rem;">💡</div>
                <div style="flex: 1;">
                  <h4 style="margin: 0 0 0.5rem 0; color: #0c4a6e; font-size: 0.95rem; font-weight: 600;">原子选择使用说明：</h4>
                  <ul style="margin: 0; padding-left: 1.25rem; color: #0369a1; font-size: 0.875rem; line-height: 1.5;">
                    <li style="margin-bottom: 0.25rem;">在上方3D分子查看器中，<strong>点击蛋白质或小分子上的原子</strong>进行选择</li>
                    <li style="margin-bottom: 0.25rem;">选中的原子信息会显示在下方列表中，包括原子类型、残基信息和坐标</li>
                    <li style="margin-bottom: 0.25rem;"><strong>点击下方原子信息条目</strong>，可直接将该原子坐标设为对接口袋中心点</li>
                    <li style="margin-bottom: 0;">选择<strong>多个原子</strong>时，系统会自动计算它们的几何中心作为口袋中心坐标</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <!-- 对接口袋中心选择模式 -->
            <div v-if="selectedAtoms.length > 1" class="pocket-center-mode-container">
              <div class="pocket-center-title">🎯 对接口袋中心选择模式</div>
              <div class="pocket-center-buttons">
                <button
                  class="pocket-mode-btn"
                  :class="{ active: pocketCenterMode === 'single' }"
                  @click="switchPocketCenterModeWithNotification('single')">
                  <span class="btn-icon">📍</span>
                  单个原子
                  <span class="btn-desc">点击原子设置</span>
                </button>
                <button
                  class="pocket-mode-btn"
                  :class="{ active: pocketCenterMode === 'center' }"
                  @click="switchPocketCenterModeWithNotification('center')">
                  <span class="btn-icon">🎯</span>
                  几何中心
                  <span class="btn-desc">自动计算</span>
                </button>
              </div>
              <div class="mode-description">
                <span v-if="pocketCenterMode === 'single'">
                  💡 <strong>单个原子模式</strong>：点击下方任意原子将其坐标设为口袋中心
                </span>
                <span v-else>
                  💡 <strong>几何中心模式</strong>：使用所有选中原子的几何中心作为口袋中心
                </span>
              </div>
            </div>

            <div class="atom-info-list">
              <p v-if="selectedAtoms.length === 0" class="no-atoms-message">暂无选择的原子</p>
              <ul v-else>
                <li v-for="(atom, index) in selectedAtoms" :key="atom.id"
                    class="atom-info-item"
                    :class="{
                      'selected-for-center': pocketCenterMode === 'center' && atom.includeInCenter !== false,
                      'excluded-from-center': pocketCenterMode === 'center' && atom.includeInCenter === false
                    }"
                    @click="handleAtomItemClick(atom, index)">
                  <button class="remove-atom-btn" @click.stop="removeAtomFromList(index)" title="移除此原子">×</button>
                  <div class="atom-info-header">
                    <span class="atom-element">{{ atom.element }}</span>
                    <span class="atom-resinfo">{{ atom.resname }}:{{ atom.resno }}</span>
                    <span class="atom-file">{{ atom.fileName }}</span>
                  </div>
                  <div class="atom-coordinates">
                    <span>X: {{ atom.x.toFixed(2) }}</span>
                    <span>Y: {{ atom.y.toFixed(2) }}</span>
                    <span>Z: {{ atom.z.toFixed(2) }}</span>
                  </div>
                  <div v-if="pocketCenterMode === 'center'" class="center-status">
                    <span v-if="atom.includeInCenter !== false" style="color: #28a745;">✓ 参与计算</span>
                    <span v-else style="color: #dc3545;">✗ 已排除</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <!-- 输入参数设置 -->
          <div class="input-files-section">
            <h3 class="section-title">AutoDock 输入参数设置</h3>
            
            <!-- 文件选择分组 -->
            <div class="parameter-group">
              <div class="group-header">
                <h4 class="group-title">📁 对接文件选择</h4>
                <div class="group-description">选择用于分子对接的蛋白质和小分子文件</div>
              </div>
              
              <div class="input-grid">
                <!-- 蛋白质文件选择 -->
                <div class="input-group">
                  <label class="input-label">
                    <span class="label-text">🧬 蛋白质受体文件 (PDB)</span>
                    <span class="required">*</span>
                  </label>
                  
                  <!-- 蛋白质文件选择区域 -->
                  <div class="protein-file-selection-section">
                    <!-- 已上传文件选择 -->
                    <div class="uploaded-files-section">
                      <h5 class="section-subtitle">
                        <span class="subtitle-icon">📂</span>
                        从已上传的结构文件选择
                      </h5>
                      <div id="protein-files-grid" class="uploaded-files-grid">
                        <div v-if="filteredProteinFiles.length === 0" class="no-files-message">
                          暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件
                        </div>
                        <div v-else class="files-grid">
                          <div 
                            v-for="file in filteredProteinFiles" 
                            :key="file.id" 
                            class="file-card"
                            :class="{ 'selected': selectedProteinFile === file.id }"
                            @click="selectProteinFileWithNotification(file.id)">
                            <div class="file-name">
                              <span class="file-icon">{{ getFileTypeIcon(file.type) }}</span>
                              {{ file.name }}
                            </div>
                            <div class="file-meta">{{ file.type.toUpperCase() }} • {{ formatFileSize(file.size) }}</div>
                            <div class="selection-indicator" v-if="selectedProteinFile === file.id">✓</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 分隔线 -->
                    <div class="section-divider">
                      <span class="divider-text">或</span>
                    </div>
                    
                    <!-- 本地文件上传 -->
                    <div class="local-upload-section">
                      <h5 class="section-subtitle">
                        <span class="subtitle-icon">💾</span>
                        上传本地受体文件
                      </h5>
                      <div class="local-upload-area">
                        <input type="file" ref="localProteinInput" accept=".pdb,.pdbqt" style="display: none;" @change="handleLocalProteinUpload">
                        <div class="upload-drop-zone" 
                             @click="triggerProteinFileInput" 
                             @dragover.prevent="handleDragOver" 
                             @dragleave.prevent="handleDragLeave" 
                             @drop.prevent="handleProteinDrop">
                          <div class="upload-icon">📁</div>
                          <div class="upload-text">
                            <strong>点击选择PDB/PDBQT文件</strong> 或 <strong>拖拽文件到此处</strong>
                          </div>
                          <div class="upload-hint">
                            支持格式：PDB、PDBQT
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 已选蛋白质文件显示 -->
                    <div id="selected-protein-display" class="selected-files-display" style="display: none;">
                      <div class="selected-files-header">
                        <h5>已选择的蛋白质文件</h5>
                        <button id="clear-protein-selection-btn" class="clear-selection-btn">清空选择</button>
                      </div>
                      <div id="selected-protein-info" class="selected-files-list"></div>
                    </div>
                  </div>
                  
                  <div class="input-hint">选择一个PDB或PDBQT格式的蛋白质文件作为对接受体</div>
                </div>
              </div>

              <!-- 小分子文件选择 -->
              <div class="input-group" style="margin-top: 1.5rem;">
                <label class="input-label">
                  <span class="label-text">💊 小分子配体文件</span>
                  <span class="required">*</span>
                </label>
                
                <!-- Tab导航 -->
                <div class="ligand-tabs">
                  <button type="button" 
                          class="ligand-tab" 
                          :class="{ active: ligandInputMode === 'file' }"
                          @click="switchLigandTabWithNotification('file')">选择配体分子结构文件</button>
                  <button type="button"
                          class="ligand-tab"
                          :class="{ active: ligandInputMode === 'smiles' }"
                          @click="switchLigandTabWithNotification('smiles')">批量SMILES文件</button>
                  <button type="button"
                          class="ligand-tab"
                          :class="{ active: ligandInputMode === 'smiles' }"
                          @click="switchLigandTabWithNotification('smiles')">批量SMILES输入</button>
                </div>

                <!-- Tab内容 -->
                <!-- 分子结构文件选择 -->
                <div id="files-tab-content" class="ligand-tab-content" :class="{ active: ligandInputMode === 'file' }">
                  <div class="ligand-input-container">
                    <!-- 文件选择区域 -->
                    <div class="file-selection-section">
                      <!-- 已上传文件选择 -->
                      <div class="uploaded-files-section">
                        <h5 class="section-subtitle">
                          <span class="subtitle-icon">📂</span>
                          从已上传的配体分子文件中选择
                        </h5>
                        <div id="uploaded-files-grid" class="uploaded-files-grid">
                          <div v-if="filteredLigandFiles.length === 0" class="no-files-message">
                            暂无已上传的分子文件，请先在上方分子查看器中上传文件
                          </div>
                          <div v-else class="files-grid">
                            <div 
                              v-for="file in filteredLigandFiles" 
                              :key="file.id" 
                              class="file-card"
                              :class="{ 'selected': selectedLigandFiles.includes(file.id) }"
                              @click="toggleLigandFileSelectionWithNotification(file.id)">
                              <div class="file-name">
                                <span class="file-icon">{{ getFileTypeIcon(file.type) }}</span>
                                {{ file.name }}
                              </div>
                              <div class="file-meta">{{ file.type.toUpperCase() }} • {{ formatFileSize(file.size) }}</div>
                              <div class="selection-indicator" v-if="selectedLigandFiles.includes(file.id)">✓</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- 分隔线 -->
                      <div class="section-divider">
                        <span class="divider-text">或</span>
                      </div>
                      
                      <!-- 本地文件上传 -->
                      <div class="local-upload-section">
                        <h5 class="section-subtitle">
                          <span class="subtitle-icon">💾</span>
                          上传本地配体文件
                        </h5>
                        <div class="local-upload-area">
                          <input type="file" ref="localLigandInput" multiple accept=".pdb,.sdf,.pdbqt,.mol2" style="display: none;" @change="handleLocalLigandUpload">
                          <div class="upload-drop-zone" 
                               @click="triggerLigandFileInput" 
                               @dragover.prevent="handleDragOver" 
                               @dragleave.prevent="handleDragLeave" 
                               @drop.prevent="handleDrop">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">
                              <strong>点击选择文件</strong> 或 <strong>拖拽文件到此处</strong>
                            </div>
                            <div class="upload-hint">
                              支持格式：PDB、SDF、PDBQT、MOL2
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- 已选文件显示 -->
                      <div id="selected-files-display" class="selected-files-display" style="display: none;">
                        <div class="selected-files-header">
                          <h5>已选择的文件 (<span id="selected-files-count">0</span>个)</h5>
                          <button id="clear-selection-btn" class="clear-selection-btn">清空选择</button>
                        </div>
                        <div id="selected-files-list" class="selected-files-list"></div>
                      </div>
                    </div>
                  </div>
                  <div class="input-hint">
                    点击已上传文件或上传本地文件进行选择<br>
                    <strong>选择1个文件：</strong>使用单分子对接模式<br>
                    <strong>选择多个文件：</strong>使用多分子批量对接模式
                  </div>
                </div>

                <!-- 批量SMILES文件上传 -->
                <div id="batch-file-tab-content" class="ligand-tab-content" :class="{ active: ligandInputMode === 'smiles' }">
                  <div class="batch-upload-container">
                    <div class="csv-upload-section">
                      <input type="file" ref="csvFileInput" accept=".csv" @change="handleCsvUpload" style="display: none;">
                      <button class="upload-btn" @click="triggerCsvFileInput">
                        <span class="btn-icon">📁</span>
                        上传CSV文件
                      </button>
                      <div class="csv-format-hint">
                        CSV格式：第一列为name，第二列为smiles<br>
                        例如：aspirin,CC(=O)OC1=CC=CC=C1C(=O)O
                      </div>
                    </div>
                    
                    <div class="batch-molecules-container" ref="batchContainer" v-show="parsedMolecules.length > 0">
                      <div class="molecules-panel">
                        <div class="panel-header">
                          <h5>分子列表 (<span>{{ moleculeCount }}</span>个)</h5>
                          <div class="batch-status-info" style="font-size: 0.8rem; color: #059669; margin-top: 0.25rem;">
                            💊 所有分子将用于批量对接
                          </div>
                          <div class="search-container">
                            <input type="text" placeholder="搜索分子名称...">
                            <button class="search-btn">🔍</button>
                          </div>
                        </div>
                        <div id="molecules-list" ref="moleculesList" class="molecules-list"></div>
                      </div>
                      
                      <div class="structure-panel">
                        <div class="panel-header">
                          <h5>2D结构</h5>
                        </div>
                        <canvas id="structure2d" ref="structure2d" width="300" height="200"></canvas>
                        <div id="molecule-info" ref="moleculeInfo" class="molecule-info"></div>
                      </div>
                    </div>
                  </div>
                  <div class="input-hint">上传包含分子名称和SMILES的CSV文件，所有分子将自动用于批量对接</div>
                </div>

                <!-- 批量SMILES直接输入 -->
                <div id="batch-text-tab-content" class="ligand-tab-content" :class="{ active: ligandInputMode === 'smiles' }">
                  <div class="text-input-container">
                    <label for="batch-smiles-text" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151;">
                      直接输入SMILES字符串
                    </label>
                    <textarea 
                      ref="batchSmilesText"
                      id="batch-smiles-text" 
                      v-model="smilesInput"
                      placeholder="每行输入一个SMILES字符串，例如：&#10;CCO&#10;CC(=O)OC1=CC=CC=C1C(=O)O&#10;CN1C=NC2=C1C(=O)N(C(=O)N2C)C"
                      style="width: 100%; min-height: 120px; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 0.5rem; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.875rem; resize: vertical; transition: border-color 0.2s ease;">
                    </textarea>
                    <div class="text-input-hint" style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">
                      每行输入一个SMILES字符串，系统将自动为每个分子生成名称
                    </div>
                    <button class="upload-btn" @click="handleSmilesTextParse" style="margin-top: 0.75rem;">
                      <span class="btn-icon">📝</span>
                      解析SMILES文本
                    </button>
                  </div>
                  <div class="input-hint">每行输入一个SMILES字符串，系统将自动为每个分子生成名称</div>
                </div>
              </div>
            </div>

            <!-- 对接参数分组 -->
            <div class="parameter-group">
              <div class="group-header">
                <h4 class="group-title">⚙️ 对接参数</h4>
                <div class="group-description">设置分子对接的空间范围和计算参数</div>
              </div>
              
              <div class="input-grid">
                <!-- 口袋坐标 -->
                <div class="input-group full-width">
                  <label class="input-label">
                    <span class="label-text">🎯 口袋中心坐标</span>
                    <span class="required">*</span>
                  </label>
                  <div class="coordinate-container">
                    <div class="coordinate-input-group">
                      <label for="pocket-x">X 坐标</label>
                      <input type="number" id="pocket-x" v-model="pocketX" step="0.1" placeholder="0.0">
                    </div>
                    <div class="coordinate-input-group">
                      <label for="pocket-y">Y 坐标</label>
                      <input type="number" id="pocket-y" v-model="pocketY" step="0.1" placeholder="0.0">
                    </div>
                    <div class="coordinate-input-group">
                      <label for="pocket-z">Z 坐标</label>
                      <input type="number" id="pocket-z" v-model="pocketZ" step="0.1" placeholder="0.0">
                    </div>
                    <button class="auto-fill-btn" @click="calculateAndSetGeometricCenter">
                      <span class="btn-icon">🎯</span>
                      自动填入
                    </button>
                  </div>
                  <div class="input-hint">手动输入坐标或点击按钮从已选择的原子计算中心坐标</div>
                </div>

                <!-- 对接盒子尺寸 -->
                <div class="input-group">
                  <label class="input-label">
                    <span class="label-text">📦 对接盒子尺寸 (Å)</span>
                  </label>
                  <div class="size-container">
                    <div class="size-input-group">
                      <label for="size-x">X 尺寸</label>
                      <input type="number" id="size-x" v-model="sizeX" value="15" min="1" max="100">
                    </div>
                    <div class="size-input-group">
                      <label for="size-y">Y 尺寸</label>
                      <input type="number" id="size-y" v-model="sizeY" value="15" min="1" max="100">
                    </div>
                    <div class="size-input-group">
                      <label for="size-z">Z 尺寸</label>
                      <input type="number" id="size-z" v-model="sizeZ" value="15" min="1" max="100">
                    </div>
                  </div>
                  <div class="input-hint">定义搜索空间的尺寸，默认为15Å × 15Å × 15Å</div>
                </div>

                <!-- 线程数设置 -->
                <div class="input-group">
                  <label for="threads" class="input-label">
                    <span class="label-text">⚡ 计算线程数</span>
                  </label>
                  <div class="threads-input-wrapper">
                    <input type="number" id="threads" v-model="threads" value="2000" min="1" max="10000" class="threads-input">
                    <div class="input-unit">线程</div>
                  </div>
                  <div class="input-hint">设置计算使用的线程数量，推荐值：2000-5000</div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons-container">
              <button id="validate-inputs" class="btn-secondary" @click="validateInputs">
                <span class="btn-icon">✅</span>
                验证输入
              </button>
              <button id="submit-btn" class="btn-primary" @click="submitFormNew" :disabled="!canSubmit">
                <span class="btn-icon">🚀</span>
                开始对接
              </button>
              <button id="reset-inputs" class="btn-secondary" @click="resetForm">
                <span class="btn-icon">🔄</span>
                重置参数
              </button>
            </div>

            <!-- 对接结果栏 -->
            <div class="docking-results-section" v-show="resultsVisible">
              <div class="results-header">
                <h3 class="results-title">
                  <span class="results-icon">📊</span>
                  对接结果
                </h3>
                <div class="results-controls">
                  <div class="results-status">
                    <span class="status-text">{{ resultsStatusText }}</span>
                    <div class="loading-spinner" v-if="isResultsLoading"></div>
                  </div>
                  <button class="hide-results-btn" title="隐藏结果" @click="hideResults">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M18 6L6 18M6 6l12 12"/>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="results-content">
                <!-- 任务信息 -->
                <div class="result-card task-info-card">
                  <div class="card-header">
                    <h4>任务信息</h4>
                  </div>
                  <div class="card-content">
                    <!-- Task ID row -->
                    <div class="task-id-row">
                      <span class="info-label">任务ID:</span>
                      <span class="info-value task-id-value">{{ currentTask?.task_id || '-' }}</span>
                    </div>
                    
                    <!-- Only show status -->
                    <div class="info-grid-compact">
                      <div class="info-item">
                        <span class="info-label">状态:</span>
                        <span class="info-value status-badge" :class="`status-${currentTask?.status || 'pending'}`">{{ getStatusText(currentTask?.status) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 结果详情 -->
                <div class="result-card results-details-card">
                  <div class="card-header">
                    <h4>结果详情</h4>
                    <button class="toggle-btn" @click="toggleDetailsView">{{ detailsExpanded ? '收起' : '展开' }}</button>
                  </div>
                  <div class="card-content">
                    <div class="human-readable-results" v-html="humanReadableResults"></div>
                    
                    <div class="detailed-results" v-show="detailsExpanded">
                      <div class="results-tabs">
                        <button class="tab-btn" :class="{ active: activeResultTab === 'scores' }" @click="activeResultTab = 'scores'">结合能量</button>
                        <button class="tab-btn" :class="{ active: activeResultTab === 'files' }" @click="activeResultTab = 'files'">文件下载</button>
                      </div>
                      <div class="tab-content" v-show="activeResultTab === 'scores'">
                        <div class="scores-list">
                          <div v-if="!scores.length" class="no-data-message">暂无结合能量数据</div>
                          <ul v-else>
                            <li v-for="(score, index) in paginatedScores" :key="index">
                              <div>
                                <strong>{{ score.filename }} (排名: {{ getItemRank(index) }}):</strong>
                                <div v-if="Array.isArray(score.scoresArray) && score.scoresArray.length > 0" style="margin-top: 0.5rem;">
                                  <div style="color: #059669; font-weight: 600;">
                                    最佳结合能量: {{ Math.min(...score.scoresArray) }} kcal/mol (构象 {{ score.scoresArray.indexOf(Math.min(...score.scoresArray)) + 1 }})
                                  </div>
                                  <div style="margin-top: 0.5rem;">
                                    <strong>构象结合能量 (前{{ Math.min(score.scoresArray.length, 3) }}个):</strong>
                                    <ul style="margin-top: 0.25rem;">
                                      <li v-for="(affinity, i) in score.scoresArray.slice(0, 3)" :key="i" 
                                          :style="{
                                            color: affinity === Math.min(...score.scoresArray) ? '#059669' : '#6b7280',
                                            fontWeight: affinity === Math.min(...score.scoresArray) ? '600' : 'normal'
                                          }">
                                        构象 {{ i + 1 }}: {{ affinity }} kcal/mol 
                                        <span v-if="affinity === Math.min(...score.scoresArray)">(最佳)</span>
                                      </li>
                                      <li v-if="score.scoresArray.length > 3" style="color: #9ca3af; font-style: italic;">
                                        ... 还有 {{ score.scoresArray.length - 3 }} 个构象
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <span v-else style="color: #ef4444;">无有效分数数据</span>
                              </div>
                            </li>
                          </ul>
                          
                          <!-- 分页控件 -->
                          <div v-if="scores.length > pageSize" class="pagination-controls">
                            <button 
                              @click="updateScoresPagination(currentPage - 1)"
                              :disabled="currentPage === 1"
                              class="page-btn"
                            >上一页</button>
                            <span>{{ currentPage }} / {{ totalPages }}</span>
                            <button 
                              @click="updateScoresPagination(currentPage + 1)"
                              :disabled="currentPage === totalPages"
                              class="page-btn"
                            >下一页</button>
                          </div>
                        </div>
                      </div>
                      
                      <div class="tab-content" v-show="activeResultTab === 'files'">
                        <div class="files-list">
                          <div v-if="!resultFiles.length" class="no-data-message">暂无可下载文件</div>
                          <ul v-else>
                            <li v-for="(file, index) in paginatedFiles" :key="index">
                              <span style="font-weight: 600; color: #1f2937;">
                                {{ file.filename }} (排名: {{ getItemRank(index) }})
                              </span>
                              <div v-if="Array.isArray(file.urlsArray) && file.urlsArray.length > 0" 
                                   style="display: flex; gap: 0.5rem; margin-top: 0.5rem; flex-wrap: wrap;">
                                <button v-for="(url, urlIndex) in file.urlsArray" :key="urlIndex"
                                        @click="downloadFileFromUrl(url, `${file.filename}_pose_${urlIndex + 1}`)"
                                        style="padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.875rem; cursor: pointer;">
                                  {{ file.urlsArray.length === 1 ? '下载文件' : `下载构象 ${urlIndex + 1}` }}
                                </button>
                              </div>
                              <span v-else style="color: #ef4444;">无可用下载链接</span>
                            </li>
                          </ul>
                          
                          <!-- 分页控件 -->
                          <div v-if="resultFiles.length > pageSize" class="pagination-controls">
                            <button 
                              @click="updateFilesPagination(currentPage - 1)"
                              :disabled="currentPage === 1"
                              class="page-btn"
                            >上一页</button>
                            <span>{{ currentPage }} / {{ totalPages }}</span>
                            <button 
                              @click="updateFilesPagination(currentPage + 1)"
                              :disabled="currentPage === totalPages"
                              class="page-btn"
                            >下一页</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 任务历史部分 -->
        <div class="task-history-section">
          <div class="section-header">
            <h2>对接任务历史</h2>
            <button class="btn btn-refresh-green" @click="loadTaskHistory">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="23 4 23 10 17 10"></polyline>
                <polyline points="1 20 1 14 7 14"></polyline>
                <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
              </svg>
              刷新
            </button>
          </div>
          <div class="task-history-container">
            <div class="tasks-list">
              <div v-if="isTasksLoading" class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>加载任务历史中...</p>
              </div>
              <div v-else-if="errorTaskMessage" class="loading-placeholder">
                <p style="color: #ef4444;">❌ {{ errorTaskMessage }}</p>
              </div>
              <div v-else-if="tasks.length === 0" class="loading-placeholder">
                <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">📋</div>
                <p>暂无AutoDock任务历史</p>
                <p style="font-size: 0.875rem; color: #9ca3af; margin-top: 0.5rem;">
                  提交第一个AutoDock任务开始使用
                </p>
              </div>
              <div v-else class="task-item-list">
                <div v-for="task in tasks" :key="task.task_id" class="task-item" :data-task-id="task.task_id">
                  <div class="task-header">
                    <span class="task-id">{{ task.task_id }}</span>
                    <span class="task-status" :class="task.status">{{ getStatusText(task.status) }}</span>
                  </div>
                  <div class="task-info">
                    <div>创建时间: {{ formatDateTime(task.created_at) }}</div>
                    <div>更新时间: {{ formatDateTime(task.updated_at) }}</div>
                    <div v-if="task.completed_at">完成时间: {{ formatDateTime(task.completed_at) }}</div>
                  </div>
                  <div class="task-actions">
                    <button class="task-btn view" @click="viewTaskDetails(task)">查看详情</button>
                    <button v-if="task.status === 'completed'" class="task-btn view" @click="showTaskResults(task)">查看结果</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
    
    <!-- 任务详情模态框 -->
    <div v-if="showTaskDetailsModal" class="modal-overlay" @click="closeTaskDetailsModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>任务详情</h3>
          <button @click="closeTaskDetailsModal" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="detail-row">
            <label>任务ID:</label>
            <span class="task-id">{{ selectedTask?.task_id }}</span>
          </div>
          <div class="detail-row">
            <label>状态:</label>
            <span class="task-status" :class="selectedTask?.status">{{ getStatusText(selectedTask?.status) }}</span>
          </div>
          <div class="detail-row">
            <label>创建时间:</label>
            <span>{{ formatDateTime(selectedTask?.created_at) }}</span>
          </div>
          <div class="detail-row">
            <label>更新时间:</label>
            <span>{{ formatDateTime(selectedTask?.updated_at) }}</span>
          </div>
          <div v-if="selectedTask?.completed_at" class="detail-row">
            <label>完成时间:</label>
            <span>{{ formatDateTime(selectedTask?.completed_at) }}</span>
          </div>
          <div v-if="selectedTask?.error_message" class="detail-row">
            <label>错误信息:</label>
            <span style="color: #ef4444;">{{ selectedTask?.error_message }}</span>
          </div>
          <div class="detail-row">
            <label>参数:</label>
            <pre class="parameters-display">{{ formatTaskParameters(selectedTask?.parameters) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { onMounted, ref, reactive, computed, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { useMoleculeViewer } from '../composables/useMoleculeViewer.js';
  import { useFileManager } from '../composables/useFileManager.js';
  import { useSmilesProcessor } from '../composables/useSmilesProcessor.js';
  import { useFormValidation } from '../composables/useFormValidation.js';
  import { useTaskManager } from '../composables/useTaskManager.js';

  // 导入AutoDock专用样式
  import '../assets/styles/autodock.css';

  // 开发环境下导入验证工具
  if (import.meta.env.DEV) {
    import('../utils/autoDockValidation.js').then(module => {
      window.validateAutoDockRefactoring = module.validateAutoDockRefactoring;
      window.checkSpecificFeature = module.checkSpecificFeature;
      console.log('🔧 AutoDock 验证工具已加载，在控制台运行 validateAutoDockRefactoring() 进行验证');
    });
  }

  export default {
    name: 'AutoDock',
    setup() {
      const router = useRouter();

      // 使用分子查看器 composable
      const {
        viewer,
        viewerContainer,
        isViewerReady,
        loadingVisible,
        errorMessage,
        uploadedFiles,
        selectedAtoms,
        pocketCenterMode,
        loadAllScripts,
        initializeViewer,
        handleFileUpload,
        deleteFile,
        clearSelectedAtoms,
        calculateGeometricCenter,
        switchPocketCenterMode,
        toggleAtomInCenter,
        setAtomAsPocketCenter,
        getFileTypeIcon,
        formatFileSize
      } = useMoleculeViewer();

      // 使用文件管理 composable
      const {
        selectedProteinFile,
        selectedLigandFiles,
        ligandInputMode,
        smilesInput,
        smilesFiles,
        filteredProteinFiles,
        filteredLigandFiles,
        selectProteinFile,
        toggleLigandFileSelection,
        switchLigandTab,
        processSmilesInput,
        deleteSmilesFile,
        clearAllSmilesFiles,
        handleCsvUpload,
        validateFileSelection,
        getSelectedFilesInfo,
        resetSelection,
        getFileStats
      } = useFileManager(uploadedFiles);

      // 使用 SMILES 处理 composable
      const {
        parsedMolecules,
        selectedMolecule,
        isProcessing,
        processingError,
        moleculeCount,
        processSmilesText,
        processCsvFile,
        selectMolecule,
        deleteMolecule,
        clearAllMolecules,
        searchMolecules,
        getMoleculeStats,
        exportMolecules
      } = useSmilesProcessor();

      // 使用表单验证 composable
      const {
        isSubmitting,
        validationErrors,
        isFormValid,
        canSubmit,
        validateForm,
        submitForm,
        resetValidation,
        getValidationSummary,
        hasFieldError
      } = useFormValidation();

      // 使用任务管理 composable
      const {
        tasks,
        currentTask,
        isTasksLoading,
        errorTaskMessage,
        resultsVisible,
        isResultsLoading,
        resultsStatusText,
        humanReadableResults,
        scores,
        resultFiles,
        detailsExpanded,
        activeResultTab,
        currentPage,
        pageSize,
        totalPages,
        paginatedScores,
        paginatedFiles,
        showTaskDetailsModal,
        selectedTask,
        loadTaskHistory,
        viewTaskDetails,
        closeTaskDetailsModal,
        showTaskResults,
        downloadFileFromUrl,
        hideResults,
        toggleDetailsView,
        updateScoresPagination,
        updateFilesPagination,
        getItemRank,
        getStatusText,
        formatDateTime,
        formatTaskParameters,
        startPeriodicRefresh,
        stopPeriodicRefresh
      } = useTaskManager();

      // 用户信息
      const username = ref('用户名');
      const loginTime = ref('登录时间：2023-04-01 12:00');
      const userInitials = ref('U');

      // 对接参数
      const pocketX = ref('');
      const pocketY = ref('');
      const pocketZ = ref('');
      const sizeX = ref('15');
      const sizeY = ref('15');
      const sizeZ = ref('15');
      const threads = ref('2000');

      // 文件相关状态
      const isLoading = ref(false);
      const fileListVisible = ref(false);
      
      // 通知相关状态
      const notifications = ref([]);
      
      // 文件选择相关状态已在 useFileManager 中管理
      
      // Tab状态控制
      // Tab 切换功能已在 useFileManager 中实现，这里添加通知功能
      const switchLigandTabWithNotification = (tabName) => {
        switchLigandTab(tabName);
        const modeNames = {
          'file': '配体文件选择',
          'smiles': 'SMILES分子输入'
        };
        showNotification(`切换至${modeNames[tabName] || tabName}模式`, 'info');
      };
      
      // 任务管理相关状态已在 useTaskManager 中管理
      
      // 模型提取相关
      const modelNumber = ref(1);
      const extractionStatus = ref('');
      
      // 文件过滤计算属性已在 useFileManager 中管理
      
      // 文件选择方法已在 useFileManager 中管理，这里添加通知功能
      const selectProteinFileWithNotification = (fileId) => {
        selectProteinFile(fileId);
        const fileName = uploadedFiles.value.find(f => f.id === fileId)?.name;
        showNotification(`已选择蛋白质文件: ${fileName}`, 'success');
      };

      const toggleLigandFileSelectionWithNotification = (fileId) => {
        const wasSelected = selectedLigandFiles.value.includes(fileId);
        toggleLigandFileSelection(fileId);

        if (wasSelected) {
          showNotification('已从选择列表中移除配体分子文件', 'info');
        } else {
          showNotification('已添加配体分子文件到选择列表', 'success');
        }
      };
      
      // 计算并设置几何中心坐标
      const calculateAndSetGeometricCenter = () => {
        if (selectedAtoms.value.length === 0) {
          showNotification('请先选择原子', 'warning');
          return;
        }

        const center = calculateGeometricCenter();
        pocketX.value = center.x;
        pocketY.value = center.y;
        pocketZ.value = center.z;

        showNotification(`已设置口袋中心坐标: (${center.x}, ${center.y}, ${center.z})`, 'success');
      };
      
      // 处理原子项点击事件
      const handleAtomItemClick = (atom, index) => {
        if (pocketCenterMode.value === 'single') {
          // 单个原子模式：设置口袋中心坐标
          const center = setAtomAsPocketCenter(atom);
          pocketX.value = center.x;
          pocketY.value = center.y;
          pocketZ.value = center.z;
          showNotification(`已设置口袋中心坐标为原子 ${atom.element}:${atom.resname}${atom.resno}`, 'success');
        } else if (pocketCenterMode.value === 'center') {
          // 几何中心模式：切换原子的参与状态
          toggleAtomInCenter(atom.id);
          const center = calculateGeometricCenter();
          pocketX.value = center.x;
          pocketY.value = center.y;
          pocketZ.value = center.z;
          const status = atom.includeInCenter !== false ? '排除' : '参与';
          showNotification(`原子 ${atom.element}:${atom.resname}${atom.resno} ${status} 几何中心计算`, 'info');
        }
      };

      // 从原子列表中移除原子
      const removeAtomFromList = (index) => {
        if (index >= 0 && index < selectedAtoms.value.length) {
          const atom = selectedAtoms.value[index];
          selectedAtoms.value.splice(index, 1);
          showNotification(`已移除原子 ${atom.element}:${atom.resname}${atom.resno}`, 'info');

          // 如果是几何中心模式，重新计算中心坐标
          if (pocketCenterMode.value === 'center' && selectedAtoms.value.length > 0) {
            const center = calculateGeometricCenter();
            pocketX.value = center.x;
            pocketY.value = center.y;
            pocketZ.value = center.z;
          }
        }
      };

      // 切换对接口袋中心选择模式（带通知）
      const switchPocketCenterModeWithNotification = (mode) => {
        switchPocketCenterMode(mode);
        const modeNames = {
          'single': '单个原子模式',
          'center': '几何中心模式'
        };
        showNotification(`切换至${modeNames[mode]}`, 'info');

        // 如果切换到几何中心模式，自动计算并设置坐标
        if (mode === 'center' && selectedAtoms.value.length > 0) {
          // 确保所有原子默认参与计算
          selectedAtoms.value.forEach(atom => {
            if (atom.includeInCenter === undefined) {
              atom.includeInCenter = true;
            }
          });

          const center = calculateGeometricCenter();
          pocketX.value = center.x;
          pocketY.value = center.y;
          pocketZ.value = center.z;
          showNotification(`已自动计算几何中心坐标: (${center.x}, ${center.y}, ${center.z})`, 'success');
        }
      };
      
      // 处理文件上传事件
      const handleFileUploadEvent = (event) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        handleFileUpload(Array.from(files));

        // 清空input值，允许重复上传同一文件
        event.target.value = '';
      };

      // 处理拖放上传
      const handleDragOver = (event) => {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
      };

      const handleDragLeave = (event) => {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
      };

      const handleDrop = (event) => {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        handleFileUpload(Array.from(event.dataTransfer.files));
      };

      // CSV 文件上传处理已从 useFileManager 导入

      // CSV 文件输入 ref
      const csvFileInput = ref(null);

      // 触发 CSV 文件选择
      const triggerCsvFileInput = () => {
        if (csvFileInput.value) {
          csvFileInput.value.click();
        }
      };

      // 处理 SMILES 文本解析
      const handleSmilesTextParse = () => {
        if (!smilesInput.value || !smilesInput.value.trim()) {
          showNotification('请输入 SMILES 字符串', 'warning');
          return;
        }

        const result = processSmilesText(smilesInput.value);
        if (result.success) {
          showNotification(result.message, 'success');
          // 清空输入
          smilesInput.value = '';
        } else {
          showNotification(result.message, 'error');
        }
      };
      
      // 通知消息显示
      const showNotification = (message, type = 'info') => {
        // 创建新通知
        const notification = {
          id: Date.now(),
          message,
          type,
        };
        
        // 添加到通知列表
        notifications.value.push(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
          // 找到要移除的通知的索引
          const index = notifications.value.findIndex(n => n.id === notification.id);
          if (index !== -1) {
            notifications.value.splice(index, 1);
          }
        }, 3000);
      };
      
      const validateInputs = () => {
        // 准备验证数据
        const formData = {
          parameters: {
            pocketX: pocketX.value,
            pocketY: pocketY.value,
            pocketZ: pocketZ.value,
            sizeX: sizeX.value,
            sizeY: sizeY.value,
            sizeZ: sizeZ.value,
            threads: threads.value
          },
          files: getSelectedFilesInfo()
        };

        // 执行验证
        const validation = validateForm(formData);

        // 显示验证结果
        if (validation.isValid) {
          showNotification('输入验证通过！', 'success');
        } else {
          showNotification('验证失败：' + validation.errors.join('；'), 'error');
        }

        return validation.isValid;
      };
      
      const submitFormNew = async () => {
        try {
          // 准备表单数据
          const formData = {
            parameters: {
              pocketX: pocketX.value,
              pocketY: pocketY.value,
              pocketZ: pocketZ.value,
              sizeX: sizeX.value,
              sizeY: sizeY.value,
              sizeZ: sizeZ.value,
              threads: threads.value
            },
            files: getSelectedFilesInfo()
          };

          // 检查是否是批量模式，显示确认对话框
          const ligandCount = formData.files.ligands.length;
          if (ligandCount > 1) {
            const confirmMessage = `您即将开始批量分子对接：\n\n` +
              `• 分子数量：${ligandCount} 个\n` +
              `• 对接模式：${formData.files.mode === 'smiles' ? 'SMILES批量' : '多文件'}\n` +
              `• 所有分子将同时进行对接\n\n` +
              `确定要继续吗？`;

            if (!confirm(confirmMessage)) {
              return;
            }
          }

          // 使用新的提交方法
          const result = await submitForm(formData);

          if (result.success) {
            showNotification(`对接任务已提交！任务ID: ${result.data.task_id}`, 'success');

            // 刷新任务列表
            if (typeof loadTaskHistory === 'function') {
              loadTaskHistory();
            }

            // 重置表单
            resetForm();
          } else {
            throw new Error(result.message);
          }
        } catch (error) {
          console.error('提交表单时出错:', error);

          // 检查是否是认证问题
          if (error.message.includes('401') || error.message.includes('认证') || error.message.includes('登录')) {
            showNotification('登录状态已过期，请重新登录', 'warning');
            setTimeout(() => {
              router.push('/login');
            }, 2000);
          } else {
            showNotification('提交失败: ' + error.message, 'error');
          }
        }
      };



      // 获取当前激活的ligand标签页
      const getActiveLigandTab = () => {
        return ligandInputMode.value;
      };

      // 准备提交数据
      const prepareData = () => {
        // 创建一个新的数据对象
        const data = {
          job_name: `AutoDock_${Date.now()}`,
          center_x: parseFloat(pocketX.value),
          center_y: parseFloat(pocketY.value),
          center_z: parseFloat(pocketZ.value),
          size_x: parseFloat(sizeX.value),
          size_y: parseFloat(sizeY.value),
          size_z: parseFloat(sizeZ.value),
          thread: parseInt(threads.value),
          exhaustiveness: 8, // 默认值
          num_modes: 9,      // 默认值
          energy_range: 3    // 默认值
        };
        
        // 获取当前激活的Tab
        const activeTab = getActiveLigandTab();
        
        // 添加蛋白质文件
        if (selectedProteinFile.value) {
          const proteinFile = uploadedFiles.value.find(file => file.id === selectedProteinFile.value);
          if (proteinFile) {
            data.receptor = {
              name: proteinFile.name,
              content: proteinFile.content,
              type: proteinFile.type
            };
          } else {
            showNotification('无法找到选中的蛋白质文件', 'error');
            return null;
          }
        } else {
          showNotification('请选择蛋白质文件', 'error');
          return null;
        }
        
        // 根据激活的Tab处理配体数据
        const currentMode = getActiveLigandTab();
        if (currentMode === 'file') {
          if (selectedLigandFiles.value.length === 0) {
            showNotification('请至少选择一个配体文件', 'error');
            return null;
          }

          if (selectedLigandFiles.value.length === 1) {
            // 单文件模式
            const ligandFile = uploadedFiles.value.find(file => file.id === selectedLigandFiles.value[0]);
            if (ligandFile) {
              data.ligand = {
                name: ligandFile.name,
                content: ligandFile.content,
                type: ligandFile.type
              };
            } else {
              showNotification('无法找到选中的配体文件', 'error');
              return null;
            }
          } else {
            // 多文件模式
            const ligandFiles = selectedLigandFiles.value.map(id => {
              const file = uploadedFiles.value.find(file => file.id === id);
              if (file) {
                return {
                  name: file.name,
                  content: file.content,
                  type: file.type
                };
              }
              return null;
            }).filter(Boolean);

            if (ligandFiles.length === 0) {
              showNotification('无法找到选中的配体文件', 'error');
              return null;
            }

            data.ligands = ligandFiles;
          }
        } else if (currentMode === 'smiles') {
          // SMILES模式
          if (smilesFiles.value.length > 0) {
            // 使用已解析的SMILES分子
            data.smiles = smilesFiles.value.map(molecule => molecule.content || molecule.smiles).join('\n');
          } else if (smilesInput.value.trim()) {
            // 直接使用SMILES输入框的内容
            data.smiles = smilesInput.value.trim();
          } else {
            showNotification('请提供SMILES数据', 'error');
            return null;
          }
        }
        
        return data;
      };
      
      const resetForm = () => {
        // 重置表单的逻辑
        pocketX.value = '';
        pocketY.value = '';
        pocketZ.value = '';
        sizeX.value = '15';
        sizeY.value = '15';
        sizeZ.value = '15';
        threads.value = '2000';
        
        // 重置文件选择
        selectedProteinFile.value = null;
        selectedLigandFiles.value = [];
      };
      
      // 通知相关方法
      const getNotificationStyle = (type) => {
        let background;
        
        switch (type) {
          case 'success':
            background = '#10b981';
            break;
          case 'error':
            background = '#ef4444';
            break;
          case 'warning':
            background = '#f59e0b';
            break;
          default:
            background = '#3b82f6';
        }
        
        return {
          position: 'fixed',
          top: '20px',
          right: '20px',
          padding: '1rem 1.5rem',
          borderRadius: '8px',
          color: 'white',
          fontWeight: '500',
          zIndex: '1000',
          maxWidth: '400px',
          wordWrap: 'break-word',
          transition: 'all 0.3s ease',
          background
        };
      };
      
      // 状态和日期格式化方法已在 useTaskManager 中实现
      
      // 格式化任务参数已在 useTaskManager 中实现
      
      // 任务管理方法已在 useTaskManager 中实现
      
      // 任务结果显示方法已在 useTaskManager 中实现
      
      // 显示结果
      const displayResults = (resultData, taskId = null) => {
        // 更新状态为完成
        resultsStatusText.value = '对接计算已完成';
        
        // 更新任务信息
        const actualTaskId = resultData.task_id || taskId;
        if (!currentTask.value) {
          currentTask.value = {
            task_id: actualTaskId || 'N/A',
            status: 'completed',
            completed_at: new Date().toISOString()
          };
        }
        
        // 处理人类可读结果
        let readableResults = null;
        if (resultData.results && resultData.results.human_readable_results) {
          readableResults = resultData.results.human_readable_results;
        } else if (resultData.human_readable_results) {
          readableResults = resultData.human_readable_results;
        }
        
        if (readableResults) {
          humanReadableResults.value = `<pre>${filterHumanReadableResults(readableResults, resultData)}</pre>`;
        } else {
          humanReadableResults.value = '<p>暂无详细结果信息</p>';
        }
        
        // 处理分数数据 - 处理为符合要求的格式
        scores.value = [];
        let scoresData = [];
        if (resultData.scores) {
          scoresData = resultData.scores;
        } else if (resultData.results && resultData.results.scores) {
          scoresData = resultData.results.scores;
        }
        
        // 将分数数据转换为所需格式
        if (Array.isArray(scoresData) && scoresData.length > 0) {
          // 假设返回的是单个分子的多个构象分数
          scores.value = [{
            filename: '分子构象',
            scoresArray: scoresData.map(s => typeof s.affinity === 'number' ? s.affinity : parseFloat(s.affinity))
          }];
        } else if (typeof scoresData === 'object' && scoresData !== null) {
          // 假设返回的是多个分子的分数数据
          scores.value = Object.entries(scoresData).map(([filename, data]) => {
            let scoresArray;
            if (Array.isArray(data)) {
              scoresArray = data.map(s => typeof s === 'number' ? s : parseFloat(s));
            } else if (typeof data === 'object' && data !== null && Array.isArray(data.affinity)) {
              scoresArray = data.affinity.map(a => parseFloat(a));
            } else {
              scoresArray = [parseFloat(data)];
            }
            return { filename, scoresArray };
          });
        }
        
        // 确保页面大小和当前页码已设置
        currentPage.value = 1;
        pageSize.value = 5;
        
        // 分页
        updateScoresPagination(1);
        
        // 处理文件数据
        resultFiles.value = [];
        let files = null;
        
        if (resultData.pdbqt_files) {
          files = resultData.pdbqt_files;
        } else if (resultData.results && resultData.results.pdbqt_files) {
          files = resultData.results.pdbqt_files;
        }
        
        if (files) {
          resultFiles.value = Object.entries(files).map(([name, content], index) => {
            // 假设文件名是分子名称
            let urls = [];
            if (typeof content === 'string') {
              // 单个文件
              urls = [content];
            } else if (Array.isArray(content)) {
              // 多个构象文件
              urls = content;
            }
            
            return {
              filename: name,
              urlsArray: urls,
              sortedIndex: index,
              originalMoleculeIndex: index
            };
          });
        }
        
        // 分页
        updateFilesPagination(1);
      };
      
      // 分页相关方法已在 useTaskManager 中实现
      
      // 文件下载功能已在 useTaskManager 中实现
      
      // 处理人类可读结果
      const filterHumanReadableResults = (humanReadableResults, resultData) => {
        try {
          if (typeof humanReadableResults === 'string') {
            return humanReadableResults;
          }
          
          if (typeof humanReadableResults === 'object') {
            if (humanReadableResults.summary) {
              return humanReadableResults.summary;
            }
            return '对接计算已完成';
          }
          
          return '对接计算已完成';
        } catch (error) {
          console.error('过滤人类可读结果时出错:', error);
          return '对接计算已完成';
        }
      };
      
      // 结果显示控制方法已在 useTaskManager 中实现
      
      // 下载文件
      const downloadFile = (file) => {
        try {
          const blob = new Blob([file.content], { type: 'text/plain' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = file.name;
          document.body.appendChild(a);
          a.click();
          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }, 100);
        } catch (error) {
          console.error('下载文件时出错:', error);
          showNotification('下载文件失败', 'error');
        }
      };
      
      // 处理PDBQT文件上传
      const handlePdbqtFileUpload = (event) => {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target.result;
          extractionStatus.value = `已加载文件: ${file.name}`;
        };
        reader.readAsText(file);
      };
      
      // 从PDBQT提取模型
      const extractModelFromPdbqt = () => {
        // 在实际开发中实现模型提取功能
        extractionStatus.value = '模型提取功能正在开发中...';
        setTimeout(() => {
          extractionStatus.value = '已成功提取模型!';
        }, 1500);
      };
      
      // refs for DOM elements (csvFileInput 已在前面定义)
      const batchSmilesText = ref(null);
      const localLigandInput = ref(null);
      const localProteinInput = ref(null);
      const batchContainer = ref(null);
      // moleculeCount 已从 useSmilesProcessor 导入
      const moleculesList = ref(null);
      const structure2d = ref(null);
      const moleculeInfo = ref(null);
      const selectedMoleculeIndex = ref(-1);
      
      // CSV 文件输入触发功能已在前面定义
      
      // 触发配体文件输入点击
      const triggerLigandFileInput = () => {
        if (localLigandInput.value) {
          localLigandInput.value.click();
        }
      };
      
      // 触发蛋白质文件输入点击
      const triggerProteinFileInput = () => {
        if (localProteinInput.value) {
          localProteinInput.value.click();
        }
      };
      
      // 处理本地配体文件上传
      const handleLocalLigandUpload = (event) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;
        
        // 使用已有的文件上传逻辑
        handleFileUpload(event);
      };
      
      // 处理本地蛋白质文件上传
      const handleLocalProteinUpload = (event) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;
        
        // 使用已有的文件上传逻辑
        handleFileUpload(event);
      };
      
      // 处理蛋白质文件拖放
      const handleProteinDrop = (event) => {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        // 过滤只接受PDB或PDBQT文件
        const files = Array.from(event.dataTransfer.files).filter(file => {
          const fileType = getFileType(file.name);
          return fileType === 'pdb' || fileType === 'pdbqt';
        });
        
        if (files.length === 0) {
          showNotification('请上传PDB或PDBQT格式的蛋白质文件', 'warning');
          return;
        }
        
        // 创建一个新的文件列表来模拟文件输入事件
        const dataTransfer = new DataTransfer();
        files.forEach(file => dataTransfer.items.add(file));
        
        handleFileUpload({ target: { files: dataTransfer.files } });
      };
      
      // 组件挂载后初始化
      onMounted(async () => {
        console.log('正在初始化分子查看器...');

        try {
          // 显示加载指示器
          loadingVisible.value = true;

          // 加载用户信息
          const storedUsername = localStorage.getItem('username');
          if (storedUsername) {
            username.value = storedUsername;
            userInitials.value = storedUsername.charAt(0).toUpperCase();
          }

          // 确保DOM已更新
          await new Promise(resolve => setTimeout(resolve, 100));

          // 初始化分子查看器（包含JS库加载）
          const viewerInitialized = await initializeViewer();

          if (viewerInitialized) {
            console.log('分子查看器初始化完成');
            showNotification('分子查看器已准备就绪', 'success');
          } else {
            console.error('分子查看器初始化失败');
            showNotification('分子查看器初始化失败，请刷新页面重试', 'error');
          }

          // 加载任务历史
          loadTaskHistory();

          // 隐藏加载指示器
          loadingVisible.value = false;
        } catch (err) {
          console.error('初始化分子查看器时出错:', err);
          errorMessage.value = '初始化分子查看器时出错: ' + err.message;
          loadingVisible.value = false;
          showNotification('加载3D查看器时出错: ' + err.message, 'error');
        }
      });
      
      // CSV 文件上传处理已在 useFileManager 和 useSmilesProcessor 中实现
      
      // SMILES 文本解析已在前面定义
      
      // 显示解析的分子
      const displayParsedMolecules = (molecules) => {
        // 保存解析的分子
        parsedMolecules.value = molecules;
        
        // 更新分子计数
        moleculeCount.value = molecules.length;
        
        // 这部分仍需DOM操作，因为它涉及到第三方库SmilesDrawer
        // 在实际项目中，应考虑将这部分封装为自定义组件
        nextTick(() => {
          // 显示分子列表容器
          const container = document.querySelector('.batch-molecules-container');
          if (container) {
            container.style.display = 'flex';
          }
          
          // 更新分子列表
          const list = document.getElementById('molecules-list');
          if (list) {
            list.innerHTML = '';
            
            molecules.forEach((molecule, index) => {
              const moleculeItem = document.createElement('div');
              moleculeItem.className = 'molecule-item';
              moleculeItem.innerHTML = `
                <div class="molecule-name">${molecule.name}</div>
                <div class="molecule-smiles" title="${molecule.smiles}">${molecule.smiles.length > 20 ? molecule.smiles.substring(0, 20) + '...' : molecule.smiles}</div>
              `;
              
              // 点击显示2D结构
              moleculeItem.addEventListener('click', () => {
                try {
                  selectedMoleculeIndex.value = index;
                  
                  // 使用SmilesDrawer渲染2D结构
                  const canvas = document.getElementById('structure2d');
                  const infoElement = document.getElementById('molecule-info');
                  
                  if (canvas && infoElement && typeof SmilesDrawer !== 'undefined') {
                    // 创建2D绘图器
                    const drawer = new SmilesDrawer.Drawer({ width: 300, height: 200 });
                    
                    // 解析SMILES字符串
                    SmilesDrawer.parse(molecule.smiles, function(tree) {
                      drawer.draw(tree, canvas, 'light', false);
                      
                      // 更新分子信息
                      infoElement.innerHTML = `
                        <div><strong>名称:</strong> ${molecule.name}</div>
                        <div><strong>SMILES:</strong> ${molecule.smiles}</div>
                        <div><strong>序号:</strong> #${index + 1}</div>
                      `;
                    });
                  }
                } catch (error) {
                  console.error('渲染分子2D结构时出错:', error);
                  showNotification('无法渲染分子结构', 'error');
                }
              });
              
              list.appendChild(moleculeItem);
            });
          }
        });
      };
      
      // 排名计算辅助函数已在 useTaskManager 中实现
      
      // SMILES输入文本已从 useFileManager 导入
      
      return {
        // 基本信息
        username,
        loginTime,
        userInitials,

        // 对接参数
        pocketX,
        pocketY,
        pocketZ,
        sizeX,
        sizeY,
        sizeZ,
        threads,

        // 文件管理
        uploadedFiles,
        isLoading,
        errorMessage,
        fileListVisible,
        selectedAtoms,

        // 通知系统
        notifications,

        // 事件处理
        handleFileUploadEvent,
        handleDragOver,
        handleDragLeave,
        handleDrop,
        handleProteinDrop,
        deleteFile,
        getFileTypeIcon,
        formatFileSize,
        calculateAndSetGeometricCenter,
        clearSelectedAtoms,
        handleAtomItemClick,
        removeAtomFromList,
        switchPocketCenterModeWithNotification,

        // 表单验证
        validateInputs,
        submitFormNew,
        isSubmitting,
        validationErrors,
        isFormValid,
        canSubmit,
        resetForm,

        // UI 状态
        loadingVisible,
        getNotificationStyle,
        viewerContainer,

        // 文件选择
        selectedProteinFile,
        selectedLigandFiles,
        filteredProteinFiles,
        filteredLigandFiles,
        selectProteinFileWithNotification,
        toggleLigandFileSelectionWithNotification,
        switchLigandTabWithNotification,

        // SMILES 处理
        parsedMolecules,
        selectedMolecule,
        isProcessing,
        processingError,
        moleculeCount,
        handleCsvUpload,
        csvFileInput,
        triggerCsvFileInput,
        handleSmilesTextParse,
        selectMolecule,
        deleteMolecule,
        clearAllMolecules,

        // 任务管理
        tasks,
        isTasksLoading,
        errorTaskMessage,
        loadTaskHistory,
        getStatusText,
        formatDateTime,
        formatTaskParameters,
        viewTaskDetails,
        showTaskDetailsModal,
        selectedTask,
        closeTaskDetailsModal,

        // 结果显示
        resultsVisible,
        isResultsLoading,
        resultsStatusText,
        currentTask,
        humanReadableResults,
        scores,
        resultFiles,
        showTaskResults,
        hideResults,
        detailsExpanded,
        toggleDetailsView,
        activeResultTab,
        downloadFile,

        // 分页
        currentPage,
        pageSize,
        totalPages,
        paginatedScores,
        paginatedFiles,
        updateScoresPagination,
        updateFilesPagination,
        getItemRank,

        // 模型提取
        modelNumber,
        extractionStatus,
        handlePdbqtFileUpload,
        extractModelFromPdbqt,

        // DOM 引用
        batchSmilesText,
        localLigandInput,
        localProteinInput,
        triggerLigandFileInput,
        triggerProteinFileInput,
        handleLocalLigandUpload,
        handleLocalProteinUpload,
        ligandInputMode,
        moleculesList,
        structure2d,
        moleculeInfo,
        selectedMoleculeIndex,
        smilesInput
      };
    }
  };
</script>

<style scoped>
  /* AutoDock 组件专用样式 - 只保留组件特有的样式 */

  /* 组件特有的网格布局 */
  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  /* 组件内部特定的文件卡片布局调整 */
  .file-card {
    min-height: 90px;
  }

  /* 组件特有的分子项目布局 */
  .molecule-item {
    min-height: 60px;
  }

  /* 组件特有的面板布局调整 */
  .batch-molecules-container {
    margin-top: 1rem;
  }

  /* 组件特有的响应式调整 */
  @media (max-width: 768px) {
    .files-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
  }

  @media (max-width: 480px) {
    .files-grid {
      grid-template-columns: 1fr;
    }

    .file-card {
      min-height: 70px;
    }
  }
</style>
