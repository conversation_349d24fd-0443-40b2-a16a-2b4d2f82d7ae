/**
 * Base Styles
 * 基础样式文件
 * 
 * 包含: 重置样式、基础排版、通用工具类
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

/* ===== CSS Reset ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ===== 基础排版 ===== */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    font-size: 14px;
}

/* ===== 链接样式 ===== */
a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: #764ba2;
}

/* ===== 按钮基础样式 ===== */
button {
    font-family: inherit;
    font-size: inherit;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

button:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* ===== 表单元素 ===== */
input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: 1px solid #e1e5e9;
    border-radius: 0.375rem;
    padding: 0.5rem;
    transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* ===== 标题样式 ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

/* ===== 段落和文本 ===== */
p {
    margin-bottom: 1rem;
}

/* ===== 列表样式 ===== */
ul, ol {
    list-style: none;
}

/* ===== 图片样式 ===== */
img {
    max-width: 100%;
    height: auto;
}

/* ===== 工具类 ===== */
.u-hidden {
    display: none !important;
}

.u-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.u-text-center {
    text-align: center !important;
}

.u-text-left {
    text-align: left !important;
}

.u-text-right {
    text-align: right !important;
}

.u-mb-0 { margin-bottom: 0 !important; }
.u-mb-1 { margin-bottom: 0.5rem !important; }
.u-mb-2 { margin-bottom: 1rem !important; }
.u-mb-3 { margin-bottom: 1.5rem !important; }
.u-mb-4 { margin-bottom: 2rem !important; }

.u-mt-0 { margin-top: 0 !important; }
.u-mt-1 { margin-top: 0.5rem !important; }
.u-mt-2 { margin-top: 1rem !important; }
.u-mt-3 { margin-top: 1.5rem !important; }
.u-mt-4 { margin-top: 2rem !important; }

/* ===== 状态类 ===== */
.is-loading {
    opacity: 0.6;
    pointer-events: none;
}

.is-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.is-active {
    /* 由具体组件定义 */
}

/* ===== 响应式断点 ===== */
/* 移动端优先的响应式设计 */
@media (min-width: 576px) {
    /* 小屏幕 */
}

@media (min-width: 768px) {
    /* 中等屏幕 */
}

@media (min-width: 992px) {
    /* 大屏幕 */
}

@media (min-width: 1200px) {
    /* 超大屏幕 */
}

/* ===== 打印样式 ===== */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    .u-print-hidden {
        display: none !important;
    }
}
