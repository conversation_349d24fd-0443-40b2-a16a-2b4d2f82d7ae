// 全局变量
let batchMolecules = [];
let refreshInterval = null;

// 全局变量用于存储更新函数引用
let globalUpdateUploadedFilesGrid = null;
let globalUpdateProteinFilesGrid = null;



// 初始化分子查看器
document.addEventListener('DOMContentLoaded', async function() {
    console.log('AutoDock: DOM loaded, initializing...');
    
    // 检查登录状态并显示用户信息
    const isLoggedIn = await checkLoginStatus();
    if (!isLoggedIn) {
        return; // 如果未登录，checkLoginStatus会处理跳转
    }
    
    // 设置文件上传监听器
    setupFileUploadListener();
    
    // 初始化标签页切换功能
    initializeTabs();
    
    // 绑定按钮事件
    initializeButtonEvents();
    
    // 加载任务历史
    loadTaskHistory();
    
    // 确保show.js已加载并初始化分子查看器
    if (typeof window.MoleculeViewer !== 'undefined' && window.MoleculeViewer.initialize) {
        try {
            window.MoleculeViewer.initialize();
            console.log('AutoDock: Molecule viewer initialized successfully');
        } catch (error) {
            console.error('AutoDock: Error initializing molecule viewer:', error);
        }
    } else {
        console.warn('AutoDock: MoleculeViewer not found, retrying in 1 second...');
        setTimeout(function() {
            if (typeof window.MoleculeViewer !== 'undefined' && window.MoleculeViewer.initialize) {
                try {
                    window.MoleculeViewer.initialize();
                    console.log('AutoDock: Molecule viewer initialized successfully (retry)');
                } catch (error) {
                    console.error('AutoDock: Error initializing molecule viewer (retry):', error);
                }
            } else {
                console.error('AutoDock: MoleculeViewer still not available after retry');
            }
        }, 1000);
    }
});

// 初始化标签页切换功能
function initializeTabs() {
    console.log('AutoDock: Initializing tabs...');
    
    const ligandTabs = document.querySelectorAll('.ligand-tab');
    const ligandTabContents = document.querySelectorAll('.ligand-tab-content');
    
    ligandTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            console.log('AutoDock: Tab clicked:', this.getAttribute('data-tab'));
            
            // 移除所有标签页的活动状态
            ligandTabs.forEach(t => t.classList.remove('active'));
            ligandTabContents.forEach(content => content.classList.remove('active'));
            
            // 激活当前标签页
            this.classList.add('active');
            
            // 显示对应的内容
            const tabType = this.getAttribute('data-tab');
            const tabContent = document.getElementById(`${tabType}-tab-content`);
            if (tabContent) {
                tabContent.classList.add('active');
                console.log('AutoDock: Activated tab content:', tabType);
            }
        });
    });
    
    // 初始化结果区域的标签页切换
    const resultTabs = document.querySelectorAll('.tab-btn');
    const resultTabContents = document.querySelectorAll('.tab-content');
    
    resultTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            console.log('AutoDock: Result tab clicked:', this.getAttribute('data-tab'));
            
            // 移除所有结果标签页的活动状态
            resultTabs.forEach(t => t.classList.remove('active'));
            resultTabContents.forEach(content => content.classList.remove('active'));
            
            // 激活当前标签页
            this.classList.add('active');
            
            // 显示对应的内容
            const tabType = this.getAttribute('data-tab');
            const tabContent = document.getElementById(`${tabType}-tab`);
            if (tabContent) {
                tabContent.classList.add('active');
                console.log('AutoDock: Activated result tab content:', tabType);
            }
        });
    });
    
    // 初始化多文件选择功能
    initializeMultiFileSelection();
    
    // 初始化蛋白质文件选择功能
    initializeProteinFileSelection();
    
    console.log('AutoDock: Tabs initialized successfully');
}


// 初始化多文件选择功能
function initializeMultiFileSelection() {
    console.log('AutoDock: Initializing new file selection interface...');
    
    // 获取界面元素
    const uploadedFilesGrid = document.getElementById('uploaded-files-grid');
    const localLigandInput = document.getElementById('local-ligand-input');
    const ligandDropZone = document.getElementById('ligand-drop-zone');
    const selectedFilesDisplay = document.getElementById('selected-files-display');
    const selectedFilesList = document.getElementById('selected-files-list');
    const selectedFilesCount = document.getElementById('selected-files-count');
    const clearSelectionBtn = document.getElementById('clear-selection-btn');
    
    // 选中的文件数组
    let selectedFiles = [];
    
    // 更新已上传文件网格
    function updateUploadedFilesGrid() {
        if (!uploadedFilesGrid) return;
        
        if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
            // 过滤出小分子文件
            const ligandFiles = uploadedFilesData.filter(file => 
                ['sdf', 'pdb', 'pdbqt', 'mol2'].includes(file.type.toLowerCase())
            );
            
            if (ligandFiles.length > 0) {
                uploadedFilesGrid.innerHTML = '';
                ligandFiles.forEach(file => {
                    const fileCard = document.createElement('div');
                    fileCard.className = 'file-card';
                    fileCard.dataset.fileId = file.id;
                    fileCard.innerHTML = `
                        <div class="file-card-header">
                            <span class="file-type-icon">${getFileTypeIcon(file.type)}</span>
                            <span class="file-name" title="${file.name}">${file.name}</span>
                        </div>
                        <div class="file-info">${file.type.toUpperCase()} • ${formatFileSize(file.size || 0)}</div>
                        <div class="selection-indicator">✓</div>
                    `;
                    
                    // 添加点击事件
                    fileCard.addEventListener('click', function() {
                        toggleFileSelection(file, fileCard);
                    });
                    
                    uploadedFilesGrid.appendChild(fileCard);
                });
            } else {
                uploadedFilesGrid.innerHTML = '<div class="no-files-message">暂无已上传的分子文件，请先在上方分子查看器中上传文件</div>';
            }
        } else {
            uploadedFilesGrid.innerHTML = '<div class="no-files-message">暂无已上传的分子文件，请先在上方分子查看器中上传文件</div>';
        }
    }
    
    // 将函数引用存储到全局变量
    globalUpdateUploadedFilesGrid = updateUploadedFilesGrid;
    
    // 切换文件选择状态
    function toggleFileSelection(file, fileCard) {
        const existingIndex = selectedFiles.findIndex(f => f.id === file.id);
        
        if (existingIndex >= 0) {
            // 取消选择
            selectedFiles.splice(existingIndex, 1);
            fileCard.classList.remove('selected');
        } else {
            // 选择文件
            selectedFiles.push({
                id: file.id,
                name: file.name,
                type: file.type,
                content: file.content,
                source: 'uploaded'
            });
            fileCard.classList.add('selected');
        }
        
        updateSelectedFilesDisplay();
    }
    
    // 本地文件上传处理
    if (localLigandInput && ligandDropZone) {
        // 点击上传区域
        ligandDropZone.addEventListener('click', function() {
            localLigandInput.click();
        });
        
        // 文件选择处理
        localLigandInput.addEventListener('change', function(e) {
            handleLocalFileUpload(e.target.files);
        });
        
        // 拖拽上传处理
        ligandDropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        ligandDropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        ligandDropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            handleLocalFileUpload(e.dataTransfer.files);
        });
    }
    
    // 处理本地文件上传
    function handleLocalFileUpload(files) {
        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const fileData = {
                    id: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                    name: file.name,
                    type: getFileType(file.name),
                    content: e.target.result,
                    source: 'local'
                };
                
                // 检查是否已经选择了这个文件
                const exists = selectedFiles.some(f => f.name === file.name && f.source === 'local');
                if (!exists) {
                    selectedFiles.push(fileData);
                    updateSelectedFilesDisplay();
                    showNotification(`已添加文件: ${file.name}`, 'success');
                } else {
                    showNotification(`文件 ${file.name} 已经存在`, 'warning');
                }
            };
            reader.readAsText(file);
        });
        
        // 清空input
        localLigandInput.value = '';
    }
    
    // 更新已选文件显示
    function updateSelectedFilesDisplay() {
        if (!selectedFilesDisplay || !selectedFilesList || !selectedFilesCount) return;
        
        if (selectedFiles.length > 0) {
            selectedFilesDisplay.style.display = 'block';
            selectedFilesCount.textContent = selectedFiles.length;
            
            selectedFilesList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'selected-file-item';
                fileItem.innerHTML = `
                    <span class="file-name">${file.name} (${file.type.toUpperCase()}) ${file.source === 'local' ? '- 本地' : ''}</span>
                    <button class="remove-file-btn" data-index="${index}">×</button>
                `;
                selectedFilesList.appendChild(fileItem);
            });
            
            // 添加删除按钮事件
            selectedFilesList.querySelectorAll('.remove-file-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    const removedFile = selectedFiles[index];
                    
                    // 从选中列表中移除
                    selectedFiles.splice(index, 1);
                    
                    // 如果是已上传文件，更新网格显示
                    if (removedFile.source === 'uploaded') {
                        const fileCard = uploadedFilesGrid.querySelector(`[data-file-id="${removedFile.id}"]`);
                        if (fileCard) {
                            fileCard.classList.remove('selected');
                        }
                    }
                    
                    updateSelectedFilesDisplay();
                });
            });
        } else {
            selectedFilesDisplay.style.display = 'none';
        }
    }
    
    // 清空选择按钮
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', function() {
            selectedFiles = [];
            
            // 清除网格中的选中状态
            uploadedFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                card.classList.remove('selected');
            });
            
            updateSelectedFilesDisplay();
        });
    }
    
    // 初始化时更新文件网格
    updateUploadedFilesGrid();
    
    // 监听文件上传更新
    const originalUpdateFileSelectors = window.updateFileSelectors;
    window.updateFileSelectors = function() {
        if (originalUpdateFileSelectors) {
            originalUpdateFileSelectors.call(this);
        }
        updateUploadedFilesGrid();
    };
    
    // 提供全局访问接口
    window.getSelectedLigandFiles = function() {
        return selectedFiles;
    };
    
    console.log('AutoDock: New file selection interface initialized');
}

// 获取文件类型图标
function getFileTypeIcon(type) {
    const icons = {
        'pdb': '🧬',
        'sdf': '💊',
        'pdbqt': '⚗️',
        'mol2': '🔬'
    };
    return icons[type.toLowerCase()] || '📄';
}

// 获取文件类型
function getFileType(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    return ext;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 检查登录状态
async function checkLoginStatus() {
    console.log('AutoDock: Checking login status...');
    
    try {
        // 首先检查后端认证状态
        const response = await fetch('/api/auth/check', {
            method: 'GET',
            credentials: 'include'
        });
        
        const data = await response.json();
        console.log('AutoDock: Backend auth check result:', data);
        
        if (data.logged_in) {
            // 后端确认已登录，更新localStorage
            localStorage.setItem('isLoggedIn', 'true');
            if (data.user && data.user.username) {
                localStorage.setItem('username', data.user.username);
            }
            if (data.user && data.user.loginTime) {
                localStorage.setItem('loginTime', data.user.loginTime);
            }
            
            // 显示用户信息
            const username = data.user?.username || localStorage.getItem('username');
            const loginTime = data.user?.loginTime || localStorage.getItem('loginTime');
            
            if (username) {
                document.getElementById('username').textContent = username;
                document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
            }
            
            if (loginTime) {
                const loginDate = new Date(loginTime);
                const now = new Date();
                const diffMinutes = Math.floor((now - loginDate) / (1000 * 60));
                
                let timeText;
                if (diffMinutes < 1) {
                    timeText = '刚刚登录';
                } else if (diffMinutes < 60) {
                    timeText = `${diffMinutes}分钟前登录`;
                } else {
                    const diffHours = Math.floor(diffMinutes / 60);
                    timeText = `${diffHours}小时前登录`;
                }
                
                document.getElementById('loginTime').textContent = timeText;
            }
            
            console.log('AutoDock: Login status verified, user logged in');
            return true;
        } else {
            // 后端确认未登录，清除localStorage并跳转
            console.log('AutoDock: Backend confirms user not logged in:', data.message);
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('loginTime');
            window.location.href = 'login';
            return false;
        }
    } catch (error) {
        console.error('AutoDock: Error checking login status:', error);
        
        // 网络错误时，回退到localStorage检查
        const isLoggedIn = localStorage.getItem('isLoggedIn');
        if (isLoggedIn !== 'true') {
            console.log('AutoDock: Fallback - localStorage indicates not logged in');
            window.location.href = 'login';
            return false;
        }
        
        // 显示localStorage中的用户信息
        const username = localStorage.getItem('username');
        const loginTime = localStorage.getItem('loginTime');
        
        if (username) {
            document.getElementById('username').textContent = username;
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
        }
        
        if (loginTime) {
            const loginDate = new Date(loginTime);
            const now = new Date();
            const diffMinutes = Math.floor((now - loginDate) / (1000 * 60));
            
            let timeText;
            if (diffMinutes < 1) {
                timeText = '刚刚登录';
            } else if (diffMinutes < 60) {
                timeText = `${diffMinutes}分钟前登录`;
            } else {
                const diffHours = Math.floor(diffMinutes / 60);
                timeText = `${diffHours}小时前登录`;
            }
            
            document.getElementById('loginTime').textContent = timeText;
        }
        
        console.log('AutoDock: Fallback - using localStorage, assuming logged in');
        return true;
    }
}

// 退出登录
function logout() {
    // 清除定期刷新
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
    
    fetch('/api/logout', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Server session ended');
            } else {
                console.error('Server logout failed:', data.message);
            }
        })
        .catch(error => {
            console.error('Error during server logout:', error);
        })
        .finally(() => {
            // Always clear client-side storage and redirect
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('loginTime');
            window.location.href = 'login';
        });
}

// 更新文件选择下拉框
function updateFileSelectors() {
    console.log('AutoDock: Updating file selectors');
    console.log('AutoDock: uploadedFilesData available:', typeof uploadedFilesData !== 'undefined');
    console.log('AutoDock: uploadedFilesData length:', typeof uploadedFilesData !== 'undefined' ? uploadedFilesData.length : 'N/A');
    if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
        console.log('AutoDock: uploadedFilesData contents:', uploadedFilesData);
    }
    
    const proteinSelect = document.getElementById('protein-select');
    const ligandSelect = document.getElementById('ligand-select');
    
    if (!proteinSelect || !ligandSelect) {
        console.log('AutoDock: File selectors not found, will retry later');
        return;
    }

    // 清空现有选项（保留默认选项）
    proteinSelect.innerHTML = '<option value="">请选择蛋白质文件...</option>';
    ligandSelect.innerHTML = '<option value="">请选择小分子文件...</option>';

    // 检查是否有全局的文件数据
    if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
        console.log('AutoDock: Found uploaded files:', uploadedFilesData.length);
        
        uploadedFilesData.forEach((fileData, index) => {
            console.log(`AutoDock: Processing file ${index + 1}:`, {
                id: fileData.id,
                name: fileData.name,
                type: fileData.type
            });
            
            const proteinOption = document.createElement('option');
            proteinOption.value = fileData.id;
            proteinOption.textContent = `${fileData.name} (${fileData.type.toUpperCase()})`;
            
            const ligandOption = document.createElement('option');
            ligandOption.value = fileData.id;
            ligandOption.textContent = `${fileData.name} (${fileData.type.toUpperCase()})`;
            
            // 根据文件类型添加到对应的选择框
            if (fileData.type === 'pdb') {
                // PDB文件既可以作为蛋白质，也可以作为小分子
                proteinSelect.appendChild(proteinOption);
                ligandSelect.appendChild(ligandOption);
                console.log(`AutoDock: Added PDB file to both selectors: ${fileData.name}`);
            } else if (fileData.type === 'sdf' || fileData.type === 'mol2') {
                ligandSelect.appendChild(ligandOption);
                console.log(`AutoDock: Added ${fileData.type.toUpperCase()} file to ligand selector: ${fileData.name}`);
            } else if (fileData.type === 'pdbqt') {
                // PDBQT文件通常是对接结果，可以作为小分子文件
                ligandSelect.appendChild(ligandOption);
                console.log(`AutoDock: Added PDBQT file to ligand selector: ${fileData.name}`);
            } else {
                console.log(`AutoDock: Skipped file with unsupported type: ${fileData.name} (${fileData.type})`);
            }
        });
        
        console.log(`AutoDock: Final protein select options count: ${proteinSelect.options.length}`);
        console.log(`AutoDock: Final ligand select options count: ${ligandSelect.options.length}`);
    } else {
        console.log('AutoDock: No uploaded files found or uploadedFilesData is undefined');
    }
}

// 监听文件上传事件
function setupFileUploadListener() {
    // 定期检查文件列表更新
    let lastFileCount = 0;
    setInterval(() => {
        if (typeof uploadedFilesData !== 'undefined') {
            if (uploadedFilesData.length !== lastFileCount) {
                console.log('AutoDock: File list changed, updating selectors');
                lastFileCount = uploadedFilesData.length;
                updateFileSelectors();
                
                // 同时更新新的文件网格界面
                if (globalUpdateUploadedFilesGrid) {
                    globalUpdateUploadedFilesGrid();
                }
                
                // 更新蛋白质文件网格
                if (globalUpdateProteinFilesGrid) {
                    globalUpdateProteinFilesGrid();
                }
            }
        }
    }, 1000); // 每秒检查一次
    
    // 立即执行一次更新
    setTimeout(() => {
        updateFileSelectors();
        if (globalUpdateUploadedFilesGrid) {
            globalUpdateUploadedFilesGrid();
        }
        if (globalUpdateProteinFilesGrid) {
            globalUpdateProteinFilesGrid();
        }
    }, 100);
}

// 获取选中的文件数据
function getSelectedFileData(fileId) {
    if (typeof uploadedFilesData !== 'undefined') {
        return uploadedFilesData.find(file => file.id === fileId);
    }
    return null;
}

// 验证输入参数
function validateInputs() {
    const pocketX = document.getElementById('pocket-x');
    const pocketY = document.getElementById('pocket-y');
    const pocketZ = document.getElementById('pocket-z');

    // 获取当前激活的tab
    const activeTab = getActiveLigandTab();
    const isBatchMode = (activeTab === 'batch-file' || activeTab === 'batch-text') && batchMolecules && batchMolecules.length > 0;

    let isValid = true;
    let errors = [];

    // 检查蛋白质文件（使用新的文件选择接口）
    const selectedProteinFile = window.getSelectedProteinFile ? window.getSelectedProteinFile() : null;
    if (!selectedProteinFile) {
        errors.push('请选择蛋白质文件');
        isValid = false;
    }

    // 根据激活的tab检查小分子输入
    switch(activeTab) {
        case 'files':
            // 新的文件选择模式：使用getSelectedLigandFiles获取选中文件
            const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
            if (selectedFiles.length === 0) {
                errors.push('请选择一个或多个分子结构文件');
                isValid = false;
            }
            break;
        case 'batch-file':
        case 'batch-text':
            // 批量SMILES模式：检查是否有批量分子数据
            if (!batchMolecules || batchMolecules.length === 0) {
                if (activeTab === 'batch-file') {
                    errors.push('请上传包含SMILES的CSV文件');
                } else {
                    errors.push('请输入SMILES文本并解析');
                }
                isValid = false;
            }
            break;
    }

    // 检查口袋坐标
    if (!pocketX.value || !pocketY.value || !pocketZ.value) {
        errors.push('请输入完整的口袋中心坐标');
        isValid = false;
    }

    if (!isValid) {
        alert('输入验证失败：\n' + errors.join('\n'));
    } else {
        let message = '输入验证通过！';
        if (isBatchMode) {
            message += `\n批量SMILES模式：将对接 ${batchMolecules.length} 个分子`;
        } else if (activeTab === 'files') {
            const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
            
            if (selectedFiles.length > 1) {
                message += `\n多分子文件模式：将对接 ${selectedFiles.length} 个分子文件`;
            } else if (selectedFiles.length === 1) {
                message += '\n单分子文件模式';
            }
        } else {
            message += '\n单分子SMILES模式';
        }
        alert(message);
    }

    return isValid;
}

// 准备对接数据
function prepareData() {
    // 使用新的蛋白质文件选择接口
    const selectedProteinFile = window.getSelectedProteinFile ? window.getSelectedProteinFile() : null;

    // 获取当前激活的tab
    const activeTab = getActiveLigandTab();
    const isBatchMode = (activeTab === 'batch-file' || activeTab === 'batch-text') && batchMolecules && batchMolecules.length > 0;
    
    // 准备发送到后端的数据
    const requestData = {
        receptor: selectedProteinFile ? {
            name: selectedProteinFile.name,
            content: selectedProteinFile.content,
            type: selectedProteinFile.type
        } : null,
        center_x: parseFloat(document.getElementById('pocket-x').value),
        center_y: parseFloat(document.getElementById('pocket-y').value),
        center_z: parseFloat(document.getElementById('pocket-z').value),
        size_x: parseInt(document.getElementById('size-x').value),
        size_y: parseInt(document.getElementById('size-y').value),
        size_z: parseInt(document.getElementById('size-z').value),
        thread: parseInt(document.getElementById('threads').value)
    };

    // 根据激活的tab处理配体数据
    switch(activeTab) {
        case 'files':
            // 新的文件选择模式：使用getSelectedLigandFiles获取选中文件
            const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
            
            if (selectedFiles.length > 1) {
                // 多文件模式：使用ligands参数（文件列表）
                requestData.ligands = selectedFiles.map(file => ({
                    name: file.name,
                    content: file.content,
                    type: file.type
                }));
                console.log(`多文件模式：选择了 ${selectedFiles.length} 个分子文件`);
            } else if (selectedFiles.length === 1) {
                // 单文件模式：使用ligand参数（单个文件）
                const file = selectedFiles[0];
                requestData.ligand = {
                    name: file.name,
                    content: file.content,
                    type: file.type
                };
                console.log('单文件模式：选择了 1 个分子文件');
            }
            break;
        case 'batch-file':
        case 'batch-text':
            // 批量SMILES模式：将所有分子的SMILES用换行符连接
            if (batchMolecules && batchMolecules.length > 0) {
                const allSmiles = batchMolecules.map(molecule => molecule.smiles).join('\n');
                requestData.smiles = allSmiles;
                console.log(`批量SMILES模式（${activeTab === 'batch-file' ? '文件' : '输入'}）：包含 ${batchMolecules.length} 个分子`);
            }
            break;
    }

    console.log('Prepared data:', requestData);
    return requestData;
}

// 开始对接
async function startDocking() {
    console.log('=== StartDocking Debug ===');
    
    // 验证输入
    if (!validateInputs()) {
        return;
    }

    // 获取当前激活的tab并检查是否在批量SMILES模式
    const activeTab = getActiveLigandTab();
    const isBatchMode = (activeTab === 'batch-file' || activeTab === 'batch-text') && batchMolecules && batchMolecules.length > 0;
    
    // 检查是否是多文件模式
    const selectedFiles = window.getSelectedLigandFiles ? window.getSelectedLigandFiles() : [];
    const isMultiFileMode = activeTab === 'files' && selectedFiles.length > 1;
    
    if (isBatchMode) {
        const tabName = activeTab === 'batch-file' ? '批量SMILES文件' : '批量SMILES输入';
        const confirmMessage = `您即将开始批量分子对接：\n\n` +
            `• 分子数量：${batchMolecules.length} 个\n` +
            `• 对接模式：${tabName}\n` +
            `• 所有分子将同时进行对接\n\n` +
            `确定要继续吗？`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
    } else if (isMultiFileMode) {
        const confirmMessage = `您即将开始多分子文件对接：\n\n` +
            `• 分子文件数量：${selectedFiles.length} 个\n` +
            `• 对接模式：多分子结构文件\n` +
            `• 所有分子将同时进行对接\n\n` +
            `确定要继续吗？`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
    }

    // 准备数据
    const data = prepareData();
    if (!data) {
        return;
    }

    // 更新UI状态
    const submitBtn = document.getElementById('submit-btn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = '提交中...';
    submitBtn.disabled = true;

    try {
        console.log('发送对接请求...');
        const response = await fetch('/api/autodock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();
        console.log('收到响应:', result);

        if (response.ok && result.success) {
            // 异步任务已创建
            console.log('任务创建成功:', result.task_id);
            
            // 显示任务创建成功的消息
            showNotification(`对接任务已提交！任务ID: ${result.task_id}`, 'success');
            
            // 刷新任务列表
            if (typeof loadTaskHistory === 'function') {
            loadTaskHistory();
            }
            
            // 重置表单
            resetForm();
            
        } else {
            // 处理认证失败的情况
            if (response.status === 401) {
                console.log('认证失败，跳转到登录页面');
                showNotification('登录状态已过期，请重新登录', 'warning');
                // 清除本地登录状态
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('username');
                localStorage.removeItem('loginTime');
                // 延迟跳转，让用户看到提示信息
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
                return;
            }
            
            throw new Error(result.message || '对接任务创建失败');
        }

    } catch (error) {
        console.error('对接请求失败:', error);
        
        // 检查是否是网络错误或认证问题
        if (error.message.includes('登录') || error.message.includes('认证') || error.message.includes('过期')) {
            showNotification('登录状态异常，请重新登录', 'warning');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        } else {
            showNotification('对接请求失败: ' + error.message, 'error');
        }
    } finally {
        // 恢复按钮状态
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 1000;
        max-width: 400px;
        word-wrap: break-word;
        transition: all 0.3s ease;
    `;
    
    // 根据类型设置颜色
    switch (type) {
        case 'success':
            notification.style.background = '#10b981';
            break;
        case 'error':
            notification.style.background = '#ef4444';
            break;
        case 'warning':
            notification.style.background = '#f59e0b';
            break;
        default:
            notification.style.background = '#3b82f6';
    }
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 重置表单
function resetForm() {
    // 重置基本输入
    document.getElementById('pocket-x').value = '';
    document.getElementById('pocket-y').value = '';
    document.getElementById('pocket-z').value = '';
    document.getElementById('size-x').value = '15';
    document.getElementById('size-y').value = '15';
    document.getElementById('size-z').value = '15';
    document.getElementById('threads').value = '2000';
    
    // 清空蛋白质文件选择
    const clearProteinBtn = document.getElementById('clear-protein-selection-btn');
    if (clearProteinBtn) {
        clearProteinBtn.click();
    }
    
    // 清空所有tab的数据
    clearSingleInputs();
    if (typeof clearBatchInputs === 'function') {
    clearBatchInputs();
    }
    
    // 切换回第一个tab
    const tabButtons = document.querySelectorAll('.ligand-tab');
    const tabContents = document.querySelectorAll('.ligand-tab-content');
    
    tabButtons.forEach(btn => btn.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));
    
    // 激活第一个tab
    const firstTab = document.querySelector('.ligand-tab[data-tab="files"]');
    const firstContent = document.getElementById('files-tab-content');
    
    if (firstTab) firstTab.classList.add('active');
    if (firstContent) firstContent.classList.add('active');
}

// 重置单个输入
function clearSingleInputs() {
    // 清除新的文件选择界面的选择状态
    const clearSelectionBtn = document.getElementById('clear-selection-btn');
    if (clearSelectionBtn) {
        clearSelectionBtn.click();
    }
}

// 重置批量输入
function clearBatchInputs() {
    document.getElementById('csv-file-input').value = '';
    document.getElementById('molecule-search').value = '';
}

// 获取当前激活的tab
function getActiveLigandTab() {
    const tabButtons = document.querySelectorAll('.ligand-tab');
    for (let i = 0; i < tabButtons.length; i++) {
        if (tabButtons[i].classList.contains('active')) {
            return tabButtons[i].getAttribute('data-tab');
        }
    }
    return null;
}

// 更新任务信息显示
function updateTaskInfo(taskInfo) {
    console.log('Updating task info:', taskInfo);
    
    // 更新任务ID
    const taskIdElement = document.getElementById('task-id');
    if (taskIdElement) {
        taskIdElement.textContent = taskInfo.task_id || '-';
    }
    
    // 更新状态
    const taskStatusElement = document.getElementById('task-status');
    if (taskStatusElement) {
        taskStatusElement.textContent = taskInfo.status || '-';
        // 根据状态设置样式
        taskStatusElement.className = 'info-value status-badge';
        if (taskInfo.status) {
            taskStatusElement.classList.add(`status-${taskInfo.status}`);
        }
    }
}

// 显示结果
function displayResults(resultData, taskId = null) {
    console.log('=== DisplayResults Debug ===');
    console.log('Result data:', resultData);
    console.log('Task ID:', taskId);
    console.log('Result data type:', typeof resultData);
    console.log('Result data keys:', Object.keys(resultData || {}));

    // 隐藏加载动画
    hideLoadingSpinner();

    // 更新状态为完成
    updateResultsStatus('completed', '对接计算已完成');

    // 更新任务信息 - 优先从结果数据中获取服务器返回的task_id
    const actualTaskId = resultData.task_id || taskId;
    const taskInfo = {
        task_id: actualTaskId || 'N/A',
        status: '已完成',
        completed_at: new Date().toISOString()
    };
    updateTaskInfo(taskInfo);

    // 获取人类可读结果
    let humanReadableResults = null;
    if (resultData.results && resultData.results.human_readable_results) {
        humanReadableResults = resultData.results.human_readable_results;
    } else if (resultData.human_readable_results) {
        humanReadableResults = resultData.human_readable_results;
    }
    
    console.log('Human readable results:', humanReadableResults);
    
    const humanReadableDiv = document.getElementById('human-readable-results');
    if (humanReadableResults) {
        // 处理人类可读结果，只保留任务总结信息
        const filteredResults = filterHumanReadableResults(humanReadableResults, resultData);
        humanReadableDiv.innerHTML = `<pre>${filteredResults}</pre>`;
    } else {
        humanReadableDiv.innerHTML = '<p>暂无详细结果信息</p>';
    }

    // 获取分数和文件数据 - 根据新的数据格式
    let scores = null;
    let files = null;
    
    if (resultData.scores) {
        scores = resultData.scores;
        files = resultData.pdbqt_files;
    }

    console.log('原始scores数据:', scores);
    console.log('原始files数据:', files);

    // 转换数据格式以适配现有的显示逻辑
    let processedScores = null;
    let processedFiles = null;

    if (scores && typeof scores === 'object') {
        processedScores = {};
        // 处理实际的数据格式：键是文件名（如"ligand_0.pdbqt"）
        Object.entries(scores).forEach(([filename, scoresArray]) => {
            console.log(`处理scores条目: ${filename} -> ${scoresArray}`);
            // 从文件名提取显示用的名称
            let displayFilename = filename;
            if (filename.includes('.pdbqt')) {
                // 将 "ligand_0.pdbqt" 转换为 "ligand_0_out"
                displayFilename = filename.replace('.pdbqt', '_out');
            }
            processedScores[displayFilename] = scoresArray;
            console.log(`转换后的scores键: ${filename} -> ${displayFilename}`);
        });
    }

    if (files && typeof files === 'object') {
        processedFiles = {};
        // 处理实际的数据格式：键是文件名，值可能是数组
        Object.entries(files).forEach(([filename, filePathArray]) => {
            console.log(`处理files条目: ${filename} -> ${filePathArray}`);
            // 从文件名提取显示用的名称
            let displayFilename = filename;
            if (filename.includes('.pdbqt')) {
                // 将 "ligand_0.pdbqt" 转换为 "ligand_0_out"
                displayFilename = filename.replace('.pdbqt', '_out');
            }
            
            // 处理文件路径：如果是数组，取第一个；如果是字符串，直接使用
            let filePath;
            if (Array.isArray(filePathArray)) {
                filePath = filePathArray.length > 0 ? filePathArray[0] : null;
            } else {
                filePath = filePathArray;
            }
            
            if (filePath) {
                console.log(`使用文件路径: ${filePath} (原始键: ${filename}, 显示键: ${displayFilename})`);
                processedFiles[displayFilename] = [filePath];
            } else {
                console.log(`❌ 文件路径为空: ${filename}`);
            }
        });
    }

    console.log('处理后的scores:', processedScores);
    console.log('处理后的files:', processedFiles);

    // 计算排序顺序（基于第一个构象的能量得分）
    let sortedOrder = null;
    if (processedScores && Object.keys(processedScores).length > 0) {
        const scoresArray = Object.entries(processedScores).map(([filename, scoresArray]) => {
            let firstScore = null;
            if (Array.isArray(scoresArray) && scoresArray.length > 0) {
                firstScore = scoresArray[0]; // 第一个构象的能量得分
            }
            return {
                filename,
                firstScore
            };
        });

        // 根据第一个构象的能量得分从小到大排序
        scoresArray.sort((a, b) => {
            if (a.firstScore === null && b.firstScore === null) return 0;
            if (a.firstScore === null) return 1;
            if (b.firstScore === null) return -1;
            return a.firstScore - b.firstScore;
        });

        sortedOrder = scoresArray.map(item => item.filename);
        console.log('Calculated sorted order:', sortedOrder);
    }
    
    // 显示分数结果
    if (processedScores && Object.keys(processedScores).length > 0) {
        console.log('✅ 调用 displayScores');
        displayScores(processedScores);
    } else {
        console.log('❌ 没有分数数据，显示默认消息');
        document.getElementById('scores-list').innerHTML = '<p>暂无分数数据</p>';
    }

    // 显示文件下载链接
    if (processedFiles && Object.keys(processedFiles).length > 0) {
        console.log('✅ 调用 displayFiles');
        displayFiles(processedFiles, sortedOrder, actualTaskId);
    } else {
        console.log('❌ 没有文件数据，显示默认消息');
        document.getElementById('files-list').innerHTML = '<p>暂无文件可下载</p>';
    }
    
    console.log('=== DisplayResults Debug End ===');
}

// 显示分数结果
function displayScores(scores) {
    console.log('=== DisplayScores Debug ===');
    console.log('Scores input:', scores);
    console.log('Scores type:', typeof scores);
    console.log('Scores keys:', Object.keys(scores || {}));
    
    const scoresList = document.getElementById('scores-list');
    scoresList.innerHTML = '';

    if (!scores || Object.keys(scores).length === 0) {
        console.log('No scores data to display');
        scoresList.innerHTML = '<p>暂无分数数据</p>';
        return;
    }

    // 将scores对象转换为数组，并根据第一个构象的能量得分排序
    const scoresArray = Object.entries(scores).map(([filename, scoresArray]) => {
        let firstScore = null;
        if (Array.isArray(scoresArray) && scoresArray.length > 0) {
            firstScore = scoresArray[0]; // 第一个构象的能量得分
        }
        return {
            filename,
            scoresArray,
            firstScore
        };
    });

    // 根据第一个构象的能量得分从小到大排序（能量越低越好）
    scoresArray.sort((a, b) => {
        // 如果某个配体没有有效的分数，将其排到最后
        if (a.firstScore === null && b.firstScore === null) return 0;
        if (a.firstScore === null) return 1;
        if (b.firstScore === null) return -1;
        
        return a.firstScore - b.firstScore; // 从小到大排序
    });

    console.log('Sorted scores array:', scoresArray);

    // 更新分页状态
    paginationState.scores.data = scoresArray;
    paginationState.scores.totalItems = scoresArray.length;
    paginationState.scores.currentPage = 1; // 重置到第一页

    // 渲染第一页
    renderScoresPage();
    
    console.log('=== DisplayScores Debug End ===');
}

// 显示文件下载链接
function displayFiles(files, sortedOrder = null, taskId = null) {
    console.log('=== DisplayFiles Debug ===');
    console.log('Files input:', files);
    console.log('Files type:', typeof files);
    console.log('Files keys:', Object.keys(files || {}));
    console.log('Sorted order:', sortedOrder);
    console.log('Task ID:', taskId);
    
    const filesList = document.getElementById('files-list');
    filesList.innerHTML = '';

    if (!files || Object.keys(files).length === 0) {
        console.log('No files data to display');
        filesList.innerHTML = '<p>暂无文件可下载</p>';
        return;
    }

    // 从文件名中提取原始分子序号的函数
    function extractMoleculeIndex(filename) {
        // 匹配 ligand_X_out 格式，其中X是分子序号
        const match = filename.match(/ligand_(\d+)_out/);
        if (match) {
            return parseInt(match[1], 10);
        }
        // 如果不匹配，尝试其他可能的格式
        const generalMatch = filename.match(/(\d+)/);
        if (generalMatch) {
            return parseInt(generalMatch[1], 10);
        }
        // 如果都不匹配，返回0作为默认值
        return 0;
    }

    // 如果有排序顺序，按照排序顺序处理文件；否则按原始顺序
    let filesToProcess;
    if (sortedOrder && sortedOrder.length > 0) {
        // 按照排序顺序创建文件数组
        filesToProcess = sortedOrder.map((filename, sortedIndex) => {
            const originalMoleculeIndex = extractMoleculeIndex(filename);
            return {
                filename,
                urlsArray: files[filename] || [],
                sortedIndex: sortedIndex, // 排序后的位置（用于显示排名）
                originalMoleculeIndex: originalMoleculeIndex // 原始分子序号（用于下载链接）
            };
        }).filter(item => item.urlsArray.length > 0); // 只保留有有效URL的文件
    } else {
        // 按原始顺序处理
        filesToProcess = Object.entries(files).map(([filename, urlsArray], index) => {
            const originalMoleculeIndex = extractMoleculeIndex(filename);
            return {
                filename,
                urlsArray,
                sortedIndex: index,
                originalMoleculeIndex: originalMoleculeIndex
            };
        });
    }
    
    console.log('Files to process:', filesToProcess);
    
    // 更新分页状态，传递taskId
    paginationState.files.data = filesToProcess;
    paginationState.files.totalItems = filesToProcess.length;
    paginationState.files.currentPage = 1; // 重置到第一页
    paginationState.files.taskId = taskId; // 保存taskId

    // 渲染第一页
    renderFilesPage();
    
    console.log('=== DisplayFiles Debug End ===');
}

// 下载文件
function downloadFile(filename, fileContent) {
    const blob = new Blob([fileContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// 从URL下载文件
function downloadFileFromUrl(url, filename) {
    console.log('=== Download Debug ===');
    console.log('Downloading file from URL:', url);
    console.log('Suggested filename:', filename);
    
    // 直接在新窗口打开文件链接
    window.open(url, '_blank');
}

// 模型提取功能
function setupModelExtraction() {
    const extractBtn = document.getElementById('extract-model-btn');
    const fileInput = document.getElementById('pdbqt-file-input');
    const modelInput = document.getElementById('model-number-input');
    const statusDiv = document.getElementById('extraction-status');
    
    if (extractBtn) {
        extractBtn.addEventListener('click', async function() {
            const file = fileInput.files[0];
            const modelNumber = parseInt(modelInput.value) || 1;
            
            if (!file) {
                showExtractionStatus('请选择PDBQT文件', 'error');
                return;
            }
            
            if (modelNumber < 1) {
                showExtractionStatus('构象编号必须大于0', 'error');
                return;
            }
            
            await extractModel(file, modelNumber);
        });
    }
}

// 显示提取状态
function showExtractionStatus(message, type = 'info') {
    const statusDiv = document.getElementById('extraction-status');
    if (!statusDiv) return;
    
    statusDiv.style.display = 'block';
    statusDiv.textContent = message;
    
    // 设置状态样式
    statusDiv.className = 'extraction-status';
    switch (type) {
        case 'success':
            statusDiv.style.backgroundColor = '#d1fae5';
            statusDiv.style.color = '#065f46';
            statusDiv.style.borderColor = '#10b981';
            break;
        case 'error':
            statusDiv.style.backgroundColor = '#fee2e2';
            statusDiv.style.color = '#991b1b';
            statusDiv.style.borderColor = '#ef4444';
            break;
        case 'warning':
            statusDiv.style.backgroundColor = '#fef3c7';
            statusDiv.style.color = '#92400e';
            statusDiv.style.borderColor = '#f59e0b';
            break;
        default: // info
            statusDiv.style.backgroundColor = '#dbeafe';
            statusDiv.style.color = '#1e40af';
            statusDiv.style.borderColor = '#3b82f6';
    }
    statusDiv.style.border = '1px solid';
}

// 提取模型
async function extractModel(file, modelNumber) {
    const extractBtn = document.getElementById('extract-model-btn');
    const originalText = extractBtn.innerHTML;
    
    try {
        // 设置按钮为处理中状态
        extractBtn.disabled = true;
        extractBtn.innerHTML = '<span>⏳</span> 处理中...';
        extractBtn.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
        
        showExtractionStatus('正在读取文件...', 'info');
        
        // 读取文件内容
        const fileContent = await readFileContent(file);
        
        showExtractionStatus(`正在提取第${modelNumber}个构象...`, 'info');
        
        // 发送到后端处理
        const response = await fetch('/api/extract_model', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                pdbqt_content: fileContent,
                model_number: modelNumber,
                filename: file.name
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showExtractionStatus('提取成功！正在下载PDB文件...', 'success');
            
            // 下载生成的PDB文件
            downloadFile(result.filename, result.pdb_content);
            
            // 显示成功信息
            setTimeout(() => {
                showExtractionStatus(`成功提取第${modelNumber}个构象并转换为PDB格式`, 'success');
            }, 1000);
            
        } else {
            showExtractionStatus('提取失败: ' + result.message, 'error');
        }
        
    } catch (error) {
        console.error('提取模型时出错:', error);
        showExtractionStatus('提取过程中发生错误: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        extractBtn.disabled = false;
        extractBtn.innerHTML = originalText;
        extractBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
    }
}


// 分页状态管理
let paginationState = {
    scores: {
        currentPage: 1,
        pageSize: 10,
        totalItems: 0,
        data: []
    },
    files: {
        currentPage: 1,
        pageSize: 10,
        totalItems: 0,
        data: [],
        taskId: null
    }
};

// 创建分页控件
function createPaginationControls(containerId, type, currentPage, totalPages, totalItems) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // 清除现有的分页控件
    const existingPagination = container.querySelector('.pagination-container');
    if (existingPagination) {
        existingPagination.remove();
    }

    if (totalPages <= 1) return; // 如果只有一页或没有数据，不显示分页控件

    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-container';

    // 分页信息
    const pageInfo = document.createElement('div');
    pageInfo.className = 'pagination-info';
    pageInfo.textContent = `共 ${totalItems} 个结果，第 ${currentPage} / ${totalPages} 页`;

    // 分页控件
    const paginationControls = document.createElement('div');
    paginationControls.className = 'pagination-controls';

    // 上一页按钮
    const prevBtn = document.createElement('button');
    prevBtn.className = 'pagination-btn';
    prevBtn.textContent = '‹';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => changePage(type, currentPage - 1);

    // 页码按钮
    const pageButtons = [];
    const maxVisiblePages = 7; // 最多显示7个页码按钮
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    // 调整起始页，确保显示足够的页码
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // 第一页
    if (startPage > 1) {
        const firstBtn = document.createElement('button');
        firstBtn.className = 'pagination-btn';
        firstBtn.textContent = '1';
        firstBtn.onclick = () => changePage(type, 1);
        pageButtons.push(firstBtn);

        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'pagination-ellipsis';
            ellipsis.textContent = '...';
            pageButtons.push(ellipsis);
        }
    }

    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i.toString();
        pageBtn.onclick = () => changePage(type, i);
        pageButtons.push(pageBtn);
    }

    // 最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'pagination-ellipsis';
            ellipsis.textContent = '...';
            pageButtons.push(ellipsis);
        }

        const lastBtn = document.createElement('button');
        lastBtn.className = 'pagination-btn';
        lastBtn.textContent = totalPages.toString();
        lastBtn.onclick = () => changePage(type, totalPages);
        pageButtons.push(lastBtn);
    }

    // 下一页按钮
    const nextBtn = document.createElement('button');
    nextBtn.className = 'pagination-btn';
    nextBtn.textContent = '›';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => changePage(type, currentPage + 1);

    // 页面大小选择器
    const pageSizeSelector = document.createElement('div');
    pageSizeSelector.className = 'page-size-selector';
    
    const pageSizeLabel = document.createElement('span');
    pageSizeLabel.textContent = '每页显示:';
    
    const pageSizeSelect = document.createElement('select');
    const pageSizeOptions = [10, 20, 50, 100];
    pageSizeOptions.forEach(size => {
        const option = document.createElement('option');
        option.value = size;
        option.textContent = size;
        option.selected = size === paginationState[type].pageSize;
        pageSizeSelect.appendChild(option);
    });
    
    pageSizeSelect.onchange = (e) => {
        changePageSize(type, parseInt(e.target.value));
    };

    // 组装控件
    paginationControls.appendChild(prevBtn);
    pageButtons.forEach(btn => paginationControls.appendChild(btn));
    paginationControls.appendChild(nextBtn);

    pageSizeSelector.appendChild(pageSizeLabel);
    pageSizeSelector.appendChild(pageSizeSelect);

    paginationContainer.appendChild(pageInfo);
    paginationContainer.appendChild(paginationControls);
    paginationContainer.appendChild(pageSizeSelector);

    container.appendChild(paginationContainer);
}

// 切换页面
function changePage(type, newPage) {
    const state = paginationState[type];
    const totalPages = Math.ceil(state.totalItems / state.pageSize);
    
    if (newPage < 1 || newPage > totalPages) return;
    
    state.currentPage = newPage;
    
    if (type === 'scores') {
        renderScoresPage();
    } else if (type === 'files') {
        renderFilesPage();
    }
}

// 改变页面大小
function changePageSize(type, newPageSize) {
    const state = paginationState[type];
    state.pageSize = newPageSize;
    state.currentPage = 1; // 重置到第一页
    
    if (type === 'scores') {
        renderScoresPage();
    } else if (type === 'files') {
        renderFilesPage();
    }
}

// 渲染分数页面
function renderScoresPage() {
    console.log('=== RenderScoresPage Debug ===');
    const state = paginationState.scores;
    console.log('Pagination state:', state);
    
    const startIndex = (state.currentPage - 1) * state.pageSize;
    const endIndex = Math.min(startIndex + state.pageSize, state.totalItems);
    const pageData = state.data.slice(startIndex, endIndex);
    console.log('Page data to render:', pageData);

    const scoresList = document.getElementById('scores-list');
    console.log('Scores list element:', scoresList);
    console.log('Scores list element exists:', !!scoresList);
    
    if (!scoresList) {
        console.error('❌ scores-list element not found!');
        return;
    }
    
    const ul = document.createElement('ul');
    console.log('Created ul element:', ul);

    pageData.forEach(({ filename, scoresArray: scores }, pageIndex) => {
        const globalIndex = startIndex + pageIndex;
        console.log(`Rendering score for ${filename} (页面位置: ${pageIndex + 1}, 全局排名: ${globalIndex + 1}):`, scores);
        
        const li = document.createElement('li');
        
        if (Array.isArray(scores) && scores.length > 0) {
            const bestScore = Math.min(...scores);
            const bestIndex = scores.indexOf(bestScore);
            
            // 只显示前3个构象，如果有更多则显示省略号
            const maxDisplayConformations = 3;
            const displayScores = scores.slice(0, maxDisplayConformations);
            const hasMoreConformations = scores.length > maxDisplayConformations;
            
            const htmlContent = `
                <strong>${filename} (排名: ${globalIndex + 1}):</strong>
                <div style="margin-top: 0.5rem;">
                    <div style="color: #059669; font-weight: 600;">
                        最佳结合能量: ${bestScore} kcal/mol (构象 ${bestIndex + 1})
                    </div>
                    <div style="margin-top: 0.5rem;">
                        <strong>构象结合能量 (前${Math.min(scores.length, maxDisplayConformations)}个):</strong>
                        <ul style="margin-top: 0.25rem;">
                            ${displayScores.map((score, index) => 
                                `<li style="color: ${index === bestIndex ? '#059669' : '#6b7280'}; font-weight: ${index === bestIndex ? '600' : 'normal'};">
                                    构象 ${index + 1}: ${score} kcal/mol ${index === bestIndex ? '(最佳)' : ''}
                                </li>`
                            ).join('')}
                            ${hasMoreConformations ? `<li style="color: #9ca3af; font-style: italic;">... 还有 ${scores.length - maxDisplayConformations} 个构象</li>` : ''}
                        </ul>
                    </div>
                </div>
            `;
            
            li.innerHTML = htmlContent;
            console.log('Created li with content:', htmlContent);
        } else {
            const htmlContent = `<strong>${filename} (排名: ${globalIndex + 1}):</strong> <span style="color: #ef4444;">无有效分数数据</span>`;
            li.innerHTML = htmlContent;
            console.log('Created li with no-data content:', htmlContent);
        }
        
        ul.appendChild(li);
        console.log('Appended li to ul, ul children count:', ul.children.length);
    });

    console.log('Final ul element:', ul);
    console.log('Final ul children count:', ul.children.length);
    console.log('Final ul innerHTML:', ul.innerHTML);

    scoresList.innerHTML = '';
    scoresList.appendChild(ul);
    
    console.log('Updated scoresList innerHTML:', scoresList.innerHTML);
    console.log('ScoresList element visibility:', window.getComputedStyle(scoresList).display);
    console.log('ScoresList element dimensions:', scoresList.getBoundingClientRect());

    // 创建分页控件
    const totalPages = Math.ceil(state.totalItems / state.pageSize);
    createPaginationControls('scores-list', 'scores', state.currentPage, totalPages, state.totalItems);
    
    console.log('=== RenderScoresPage Debug End ===');
}

// 渲染文件页面
function renderFilesPage() {
    console.log('=== RenderFilesPage Debug ===');
    const state = paginationState.files;
    console.log('Files pagination state:', state);
    
    const startIndex = (state.currentPage - 1) * state.pageSize;
    const endIndex = Math.min(startIndex + state.pageSize, state.totalItems);
    const pageData = state.data.slice(startIndex, endIndex);
    const taskId = state.taskId; // 获取保存的taskId
    console.log('Files page data to render:', pageData);

    const filesList = document.getElementById('files-list');
    console.log('Files list element:', filesList);
    console.log('Files list element exists:', !!filesList);
    
    if (!filesList) {
        console.error('❌ files-list element not found!');
        return;
    }
    
    const ul = document.createElement('ul');

    pageData.forEach(({ filename, urlsArray, sortedIndex, originalMoleculeIndex }, pageIndex) => {
        const globalIndex = startIndex + pageIndex;
        // 使用originalMoleculeIndex作为下载链接的分子序号（原始分子的真实序号）
        console.log(`Rendering file for ${filename} (页面位置: ${pageIndex + 1}, 全局排名: ${globalIndex + 1}, 原始分子序号: ${originalMoleculeIndex}):`, urlsArray);
        
        const li = document.createElement('li');
        
        if (Array.isArray(urlsArray) && urlsArray.length > 0) {
            const fileNameSpan = document.createElement('span');
            fileNameSpan.textContent = `${filename} (排名: ${globalIndex + 1})`;
            fileNameSpan.style.fontWeight = '600';
            fileNameSpan.style.color = '#1f2937';
            
            const buttonsContainer = document.createElement('div');
            buttonsContainer.style.display = 'flex';
            buttonsContainer.style.gap = '0.5rem';
            buttonsContainer.style.marginTop = '0.5rem';
            
            urlsArray.forEach((url, index) => {
                const downloadBtn = document.createElement('button');
                
                if (urlsArray.length === 1) {
                    downloadBtn.textContent = '下载文件';
                } else {
                    downloadBtn.textContent = `下载构象 ${index + 1}`;
                }
                
                downloadBtn.style.padding = '0.5rem 1rem';
                downloadBtn.style.background = '#3b82f6';
                downloadBtn.style.color = 'white';
                downloadBtn.style.border = 'none';
                downloadBtn.style.borderRadius = '4px';
                downloadBtn.style.fontSize = '0.875rem';
                downloadBtn.style.cursor = 'pointer';
                downloadBtn.style.transition = 'background-color 0.2s ease';
                
                downloadBtn.onmouseover = () => {
                    downloadBtn.style.background = '#1d4ed8';
                };
                downloadBtn.onmouseout = () => {
                    downloadBtn.style.background = '#3b82f6';
                };
                
                // 直接使用POST返回结果中的实际文件链接
                let downloadUrl = url; // 使用API返回的实际文件链接
                
                downloadBtn.onclick = () => downloadFileFromUrl(downloadUrl, `${filename}_pose_${index + 1}`);
                
                buttonsContainer.appendChild(downloadBtn);
            });
            
            li.appendChild(fileNameSpan);
            li.appendChild(buttonsContainer);
        } else {
            const displayName = `${filename} (排名: ${globalIndex + 1})`;
            li.innerHTML = `<strong>${displayName}:</strong> <span style="color: #ef4444;">无可用下载链接</span>`;
        }
        
        ul.appendChild(li);
    });

    console.log('Final files ul element:', ul);
    console.log('Final files ul children count:', ul.children.length);

    filesList.innerHTML = '';
    filesList.appendChild(ul);
    
    console.log('Updated filesList innerHTML:', filesList.innerHTML);
    console.log('FilesList element visibility:', window.getComputedStyle(filesList).display);
    console.log('FilesList element dimensions:', filesList.getBoundingClientRect());

    // 创建分页控件
    const totalPages = Math.ceil(state.totalItems / state.pageSize);
    createPaginationControls('files-list', 'files', state.currentPage, totalPages, state.totalItems);
    
    console.log('=== RenderFilesPage Debug End ===');
}



// 显示结果栏
function showResultsSection() {
    console.log('=== ShowResultsSection Debug ===');
    const resultsSection = document.getElementById('docking-results-section');
    console.log('Results section element:', resultsSection);
    console.log('Results section current display:', resultsSection ? resultsSection.style.display : 'element not found');
    
    if (resultsSection) {
        resultsSection.style.display = 'block';
        console.log('Results section display set to block');
        
        // 显示加载状态
        showLoadingSpinner();
        
        // 滚动到结果栏
        resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        console.log('Scrolled to results section');
        } else {
        console.error('Results section element not found!');
    }
    console.log('=== ShowResultsSection Debug End ===');
}

// 更新结果状态
function updateResultsStatus(status, message) {
    console.log(`=== UpdateResultsStatus: ${status} - ${message} ===`);
    const statusText = document.getElementById('results-status-text');
    console.log('Status text element:', statusText);
    
    if (statusText) {
        statusText.textContent = message;
        statusText.className = `status-text status-${status}`;
        console.log(`Status updated to: ${status}, message: ${message}`);
    } else {
        console.error('Status text element not found!');
    }
}

// 显示/隐藏加载动画
function showLoadingSpinner() {
    const loadingElement = document.getElementById('results-loading');
    if (loadingElement) {
        loadingElement.style.display = 'block';
    }
}

function hideLoadingSpinner() {
    const loadingElement = document.getElementById('results-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

// 切换详细结果显示
function toggleDetails() {
    const detailedResults = document.getElementById('detailed-results');
    const toggleBtn = document.getElementById('toggle-details');
    
    if (detailedResults.style.display === 'none') {
        detailedResults.style.display = 'block';
        toggleBtn.textContent = '收起';
    } else {
        detailedResults.style.display = 'none';
        toggleBtn.textContent = '展开';
    }
}

// 隐藏结果区域
function hideResults() {
    const resultsSection = document.getElementById('docking-results-section');
    if (resultsSection) {
        resultsSection.style.display = 'none';
        console.log('Results section hidden');
    }
}


// 显示任务结果
async function showTaskResults(taskId) {
    try {
        console.log('=== showTaskResults Debug ===');
        console.log('Fetching results for task ID:', taskId);
        
        const response = await fetch(`/api/tasks/${taskId}`);
        const result = await response.json();
        
        // console.log('API response:', result);
        // console.log('Task data:', result.task);
        // console.log('Task result:', result.task?.result);     
        // console.log('Task result type:', typeof result.task?.result);
        
        if (result.success && result.task) {
            // 显示结果区域
            const resultsSection = document.getElementById('docking-results-section');
            if (resultsSection) {
                resultsSection.style.display = 'block';
                resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                console.log('Results section displayed and scrolled into view');
            } else {
                console.error('Results section not found!');
            }
            
            // 检查任务是否有结果数据
            if (result.task.result) {
                console.log('Task has result data, calling displayResults...');
                // 显示结果，传递从后端获取的实际任务ID
                displayResults(result.task.result, result.task.task_id);
                
                // 自动展开详细结果区域
                setTimeout(() => {
                    const detailedResults = document.getElementById('detailed-results');
                    const toggleBtn = document.getElementById('toggle-details');
                    if (detailedResults && toggleBtn) {
                        detailedResults.style.display = 'block';
                        toggleBtn.textContent = '收起';
                        console.log('Auto-expanded detailed results');
                    }
                    
                    // 如果有文件数据，自动切换到文件标签页
                    const taskResult = result.task.result;
                    let hasFiles = false;
                    
                    if (typeof taskResult === 'string') {
                        try {
                            const parsedResult = JSON.parse(taskResult);
                            hasFiles = parsedResult.results && parsedResult.results.pdbqt_files && 
                                        Object.keys(parsedResult.results.pdbqt_files).length > 0;
                        } catch (e) {
                            console.log('Could not parse task result as JSON');
                        }
                    } else if (taskResult && taskResult.results && taskResult.results.pdbqt_files) {
                        hasFiles = Object.keys(taskResult.results.pdbqt_files).length > 0;
                    }
                    
                    if (hasFiles) {
                        // 切换到文件标签页
                        const scoresTab = document.getElementById('scores-tab');
                        const filesTab = document.getElementById('files-tab');
                        const scoresBtn = document.querySelector('.tab-btn[data-tab="scores"]');
                        const filesBtn = document.querySelector('.tab-btn[data-tab="files"]');
                        
                        if (scoresTab && filesTab && scoresBtn && filesBtn) {
                            // 移除scores标签页的active状态
                            scoresTab.classList.remove('active');
                            scoresBtn.classList.remove('active');
                            
                            // 添加files标签页的active状态
                            filesTab.classList.add('active');
                            filesBtn.classList.add('active');
                            
                            console.log('Auto-switched to files tab');
                        }
                    }
                }, 500); // 延迟500ms确保displayResults完成
            } else {
                console.log('Task has no result data');
            }
        } else {
            showNotification('任务结果不可用', 'warning');
        }
    } catch (error) {
        console.error('获取任务结果出错:', error);
        showNotification('获取任务结果出错', 'error');
    }
}

// 格式化任务参数显示（隐藏文件内容，只显示文件名和大小）
function formatTaskParameters(parameters) {
    if (!parameters) return 'N/A';
    
    try {
        // 深拷贝参数对象
        const formattedParams = JSON.parse(JSON.stringify(parameters));
        
        // 处理receptor文件
        if (formattedParams.receptor && formattedParams.receptor.content) {
            const content = formattedParams.receptor.content;
            const sizeKB = Math.round(content.length / 1024 * 100) / 100;
            formattedParams.receptor = {
                name: formattedParams.receptor.name || '未知文件',
                size: `${sizeKB} KB`,
                type: 'PDB文件'
            };
        }
        
        // 处理ligand文件
        if (formattedParams.ligand && formattedParams.ligand.content) {
            const content = formattedParams.ligand.content;
            const sizeKB = Math.round(content.length / 1024 * 100) / 100;
            formattedParams.ligand = {
                name: formattedParams.ligand.name || '未知文件',
                size: `${sizeKB} KB`,
                type: 'SDF文件'
            };
        }
        
        // 格式化显示
        return JSON.stringify(formattedParams, null, 2);
    } catch (error) {
        console.error('格式化参数时出错:', error);
        return '参数格式错误';
    }
}

// 显示任务详情模态框
function showTaskDetailsModal(task) {
    // 创建模态框HTML
    const modalHtml = `
        <div id="task-details-modal" class="modal-overlay" onclick="closeTaskDetailsModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>任务详情</h3>
                    <button onclick="closeTaskDetailsModal()" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="detail-row">
                        <label>任务ID:</label>
                        <span class="task-id">${task.task_id}</span>
                    </div>
                    <div class="detail-row">
                        <label>状态:</label>
                        <span class="task-status ${task.status}">${getStatusText(task.status)}</span>
                    </div>
                    <div class="detail-row">
                        <label>创建时间:</label>
                        <span>${formatDateTime(task.created_at)}</span>
                    </div>
                    <div class="detail-row">
                        <label>更新时间:</label>
                        <span>${formatDateTime(task.updated_at)}</span>
                    </div>
                    ${task.completed_at ? `
                        <div class="detail-row">
                            <label>完成时间:</label>
                            <span>${formatDateTime(task.completed_at)}</span>
                        </div>
                    ` : ''}
                    ${task.error_message ? `
                        <div class="detail-row">
                            <label>错误信息:</label>
                            <span style="color: #ef4444;">${task.error_message}</span>
                        </div>
                    ` : ''}
                    <div class="detail-row">
                        <label>参数:</label>
                        <pre class="parameters-display">${formatTaskParameters(task.parameters)}</pre>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}


// 关闭任务详情模态框
function closeTaskDetailsModal() {
    const modal = document.getElementById('task-details-modal');
    if (modal) {
        modal.remove();
    }
}


// 过滤和格式化人类可读结果
function filterHumanReadableResults(humanReadableResults, resultData) {
    if (!humanReadableResults) return '';
    
    try {
        let filteredText = '';
        
        // 添加任务总结信息
        if (resultData && resultData.task_id) {
            // 计算完成的分子数量
            let moleculeCount = 0;
            if (resultData.results && resultData.results.scores) {
                moleculeCount = Object.keys(resultData.results.scores).length;
            }
            
            // 只显示任务总结，不显示每个配体的详细内容
            filteredText = `任务 ${resultData.task_id} 共完成了 ${moleculeCount} 个分子的对接计算。`;
            
            return filteredText;
        }
        
        // 如果没有任务ID，尝试从humanReadableResults中提取总结信息
        if (typeof humanReadableResults === 'string') {
            // 如果是字符串，尝试提取总结行
            const lines = humanReadableResults.split('\n');
            for (const line of lines) {
                if (line.includes('共完成了') && line.includes('个分子的对接计算')) {
                    return line.trim();
                }
                if (line.includes('任务') && line.includes('完成')) {
                    return line.trim();
                }
            }
            // 如果找不到总结行，返回第一行作为总结
            return lines[0] || '对接计算已完成';
        }
        
        // 如果是对象，尝试提取总结信息
        if (typeof humanReadableResults === 'object') {
            if (humanReadableResults.summary) {
                return humanReadableResults.summary;
            }
            // 默认返回简单的完成信息
            return '对接计算已完成';
        }
        
        // 其他情况，返回简单的完成信息
        return '对接计算已完成';
        
    } catch (error) {
        console.error('Error filtering human readable results:', error);
        return '对接计算已完成';
    }
}

// 显示任务历史
function displayTaskHistory(tasks) {
    console.log('=== displayTaskHistory called ===');
    console.log('Tasks received:', tasks);
    const tasksList = document.getElementById('tasks-list');
    console.log('Tasks list element:', tasksList);
    
    if (!tasks || tasks.length === 0) {
        console.log('No tasks found, showing empty message');
        tasksList.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 2rem;">暂无AutoDock任务历史</p>';
        // 没有任务时停止定期刷新
        stopPeriodicRefresh();
        return;
    }
    
    console.log('Displaying', tasks.length, 'tasks');
    // 检查是否有等待中或运行中的任务
    const hasPendingOrRunningTasks = tasks.some(task => 
        task.status === 'pending' || task.status === 'running'
    );
    
    // 根据任务状态决定是否需要定期刷新
    if (hasPendingOrRunningTasks) {
        // 如果有等待中或运行中的任务，确保定期刷新正在运行
        if (!refreshInterval) {
            startPeriodicRefresh();
        }
    } else {
        // 如果没有等待中或运行中的任务，停止定期刷新
        stopPeriodicRefresh();
    }
    
    tasksList.innerHTML = tasks.map(task => `
        <div class="task-item" data-task-id="${task.task_id}">
            <div class="task-header">
                <span class="task-id">${task.task_id}</span>
                <span class="task-status ${task.status}">${getStatusText(task.status)}</span>
            </div>
            <div class="task-info">
                <div>创建时间: ${formatDateTime(task.created_at)}</div>
                <div>更新时间: ${formatDateTime(task.updated_at)}</div>
                ${task.completed_at ? `<div>完成时间: ${formatDateTime(task.completed_at)}</div>` : ''}
            </div>
            <div class="task-actions">
                <button class="task-btn view" onclick="viewTaskDetails('${task.task_id}')">查看详情</button>
                ${task.status === 'completed' ? `<button class="task-btn view" onclick="showTaskResults('${task.task_id}')">查看结果</button>` : ''}
            </div>
        </div>
    `).join('');
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败'
    };
    return statusMap[status] || status;
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 查看任务详情
async function viewTaskDetails(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}`);
        const result = await response.json();
        
        if (result.success) {
            showTaskDetailsModal(result.task);
        } else {
            showNotification('获取任务详情失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('获取任务详情出错:', error);
        showNotification('获取任务详情出错', 'error');
    }
}

// 加载任务历史
async function loadTaskHistory() {
    console.log('=== loadTaskHistory called ===');
    try {
        console.log('Fetching tasks from /api/tasks?task_type=autodock...');
        const response = await fetch('/api/tasks?task_type=autodock');
        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('API result:', result);
        
        if (result.success) {
            console.log('Tasks data:', result.tasks);
            displayTaskHistory(result.tasks);
        } else {
            console.error('加载任务历史失败:', result.message);
            // 如果API调用失败，显示空状态
            displayTaskHistory([]);
        }
    } catch (error) {
        console.error('加载任务历史出错:', error);
        // 如果网络错误或其他异常，显示空状态
        displayTaskHistory([]);
    }
}


// 定期刷新相关函数

// 开始定期刷新
function startPeriodicRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    // 每30秒刷新一次任务状态
    refreshInterval = setInterval(() => {
        console.log('定期刷新任务状态...');
        loadTaskHistory();
    }, 30000);
    console.log('已启动定期刷新');
}

// 停止定期刷新
function stopPeriodicRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
        console.log('已停止定期刷新');
    }
}

// 初始化蛋白质文件选择功能
function initializeProteinFileSelection() {
    console.log('AutoDock: Initializing protein file selection interface...');
    
    // 获取界面元素
    const proteinFilesGrid = document.getElementById('protein-files-grid');
    const localProteinInput = document.getElementById('local-protein-input');
    const proteinDropZone = document.getElementById('protein-drop-zone');
    const selectedProteinDisplay = document.getElementById('selected-protein-display');
    const selectedProteinInfo = document.getElementById('selected-protein-info');
    const clearProteinSelectionBtn = document.getElementById('clear-protein-selection-btn');
    
    // 存储选中的蛋白质文件（单选）
    let selectedProteinFile = null;
    
    // 更新已上传蛋白质文件网格
    function updateProteinFilesGrid() {
        if (!proteinFilesGrid) return;
        
        if (typeof uploadedFilesData !== 'undefined' && uploadedFilesData.length > 0) {
            // 过滤出PDB和PDBQT文件
            const proteinFiles = uploadedFilesData.filter(file => 
                file.type.toLowerCase() === 'pdb' || file.type.toLowerCase() === 'pdbqt'
            );
            
            if (proteinFiles.length > 0) {
                proteinFilesGrid.innerHTML = '';
                proteinFiles.forEach(file => {
                    const fileCard = document.createElement('div');
                    fileCard.className = 'file-card';
                    fileCard.dataset.fileId = file.id;
                    fileCard.innerHTML = `
                        <div class="file-card-header">
                            <span class="file-type-icon">${getFileTypeIcon(file.type)}</span>
                            <span class="file-name" title="${file.name}">${file.name}</span>
                        </div>
                        <div class="file-info">${file.type.toUpperCase()} • ${formatFileSize(file.size || 0)}</div>
                        <div class="selection-indicator">✓</div>
                    `;
                    
                    // 添加点击事件（单选）
                    fileCard.addEventListener('click', function() {
                        selectProteinFile(file, fileCard);
                    });
                    
                    proteinFilesGrid.appendChild(fileCard);
                });
            } else {
                proteinFilesGrid.innerHTML = '<div class="no-files-message">暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件</div>';
            }
        } else {
            proteinFilesGrid.innerHTML = '<div class="no-files-message">暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件</div>';
        }
    }
    
    // 将函数引用存储到全局变量
    globalUpdateProteinFilesGrid = updateProteinFilesGrid;
    
    // 选择蛋白质文件（单选模式）
    function selectProteinFile(file, fileCard) {
        // 清除之前的选择
        proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
            card.classList.remove('selected');
        });
        
        // 选择新文件
        selectedProteinFile = {
            id: file.id,
            name: file.name,
            type: file.type,
            content: file.content,
            source: 'uploaded'
        };
        fileCard.classList.add('selected');
        
        updateSelectedProteinDisplay();
    }
    
    // 本地蛋白质文件上传处理
    if (localProteinInput && proteinDropZone) {
        // 点击上传区域
        proteinDropZone.addEventListener('click', function() {
            localProteinInput.click();
        });
        
        // 文件选择处理
        localProteinInput.addEventListener('change', function(e) {
            handleLocalProteinUpload(e.target.files);
        });
        
        // 拖拽上传处理
        proteinDropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        proteinDropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        proteinDropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            handleLocalProteinUpload(e.dataTransfer.files);
        });
    }
    
    // 处理本地蛋白质文件上传
    function handleLocalProteinUpload(files) {
        if (files.length > 0) {
            const file = files[0]; // 只取第一个文件（单选）
            const reader = new FileReader();
            reader.onload = function(e) {
                // 清除之前的选择
                proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                    card.classList.remove('selected');
                });
                
                selectedProteinFile = {
                    id: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                    name: file.name,
                    type: getFileType(file.name),
                    content: e.target.result,
                    source: 'local'
                };
                
                updateSelectedProteinDisplay();
                showNotification(`已选择蛋白质文件: ${file.name}`, 'success');
            };
            reader.readAsText(file);
        }
        
        // 清空input
        localProteinInput.value = '';
    }
    
    // 更新已选蛋白质文件显示
    function updateSelectedProteinDisplay() {
        if (!selectedProteinDisplay || !selectedProteinInfo) return;
        
        if (selectedProteinFile) {
            selectedProteinDisplay.style.display = 'block';
            
            selectedProteinInfo.innerHTML = `
                <div class="selected-file-item">
                    <span class="file-name">${selectedProteinFile.name} (${selectedProteinFile.type.toUpperCase()}) ${selectedProteinFile.source === 'local' ? '- 本地' : ''}</span>
                    <button class="remove-file-btn" id="remove-protein-btn">×</button>
                </div>
            `;
            
            // 添加删除按钮事件
            document.getElementById('remove-protein-btn').addEventListener('click', function() {
                selectedProteinFile = null;
                
                // 清除网格中的选中状态
                proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                    card.classList.remove('selected');
                });
                
                updateSelectedProteinDisplay();
            });
        } else {
            selectedProteinDisplay.style.display = 'none';
        }
    }
    
    // 清空选择按钮
    if (clearProteinSelectionBtn) {
        clearProteinSelectionBtn.addEventListener('click', function() {
            selectedProteinFile = null;
            
            // 清除网格中的选中状态
            proteinFilesGrid.querySelectorAll('.file-card.selected').forEach(card => {
                card.classList.remove('selected');
            });
            
            updateSelectedProteinDisplay();
        });
    }
    
    // 初始化时更新文件网格
    updateProteinFilesGrid();
    
    // 提供全局访问接口
    window.getSelectedProteinFile = function() {
        return selectedProteinFile;
    };
}

// 初始化按钮事件
function initializeButtonEvents() {
    console.log('AutoDock: Initializing button events...');

    // 绑定收起/展开按钮
    const toggleDetailsBtn = document.getElementById('toggle-details');
    if (toggleDetailsBtn) {
        toggleDetailsBtn.addEventListener('click', function() {
            console.log('AutoDock: Toggle details button clicked');
            toggleDetails();
        });
        console.log('AutoDock: Toggle details button event bound');
    } else {
        console.error('AutoDock: Toggle details button not found');
    }
    
    // 绑定"开始对接"按钮
    const submitBtn = document.getElementById('submit-btn');
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            console.log('AutoDock: Submit button clicked');
            startDocking();
        });
        console.log('AutoDock: Submit button event bound');
    } else {
        console.error('AutoDock: Submit button not found');
    }
    
    // 绑定"验证输入"按钮
    const validateBtn = document.getElementById('validate-inputs');
    if (validateBtn) {
        validateBtn.addEventListener('click', function() {
            console.log('AutoDock: Validate button clicked');
            validateInputs();
        });
        console.log('AutoDock: Validate button event bound');
    }
    
    // 绑定"重置参数"按钮
    const resetBtn = document.getElementById('reset-inputs');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            console.log('AutoDock: Reset button clicked');
            resetForm();
        });
        console.log('AutoDock: Reset button event bound');
    }
    
    // 绑定"自动填入"坐标按钮
    const autoFillBtn = document.getElementById('auto-fill-coords');
    if (autoFillBtn) {
        autoFillBtn.addEventListener('click', function() {
            console.log('AutoDock: Auto-fill coordinates button clicked');
            calculateAndSetGeometricCenter();
        });
        console.log('AutoDock: Auto-fill coordinates button event bound');
    }
    
    // 绑定刷新任务按钮
    const refreshTasksBtn = document.getElementById('refresh-tasks-btn');
    if (refreshTasksBtn) {
        refreshTasksBtn.addEventListener('click', function() {
            console.log('AutoDock: Refresh tasks button clicked');
            loadTaskHistory();
        });
        console.log('AutoDock: Refresh tasks button event bound');
    }
    
    console.log('AutoDock: Button events initialized successfully');

    // 绑定 CSV 文件上传按钮
    const uploadCsvBtn = document.getElementById('upload-csv-btn');
    const csvFileInput = document.getElementById('csv-file-input');
    if (uploadCsvBtn && csvFileInput) {
        uploadCsvBtn.addEventListener('click', function() {
            console.log('AutoDock: CSV upload button clicked');
            csvFileInput.click();
        });
        
        csvFileInput.addEventListener('change', function(e) {
            console.log('AutoDock: CSV file selected');
            handleCSVUpload(e.target.files);
        });
        
        console.log('AutoDock: CSV upload button events bound');
    } else {
        console.error('AutoDock: CSV upload elements not found');
    }
    
    // 绑定 SMILES 文本解析按钮
    const parseSmilesTextBtn = document.getElementById('parse-smiles-text-btn');
    if (parseSmilesTextBtn) {
        parseSmilesTextBtn.addEventListener('click', function() {
            console.log('AutoDock: Parse SMILES text button clicked');
            handleSmilesTextParsing();
        });
        console.log('AutoDock: Parse SMILES text button event bound');
    } else {
        console.error('AutoDock: Parse SMILES text button not found');
    }
}

    // 处理 CSV 文件上传
function handleCSVUpload(files) {
    if (files.length === 0) return;
    
    const file = files[0];
    if (!file.name.toLowerCase().endsWith('.csv')) {
        showNotification('请选择 CSV 格式文件', 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csvContent = e.target.result;
            const molecules = parseCSVContent(csvContent);
            
            if (molecules.length === 0) {
                showNotification('CSV 文件中没有找到有效的分子数据', 'error');
                return;
            }
            
            // 显示分子列表
            displayBatchMolecules(molecules);
            showNotification(`成功解析 ${molecules.length} 个分子`, 'success');
            
        } catch (error) {
            console.error('CSV 解析错误:', error);
            showNotification('CSV 文件解析失败: ' + error.message, 'error');
        }
    };
    
    reader.readAsText(file);
    
    // 清空 input
    document.getElementById('csv-file-input').value = '';
}

// 解析 CSV 内容
function parseCSVContent(csvContent) {
    const lines = csvContent.trim().split('\n');
    const molecules = [];
    
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        // 简单的 CSV 解析（假设用逗号分隔）
        const parts = line.split(',');
        if (parts.length >= 2) {
            const name = parts[0].trim().replace(/"/g, '');
            const smiles = parts[1].trim().replace(/"/g, '');
            
            if (name && smiles) {
                molecules.push({
                    name: name,
                    smiles: smiles,
                    id: 'csv_' + i + '_' + Date.now()
                });
            }
        }
    }
    
    return molecules;
}

// 处理 SMILES 文本解析
function handleSmilesTextParsing() {
    const textArea = document.getElementById('batch-smiles-text');
    if (!textArea) {
        showNotification('文本输入区域未找到', 'error');
        return;
    }
    
    const smilesText = textArea.value.trim();
    if (!smilesText) {
        showNotification('请输入 SMILES 字符串', 'warning');
        return;
    }
    
    try {
        const lines = smilesText.split('\n');
        const molecules = [];
        
        for (let i = 0; i < lines.length; i++) {
            const smiles = lines[i].trim();
            if (smiles) {
                molecules.push({
                    name: `Molecule_${i + 1}`,
                    smiles: smiles,
                    id: 'text_' + i + '_' + Date.now()
                });
            }
        }
        
        if (molecules.length === 0) {
            showNotification('没有找到有效的 SMILES 字符串', 'warning');
            return;
        }
        
        // 显示分子列表
        displayBatchMolecules(molecules);
        showNotification(`成功解析 ${molecules.length} 个分子`, 'success');
        
    } catch (error) {
        console.error('SMILES 文本解析错误:', error);
        showNotification('SMILES 文本解析失败: ' + error.message, 'error');
    }
}

// 显示批量分子列表
function displayBatchMolecules(molecules) {
    const batchContainer = document.querySelector('.batch-molecules-container');
    const moleculesList = document.getElementById('molecules-list');
    const moleculeCount = document.getElementById('molecule-count');
    
    if (!batchContainer || !moleculesList || !moleculeCount) {
        console.error('批量分子显示元素未找到');
        return;
    }
    
    // 显示容器
    batchContainer.style.display = 'block';
    
    // 更新分子数量
    moleculeCount.textContent = molecules.length;
    
    // 生成分子列表
    moleculesList.innerHTML = '';
    molecules.forEach((molecule, index) => {
        const moleculeItem = document.createElement('div');
        moleculeItem.className = 'molecule-item';
        moleculeItem.innerHTML = `
            <div class="molecule-info">
                <div class="molecule-name">${molecule.name}</div>
                <div class="molecule-smiles">${molecule.smiles}</div>
            </div>
        `;
        
        // 添加点击事件显示 2D 结构
        moleculeItem.addEventListener('click', function() {
            // 移除其他选中状态
            moleculesList.querySelectorAll('.molecule-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选中当前项
            this.classList.add('selected');
            
            // 显示 2D 结构（如果有 SMILES 绘制功能）
            displayMolecule2D(molecule.smiles, molecule.name);
        });
        
        moleculesList.appendChild(moleculeItem);
        
    });
    
    // 存储分子数据以供后续使用
    window.currentBatchMolecules = molecules;
    window.batchMolecules = molecules;
    // 重要：同时设置全局的batchMolecules变量
    batchMolecules = molecules;
}

// 显示 2D 分子结构（如果有 SMILES 绘制库）
function displayMolecule2D(smiles, name) {
    const canvas = document.getElementById('structure2d');
    const moleculeInfo = document.getElementById('molecule-info');
    
    if (!canvas || !moleculeInfo) return;
    
    // 更新分子信息
    moleculeInfo.innerHTML = `
        <div><strong>名称:</strong> ${name}</div>
        <div><strong>SMILES:</strong> ${smiles}</div>
    `;
    
    // 如果有 SMILES 绘制库（如 SMILES-Drawer），在这里绘制 2D 结构
    if (typeof SmilesDrawer !== 'undefined') {
        try {
            const smilesDrawer = new SmilesDrawer.Drawer({
                width: 300,
                height: 200
            });
            
            SmilesDrawer.parse(smiles, function(tree) {
                smilesDrawer.draw(tree, canvas, 'light', false);
            });
        } catch (error) {
            console.error('绘制 2D 结构失败:', error);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('无法绘制 2D 结构', canvas.width / 2, canvas.height / 2);
        }
    } else {
        // 如果没有绘制库，显示占位文本
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#666';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('2D 结构预览', canvas.width / 2, canvas.height / 2);
    }
}
