<template>
  <div class="register-page">
    <!-- Language Switcher -->
    <div class="language-switcher">
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'zh' }"
        @click="switchLanguage('zh')"
      >
        🇨🇳 中文
      </button>
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'en' }"
        @click="switchLanguage('en')"
      >
        🇺🇸 EN
      </button>
    </div>

    <div class="register-container">
      <div class="register-header">
        <h1>{{ t('register.title') }}</h1>
        <p>{{ t('register.subtitle') }}</p>
      </div>
      
      <form @submit.prevent="submitForm">
        <div class="form-group">
          <label for="contactInfo">{{ t('register.contact-info-label') }}</label>
          <div class="input-with-captcha">
            <input 
              type="text" 
              id="contactInfo" 
              v-model="form.contactInfo" 
              required 
              :placeholder="t('register.contact-info-placeholder')"
              :class="{ 'valid': isContactInfoValid && form.contactInfo, 'invalid': !isContactInfoValid && form.contactInfo }"
              @input="validateContactInfo"
            >
            <div id="captcha-element"></div>
            <button 
              type="button" 
              class="code-btn" 
              :disabled="!isContactInfoValid || countdown > 0" 
              @click="handleSendCode"
            >
              {{ countdown > 0 ? `${countdown}${t('register.send-code-retry')}` : t('register.send-code-btn') }}
            </button>
          </div>
        </div>
        
        <div class="form-group">
          <label for="verificationCode">{{ t('register.verification-code-label') }}</label>
          <input 
            type="text" 
            id="verificationCode" 
            v-model="form.verificationCode" 
            required 
            :placeholder="t('register.verification-code-placeholder')"
            :class="{ 'valid': form.verificationCode.length > 0 }"
          >
        </div>
        
        <div class="form-group">
          <label for="password">{{ t('common.password-label') }}</label>
          <input 
            type="password" 
            id="password" 
            v-model="form.password" 
            required 
            :placeholder="t('common.password-placeholder')"
            :class="{ 'valid': isPasswordValid && form.password, 'invalid': !isPasswordValid && form.password }"
            @input="validatePassword"
          >
          <div class="password-requirements" v-if="form.password">
            <div :class="['requirement', passwordChecks.length ? 'valid' : 'invalid']">
              <div class="requirement-icon">{{ passwordChecks.length ? '✓' : '✗' }}</div>
              <span>{{ t('register.req-length') }}</span>
            </div>
            <div :class="['requirement', passwordChecks.uppercase ? 'valid' : 'invalid']">
              <div class="requirement-icon">{{ passwordChecks.uppercase ? '✓' : '✗' }}</div>
              <span>{{ t('register.req-uppercase') }}</span>
            </div>
            <div :class="['requirement', passwordChecks.lowercase ? 'valid' : 'invalid']">
              <div class="requirement-icon">{{ passwordChecks.lowercase ? '✓' : '✗' }}</div>
              <span>{{ t('register.req-lowercase') }}</span>
            </div>
            <div :class="['requirement', passwordChecks.number ? 'valid' : 'invalid']">
              <div class="requirement-icon">{{ passwordChecks.number ? '✓' : '✗' }}</div>
              <span>{{ t('register.req-number') }}</span>
            </div>
            <div :class="['requirement', passwordChecks.special ? 'valid' : 'invalid']">
              <div class="requirement-icon">{{ passwordChecks.special ? '✓' : '✗' }}</div>
              <span>{{ t('register.req-special') }}</span>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="confirmPassword">{{ t('register.confirm-password-label') }}</label>
          <input 
            type="password" 
            id="confirmPassword" 
            v-model="form.confirmPassword" 
            required 
            :placeholder="t('register.confirm-password-placeholder')"
            :class="{ 'valid': isPasswordMatch && form.confirmPassword, 'invalid': !isPasswordMatch && form.confirmPassword }"
            @input="validateForm"
          >
        </div>
        
        <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
        <div v-if="successMessage" class="success-message">{{ successMessage }}</div>
        
        <!-- User Agreement Checkbox -->
        <div class="agreement-container">
          <input 
            type="checkbox" 
            id="agreementCheckbox" 
            class="agreement-checkbox"
            v-model="form.agreementAccepted"
            @change="validateForm"
          >
          <label for="agreementCheckbox" class="agreement-text">
            <span>{{ t('register.agreement-text') }}</span>
            <a href="/terms-of-service" target="_blank" class="agreement-link">{{ t('register.user-agreement') }}</a>
          </label>
        </div>
        
        <button type="submit" class="register-btn" :disabled="!isFormValid">{{ t('common.register-btn') }}</button>
        
        <div class="auth-divider">
          <span>{{ t('register.already-have-account') }}</span>
        </div>
        
        <button type="button" class="back-btn" @click="goToLogin">{{ t('register.back-to-login') }}</button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { sendVerificationCode, registerUser } from '../api/login'
import { t, switchLanguage as switchLang, currentLanguage } from '../utils/i18n'
import { isAuthenticated } from '../utils/auth'

const router = useRouter()

// 响应式数据
const form = reactive({
  contactInfo: '',
  password: '',
  confirmPassword: '',
  verificationCode: '',
  agreementAccepted: false
})

const passwordChecks = reactive({
  length: false,
  uppercase: false,
  lowercase: false,
  number: false,
  special: false
})

const countdown = ref(0)
const errorMessage = ref('')
const successMessage = ref('')
const isContactInfoValid = ref(false)
const captchaInstance = ref(null)
let countdownTimer = null

// 计算属性
const isPasswordValid = computed(() => {
  return Object.values(passwordChecks).every(check => check)
})

const isPasswordMatch = computed(() => {
  return form.password === form.confirmPassword && form.confirmPassword.length > 0
})

const isFormValid = computed(() => {
  return isContactInfoValid.value && 
         isPasswordValid.value && 
         isPasswordMatch.value &&
         form.verificationCode.length > 0 &&
         form.agreementAccepted
})

// 方法
const validateContactInfo = () => {
  const mobileRegex = /^1[3-9]\d{9}$/
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  isContactInfoValid.value = mobileRegex.test(form.contactInfo) || emailRegex.test(form.contactInfo)
}

const getContactType = (contactInfo) => {
  const mobileRegex = /^1[3-9]\d{9}$/
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  
  if (mobileRegex.test(contactInfo)) {
    return 'mobile'
  } else if (emailRegex.test(contactInfo)) {
    return 'email'
  } else {
    return null
  }
}

const validatePassword = () => {
  const requirements = {
    length: /^.{6,}$/,
    uppercase: /[A-Z]/,
    lowercase: /[a-z]/,
    number: /[0-9]/,
    special: /[!@#$%^&*(),.?":{}|<>_+=\-\[\]\\;'`~]/
  }

  passwordChecks.length = requirements.length.test(form.password)
  passwordChecks.uppercase = requirements.uppercase.test(form.password)
  passwordChecks.lowercase = requirements.lowercase.test(form.password)
  passwordChecks.number = requirements.number.test(form.password)
  passwordChecks.special = requirements.special.test(form.password)
}

const validateForm = () => {
  // Form validation is handled by computed properties
}

const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

const sendVerificationCodeHandler = async () => {
  if (!isContactInfoValid.value || countdown.value > 0) {
    return
  }

  const contactType = getContactType(form.contactInfo)
  
  if (!contactType) {
    showError(t('register.invalid-contact'))
    return
  }

  try {
    const response = await sendVerificationCode({
      contactInfo: form.contactInfo,
      method: contactType
    })

    if (response.success) {
      if (response.rescode === '200') {
        showSuccess(t('register.code-sent'))
        startCountdown()
      } else {
        showError(response.notice || t('register.code-send-failed'))
      }
    } else {
      showError(response.message || t('register.code-send-failed'))
    }
  } catch (error) {
    console.error('发送验证码错误:', error)
    showError(t('common.network-error'))
  }
}

// 处理验证码按钮点击（集成阿里云验证码）
const handleSendCode = () => {
  if (!isContactInfoValid.value || countdown.value > 0) {
    return
  }

  // 如果有验证码实例，触发验证码验证
  if (captchaInstance.value) {
    captchaInstance.value.showCaptcha()
  } else {
    // 如果没有验证码实例，直接发送验证码（降级处理）
    sendVerificationCodeHandler()
  }
}

const submitForm = async () => {
  if (!isFormValid.value) {
    return
  }

  const contactType = getContactType(form.contactInfo)

  // Final validation
  if (!isPasswordValid.value) {
    showError(t('register.password-invalid'))
    return
  }

  if (!isPasswordMatch.value) {
    showError(t('register.password-not-match'))
    return
  }

  if (!isContactInfoValid.value) {
    showError(t('register.invalid-contact'))
    return
  }

  if (!form.verificationCode) {
    showError(t('register.enter-code'))
    return
  }

  if (!form.agreementAccepted) {
    showError(t('register.agreement-required'))
    return
  }

  try {
    const response = await registerUser({
      contactInfo: form.contactInfo,
      method: contactType,
      password: form.password,
      verificationCode: form.verificationCode
    })

    if (response.success) {
      showSuccess(t('register.success'))
      setTimeout(() => {
        router.push('/home')
      }, 2000)
    } else {
      showError(response.message || t('register.failed'))
      setTimeout(() => {
        // Optionally refresh the page or redirect back to register
      }, 1000)
    }
  } catch (error) {
    console.error('注册错误:', error)
    showError(t('common.network-error'))
  }
}

const showError = (message) => {
  errorMessage.value = message
  successMessage.value = ''
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

const showSuccess = (message) => {
  successMessage.value = message
  errorMessage.value = ''
  setTimeout(() => {
    successMessage.value = ''
  }, 5000)
}

const goToLogin = () => {
  router.push('/login')
}

const switchLanguage = (lang) => {
  switchLang(lang)
  // Update document title
  document.title = t('page-title')
}

// 初始化阿里云验证码
const initAliyunCaptcha = () => {
  if (window.initAliyunCaptcha) {
    window.initAliyunCaptcha({
      SceneId: 'ic_login',
      prefix: '7kaa4s',
      mode: 'popup',
      element: '#captcha-element',
      button: '.code-btn',
      captchaVerifyCallback: function(captchaVerifyParam) {
        console.log('验证码验证成功:', captchaVerifyParam)
        // 验证码验证成功后才发送验证码
        sendVerificationCodeHandler()
      },
      onBizResultCallback: function(bizResult) {
        console.log('业务结果:', bizResult)
      },
      getInstance: function(instance) {
        captchaInstance.value = instance
      }
    })
  }
}

// 生命周期
onMounted(() => {
  // 检查是否已经登录
  if (isAuthenticated()) {
    router.push('/home')
    return
  }

  // 设置页面标题
  document.title = t('register.page-title')
  
  // 初始化阿里云验证码
  setTimeout(() => {
    initAliyunCaptcha()
  }, 1000)
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})

// 监听语言变化
watch(currentLanguage, () => {
  document.title = t('register.page-title')
})
</script>

<style scoped>
@import '../assets/styles/register.css';
</style> 