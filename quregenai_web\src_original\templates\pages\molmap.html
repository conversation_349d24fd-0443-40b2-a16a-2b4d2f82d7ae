{% extends "layout_with_nav.html" %}

{% block title %}MolMap ADMET - QureGenAI 药物设计平台{% endblock %}

{% block page_css %}
<link rel="stylesheet" href="/src/styles/pages/molmap.css">
{% endblock %}

{% block content %}

            <div class="page-title">
                <div class="molmap-icon">🧬</div>
                <div>
                    <h1 data-i18n="molmap-title">MolMap ADMET预测</h1>
                </div>
            </div>
            <p class="page-subtitle" data-i18n="molmap-subtitle">
                基于分子指纹图谱的ADMET性质预测，支持单个或批量分子的药物代谢动力学和毒性评估
            </p>

            <div class="form-container">
                <form id="molmapForm">
                    <div class="form-section">
                        <h3>
                            <span>📝</span>
                            <span data-i18n="smiles-input-title">SMILES输入</span>
                        </h3>

                        <div class="smiles-input-tabs">
                            <button type="button" class="tab-button active" data-tab="single">
                                <span data-i18n="single-smiles">单个SMILES</span>
                            </button>
                            <button type="button" class="tab-button" data-tab="batch">
                                <span data-i18n="batch-smiles">批量SMILES</span>
                            </button>
                        </div>

                        <div class="tab-content active" id="single-tab">
                            <div class="form-group">
                                <label for="singleSmiles" data-i18n="single-smiles-label">SMILES字符串</label>
                                <input type="text" id="singleSmiles" name="singleSmiles" class="form-control" 
                                       placeholder="例如：CCO (乙醇)" data-i18n-placeholder="single-smiles-placeholder">
                                <div class="help-text" data-i18n="single-smiles-help">
                                    输入单个分子的SMILES表示，不支持多分子输入（包含'.'的SMILES）
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="batch-tab">
                            <div class="form-group">
                                <label for="batchSmiles" data-i18n="batch-smiles-label">批量SMILES（每行一个）</label>
                                <textarea id="batchSmiles" name="batchSmiles" class="form-control" 
                                          placeholder="CCO&#10;CC(=O)O&#10;c1ccccc1" data-i18n-placeholder="batch-smiles-placeholder"></textarea>
                                <div class="help-text" data-i18n="batch-smiles-help">
                                    每行输入一个SMILES，最多支持10个分子同时预测
                                </div>
                            </div>
                        </div>

                        <button type="button" class="validate-btn" onclick="validateSmiles()">
                            <span data-i18n="validate-smiles-btn">验证SMILES</span>
                        </button>

                        <div id="validationStatus" class="validation-status"></div>

                        <div class="smiles-examples">
                            <h4 data-i18n="example-molecules">示例分子：</h4>
                            <div class="example-smiles">
                                <div class="example-smiles-item" onclick="useExampleSmiles('CCO')" title="乙醇">CCO</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('CC(=O)O')" title="乙酸">CC(=O)O</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('c1ccccc1')" title="苯">c1ccccc1</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('O=C(O)c1ccccc1O')" title="水杨酸">O=C(O)c1ccccc1O</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('Cn1cnc2c1c(=O)n(C)c(=O)n2C')" title="咖啡因">Cn1cnc2c1c(=O)n(C)c(=O)n2C</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>
                            <span>⚙️</span>
                            <span data-i18n="task-settings-title">任务设置</span>
                        </h3>

                        <div class="form-group">
                            <label for="jobName" data-i18n="job-name-label">任务名称（可选）</label>
                            <input type="text" id="jobName" name="jobName" class="form-control" 
                                   placeholder="自定义任务名称" data-i18n-placeholder="job-name-placeholder">
                            <div class="help-text" data-i18n="job-name-help">
                                为您的预测任务指定一个便于识别的名称，留空将自动生成
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn" id="submitBtn">
                        <span data-i18n="submit-prediction">提交ADMET预测</span>
                    </button>
                </form>
            </div>

            <div id="resultsSection" class="results-section">
                <h3 data-i18n="task-submitted">任务已提交</h3>
                <div class="task-info">
                    <h4 data-i18n="task-info-title">任务信息</h4>
                    <div class="task-details" id="taskDetails">
                        <!-- 任务详情将在这里动态显示 -->
                    </div>
                </div>
                <p data-i18n="task-processing-notice">
                    您的ADMET预测任务正在处理中，请前往任务管理页面查看进度和结果。
                </p>
            </div>

            <!-- 任务结果显示区域 -->
            <div id="resultsContainer" class="results-container">
                <h3 data-i18n="results-title">任务结果</h3>
                <div id="taskStatusInfo" class="task-status-info">
                    <!-- 任务状态信息 -->
                </div>
                <div id="moleculeResults" class="molecule-results">
                    <!-- 分子结果将在这里动态生成 -->
                </div>
            </div>


            <div class="section-header">
                <h2 data-i18n="task-history">ADMET预测历史</h2>
                <button id="refresh-tasks-btn" class="btn btn-refresh-green">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="23 4 23 10 17 10"></polyline>
                        <polyline points="1 20 1 14 7 14"></polyline>
                        <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                    </svg>
                    <span data-i18n="refresh-tasks">刷新</span>
                </button>
            </div>
            <div class="task-history-container">
                <div id="tasks-list" class="tasks-list">
                    <!-- 任务列表将在这里动态生成 -->
                </div>
                <!-- 添加分页控件 -->
                <div id="tasks-pagination" class="pagination-container" style="display: none;">
                    <div class="pagination-info">
                        <span id="pagination-info-text"></span>
                    </div>
                    <div class="pagination-controls">
                        <button id="prev-page-btn" class="pagination-btn" onclick="loadTaskHistoryPage(currentPage - 1)">
                            <span data-i18n="prev-page">上一页</span>
                        </button>
                        <div id="page-numbers" class="page-numbers">
                            <!-- 页码按钮将在这里动态生成 -->
                        </div>
                        <button id="next-page-btn" class="pagination-btn" onclick="loadTaskHistoryPage(currentPage + 1)">
                            <span data-i18n="next-page">下一页</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    

    <!-- 通知消息 -->
    <div id="notification" class="notification"></div>
{% endblock %}

{% block page_js %}
<script>
        // 多语言文本配置
        const translations = {
            zh: {
                'molmap-title': 'MolMap ADMET预测',
                'molmap-subtitle': '基于分子指纹图谱的ADMET性质预测，支持单个或批量分子的药物代谢动力学和毒性评估',
                'smiles-input-title': 'SMILES输入',
                'single-smiles': '单个SMILES',
                'batch-smiles': '批量SMILES',
                'single-smiles-label': 'SMILES字符串',
                'single-smiles-placeholder': '例如：CCO (乙醇)',
                'single-smiles-help': '输入单个分子的SMILES表示，不支持多分子输入（包含\'.\'的SMILES）',
                'batch-smiles-label': '批量SMILES（每行一个）',
                'batch-smiles-placeholder': 'CCO\nCC(=O)O\nc1ccccc1',
                'batch-smiles-help': '每行输入一个SMILES，最多支持10个分子同时预测',
                'validate-smiles-btn': '验证SMILES',
                'example-molecules': '示例分子：',
                'task-settings-title': '任务设置',
                'job-name-label': '任务名称（可选）',
                'job-name-placeholder': '自定义任务名称',
                'job-name-help': '为您的预测任务指定一个便于识别的名称，留空将自动生成',
                'submit-prediction': '提交ADMET预测',
                'task-submitted': '任务已提交',
                'task-submitted-with-id': '任务提交成功！任务ID',
                'task-info-title': '任务信息',
                'task-processing-notice': '您的ADMET预测任务正在处理中，请前往任务管理页面查看进度和结果。',
                'validation-success': 'SMILES验证通过',
                'validation-error': 'SMILES验证失败',
                'task-id': '任务ID',
                'job-name': '任务名称',
                'molecule-count': '分子数量',
                'submit-time': '提交时间',
                'status': '状态',
                'processing': '处理中',
                'results-title': '任务结果',
                'download-csv': '查看详情',
                'completed': '已完成',
                'failed': '失败',
                'loading-csv': '加载中...',
                'csv-loaded': '已加载',
                'retry-download': '重试下载',
                'task-history': 'ADMET预测历史',
                'refresh-tasks': '刷新',
                'prev-page': '上一页',
                'next-page': '下一页',
                'pending': '等待中',
                'view-results': '查看结果'
            },
            en: {
                'molmap-title': 'MolMap ADMET Prediction',
                'molmap-subtitle': 'ADMET property prediction based on molecular fingerprint maps, supporting single or batch molecular pharmacokinetic and toxicity assessment',
                'smiles-input-title': 'SMILES Input',
                'single-smiles': 'Single SMILES',
                'batch-smiles': 'Batch SMILES',
                'single-smiles-label': 'SMILES String',
                'single-smiles-placeholder': 'e.g., CCO (ethanol)',
                'single-smiles-help': 'Enter a single molecule SMILES representation, multi-molecule input (containing \'.\') is not supported',
                'batch-smiles-label': 'Batch SMILES (one per line)',
                'batch-smiles-placeholder': 'CCO\nCC(=O)O\nc1ccccc1',
                'batch-smiles-help': 'Enter one SMILES per line, supports up to 10 molecules simultaneously',
                'validate-smiles-btn': 'Validate SMILES',
                'example-molecules': 'Example Molecules:',
                'task-settings-title': 'Task Settings',
                'job-name-label': 'Job Name (Optional)',
                'job-name-placeholder': 'Custom job name',
                'job-name-help': 'Specify a recognizable name for your prediction task, leave blank for auto-generation',
                'submit-prediction': 'Submit ADMET Prediction',
                'task-submitted': 'Task Submitted',
                'task-submitted-with-id': 'Task submitted successfully! Task ID',
                'task-info-title': 'Task Information',
                'task-processing-notice': 'Your ADMET prediction task is being processed. Please go to the task management page to view progress and results.',
                'validation-success': 'SMILES validation passed',
                'validation-error': 'SMILES validation failed',
                'task-id': 'Task ID',
                'job-name': 'Job Name',
                'molecule-count': 'Molecule Count',
                'submit-time': 'Submit Time',
                'status': 'Status',
                'processing': 'Processing',
                'results-title': 'Task Results',
                'download-csv': 'View Details',
                'completed': 'Completed',
                'failed': 'Failed',
                'loading-csv': 'Loading...',
                'csv-loaded': 'Loaded',
                'retry-download': 'Retry Download',
                'task-history': 'ADMET Prediction History',
                'refresh-tasks': 'Refresh',
                'prev-page': 'Previous',
                'next-page': 'Next',
                'pending': 'Pending',
                'view-results': 'View Results'
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            
            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });
            
            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            // 更新文档标题
            document.title = lang === 'zh' ? 'MolMap ADMET - QureGenAI 药物设计平台' : 'MolMap ADMET - QureGenAI Drug Design Platform';
            
            // 更新页面文本
            updatePageText(lang);
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];
            
            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t[key]) {
                    element.textContent = t[key];
                }
            });

            // 更新placeholder
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (t[key]) {
                    element.placeholder = t[key];
                }
            });
        }

        // 通用API请求函数
        function apiRequest(url, options = {}) {
            options.credentials = 'include';
            
            return fetch(url, options)
                .then(response => {
                    if (response.status === 401) {
                        clearLocalStorage();
                        window.location.href = 'login';
                        throw new Error('认证失败，请重新登录');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.redirect === '/login') {
                        clearLocalStorage();
                        window.location.href = 'login';
                        throw new Error(data.message || '需要重新登录');
                    }
                    return data;
                });
        }

        // 显示通知
        function showNotification(message, type = 'info', duration = 4000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // Tab切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.dataset.tab;
                
                // 更新按钮状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // 更新内容显示
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                document.getElementById(`${tabName}-tab`).classList.add('active');
            });
        });

        // 使用示例SMILES
        function useExampleSmiles(smiles) {
            const activeTab = document.querySelector('.tab-button.active').dataset.tab;
            
            if (activeTab === 'single') {
                document.getElementById('singleSmiles').value = smiles;
            } else {
                const textarea = document.getElementById('batchSmiles');
                const currentValue = textarea.value.trim();
                if (currentValue) {
                    textarea.value = currentValue + '\n' + smiles;
                } else {
                    textarea.value = smiles;
                }
            }
        }

        // 获取当前SMILES输入
        function getCurrentSmiles() {
            const activeTab = document.querySelector('.tab-button.active').dataset.tab;
            
            if (activeTab === 'single') {
                const smiles = document.getElementById('singleSmiles').value.trim();
                return smiles ? [smiles] : [];
            } else {
                const smilesText = document.getElementById('batchSmiles').value.trim();
                return smilesText ? smilesText.split('\n').map(s => s.trim()).filter(s => s) : [];
            }
        }

        // 验证SMILES
        async function validateSmiles() {
            const smiles = getCurrentSmiles();
            
            if (smiles.length === 0) {
                showValidationResult(false, '请输入SMILES字符串');
                return;
            }

            try {
                const response = await apiRequest('/api/molmap/validate_smiles', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        smiles: smiles.length === 1 ? smiles[0] : smiles
                    })
                });

                if (response.success) {
                    const t = translations[currentLanguage];
                    let message = `${t['validation-success']}: ${response.valid_count}/${response.total_count}`;
                    if (response.invalid_count > 0) {
                        message += ` (${response.invalid_count} 个无效)`;
                    }
                    showValidationResult(response.overall_valid, message);
                } else {
                    showValidationResult(false, response.message);
                }
            } catch (error) {
                console.error('验证SMILES时出错:', error);
                showValidationResult(false, '验证失败：' + error.message);
            }
        }

        // 显示验证结果
        function showValidationResult(success, message) {
            const statusDiv = document.getElementById('validationStatus');
            statusDiv.textContent = message;
            statusDiv.className = `validation-status ${success ? 'success' : 'error'}`;
            statusDiv.style.display = 'block';
        }

        // 表单提交
        document.getElementById('molmapForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const smiles = getCurrentSmiles();
            const jobName = document.getElementById('jobName').value.trim();
            
            if (smiles.length === 0) {
                showNotification('请输入SMILES字符串', 'error');
                return;
            }

            // 禁用提交按钮
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';

            try {
                const requestData = {
                    smiles: smiles.length === 1 ? smiles[0] : smiles
                };
                
                if (jobName) {
                    requestData.job_name = jobName;
                }

                const response = await apiRequest('/api/molmap/admet_predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (response.success) {
                    const t = translations[currentLanguage];
                    const message = `${t['task-submitted'] || '任务已提交'}！任务ID: ${response.task_id}`;
                    showNotification(message, 'success', 3000);
                    // 移除 showTaskResult(response); 调用
                } else {
                    showNotification('提交失败：' + response.message, 'error');
                }
            } catch (error) {
                console.error('提交任务时出错:', error);
                showNotification('提交失败：' + error.message, 'error');
            } finally {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        });

        // 显示任务结果
        function showTaskResult(response) {
            const resultsSection = document.getElementById('resultsSection');
            const taskDetails = document.getElementById('taskDetails');
            const t = translations[currentLanguage];
            
            taskDetails.innerHTML = `
                <div class="task-detail-item">
                    <span>${t['task-id']}:</span>
                    <span>${response.task_id}</span>
                </div>
                <div class="task-detail-item">
                    <span>${t['job-name']}:</span>
                    <span>${response.job_name}</span>
                </div>
                <div class="task-detail-item">
                    <span>${t['molecule-count']}:</span>
                    <span>${response.molecule_count}</span>
                </div>
                <div class="task-detail-item">
                    <span>${t['submit-time']}:</span>
                    <span>${new Date().toLocaleString()}</span>
                </div>
                <div class="task-detail-item">
                    <span>${t['status']}:</span>
                    <span>${t['processing']}</span>
                </div>
            `;
            
            resultsSection.classList.add('show');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // 任务历史相关变量
        let currentPage = 1;
        let totalPages = 1;
        let tasksPerPage = 10;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn !== 'true') {
                window.location.href = 'login';
                return;
            }

            // 显示用户信息
            const username = localStorage.getItem('username') || '未知用户';
            document.getElementById('username').textContent = username;
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
            
            // 初始化语言
            switchLanguage(currentLanguage);

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });

            // 加载任务历史
            loadTaskHistory();

            // 绑定刷新按钮事件
            document.getElementById('refresh-tasks-btn').addEventListener('click', function() {
                loadTaskHistory();
            });

        });


        // 加载任务历史
        async function loadTaskHistory(page = 1) {
            try {
                showTasksLoading();
                
                const response = await apiRequest(`/api/tasks?page=${page}&per_page=${tasksPerPage}&task_type=molmap`);
                
                if (response.success) {
                    displayTasks(response.tasks);
                    updatePagination(response.pagination);
                } else {
                    showNoTasks('加载任务历史失败: ' + response.message);
                }
            } catch (error) {
                console.error('加载任务历史时出错:', error);
                showNoTasks('加载任务历史失败: ' + error.message);
            }
        }

        // 显示任务加载状态
        function showTasksLoading() {
            const tasksList = document.getElementById('tasks-list');
            tasksList.innerHTML = `
                <div class="loading-tasks">
                    <div class="loading-spinner"></div>
                    <p>正在加载任务历史...</p>
                </div>
            `;
        }

        // 显示无任务状态
        function showNoTasks(message = '暂无ADMET预测任务') {
            const tasksList = document.getElementById('tasks-list');
            tasksList.innerHTML = `
                <div class="no-tasks">
                    <p>${message}</p>
                </div>
            `;
            
            // 隐藏分页
            document.getElementById('tasks-pagination').style.display = 'none';
        }

        // 显示任务列表
        function displayTasks(tasks) {
            const tasksList = document.getElementById('tasks-list');
            const t = translations[currentLanguage];
            
            if (!tasks || tasks.length === 0) {
                showNoTasks();
                return;
            }
            
            tasksList.innerHTML = tasks.map(task => {
                const statusClass = task.status || 'pending';
                const statusText = getStatusText(task.status);

                // 解析task.result字段
                let resultData = {};


                try {
                    if (task.result && typeof task.result === 'string') {
                        // console.log('原始task.result长度:', task.result.length);
                        // console.log('原始task.result前100字符:', task.result.substring(0, 100));
                        
                        // 尝试解析JSON字符串
                        let parsed;
                        try {
                            // 先尝试直接解析
                            parsed = JSON.parse(task.result);
                            // console.log('第一次JSON.parse结果类型:', typeof parsed);
                            
                            // 如果第一次解析返回的还是字符串，说明需要二次解析
                            if (typeof parsed === 'string') {
                                // console.log('第一次解析返回字符串，尝试二次解析...');
                                try {
                                    parsed = JSON.parse(parsed);
                                    // console.log('二次JSON.parse成功，结果类型:', typeof parsed);
                                } catch (secondError) {
                                    // console.warn('二次JSON.parse失败:', secondError);
                                    parsed = null;
                                }
                            }
                        } catch (parseError) {
                            // console.warn('第一次JSON.parse失败:', parseError);
                            // 尝试清理字符串后重新解析
                            const cleaned = task.result.trim().replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
                            try {
                                parsed = JSON.parse(cleaned);
                                // console.log('清理后JSON.parse成功，结果类型:', typeof parsed);
                                
                                // 如果清理后解析返回的还是字符串，也需要二次解析
                                if (typeof parsed === 'string') {
                                    // console.log('清理后解析返回字符串，尝试二次解析...');
                                    try {
                                        parsed = JSON.parse(parsed);
                                        // console.log('清理后二次JSON.parse成功，结果类型:', typeof parsed);
                                    } catch (secondCleanError) {
                                        // console.warn('清理后二次JSON.parse失败:', secondCleanError);
                                        parsed = null;
                                    }
                                }
                            } catch (cleanError) {
                                console.warn('清理后JSON.parse仍然失败:', cleanError);
                                parsed = null;
                            }
                        }
                        
                        // 检查解析后的结果类型
                        if (parsed !== null && typeof parsed === 'object' && !Array.isArray(parsed)) {
                            resultData = parsed;
                            // console.log('✅ 成功设置resultData');
                        } else {
                            // console.warn('解析后的结果不是普通对象:', parsed);
                            // console.warn('类型:', typeof parsed);
                            // console.warn('是否为数组:', Array.isArray(parsed));
                            resultData = {};
                        }
                    } else if (task.result && typeof task.result === 'object') {
                        resultData = task.result;
                    }
                } catch (e) {
                    console.warn('解析task.result失败:', e);
                    console.warn('原始task.result内容:', task.result);
                    resultData = {};
                }
                
                return `
                    <div class="task-item" onclick="viewTaskDetails('${task.task_id}')">
                        <div class="task-header">
                            <div>
                                <div class="task-title">${task.job_name || task.task_id}</div>
                                <div class="task-id">ID: ${task.task_id}</div>
                            </div>
                            <div class="task-status ${statusClass}">${statusText}</div>
                        </div>
                        
                        <div class="task-details">
                            <div class="task-detail-item">
                                <span class="task-detail-label">分子数量:</span>
                                <span class="task-detail-value">${resultData.molecule_count || 'N/A'}</span>
                            </div>
                            <div class="task-detail-item">
                                <span class="task-detail-label">提交时间:</span>
                                <span class="task-detail-value">${formatDateTime(task.created_at)}</span>
                            </div>
                            <div class="task-detail-item">
                                <span class="task-detail-label">完成时间:</span>
                                <span class="task-detail-value">${task.completed_at ? formatDateTime(task.completed_at) : 'N/A'}</span>
                            </div>
                        </div>
                        
                        <div class="task-actions">
                            <button class="btn btn-view-results" 
                                    onclick="event.stopPropagation(); viewTaskResults('${task.task_id}')"
                                    ${task.status !== 'completed' ? 'disabled' : ''}>
                                查看结果
                            </button>
                    ${task.status === 'completed' ? `
                        <button class="btn btn-download-all" 
                                onclick="event.stopPropagation(); downloadMolMapTaskResultsZip('${task.task_id}')"
                                style="background: #10b981; border-color: #10b981;">
                            📦 下载结果文件
                        </button>
                    ` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const t = translations[currentLanguage];
            const statusMap = {
                'completed': t['completed'] || '已完成',
                'running': t['processing'] || '处理中',
                'failed': t['failed'] || '失败',
                'pending': t['pending'] || '等待中'
            };
            return statusMap[status] || status;
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // 查看任务详情
        function viewTaskDetails(taskId) {
            console.log('查看任务详情:', taskId);
            // 这里可以实现任务详情弹窗或跳转
        }

        // 查看任务结果
        // 修改viewTaskResults函数，适配新的数据结构
        async function viewTaskResults(taskId) {
            try {
                const response = await apiRequest(`/api/molmap/tasks/${taskId}/results`);
                
                if (response.success && response.status === 'completed') {
                    // 显示结果
                    showMoleculeResults(response);
                    
                    // 滚动到结果区域
                    document.getElementById('resultsContainer').scrollIntoView({ 
                        behavior: 'smooth' 
                    });
                } else {
                    showNotification('任务尚未完成或获取结果失败', 'error');
                }
            } catch (error) {
                console.error('获取任务结果时出错:', error);
                showNotification('获取任务结果失败: ' + error.message, 'error');
            }
        }

        // 更新分页
        function updatePagination(pagination) {
            if (!pagination) {
                document.getElementById('tasks-pagination').style.display = 'none';
                return;
            }
            
            currentPage = pagination.page;
            totalPages = pagination.pages;
            
            if (totalPages <= 1) {
                document.getElementById('tasks-pagination').style.display = 'none';
                return;
            }
            
            document.getElementById('tasks-pagination').style.display = 'block';
            
            // 更新分页信息
            const infoText = `第 ${currentPage} 页，共 ${totalPages} 页 (总计 ${pagination.total} 个任务)`;
            document.getElementById('pagination-info-text').textContent = infoText;
            
            // 更新按钮状态
            document.getElementById('prev-page-btn').disabled = currentPage <= 1;
            document.getElementById('next-page-btn').disabled = currentPage >= totalPages;
            
            // 生成页码按钮
            generatePageNumbers();
        }

        // 生成页码按钮
        function generatePageNumbers() {
            const pageNumbers = document.getElementById('page-numbers');
            pageNumbers.innerHTML = '';
            
            const maxVisible = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            if (endPage - startPage < maxVisible - 1) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const button = document.createElement('button');
                button.className = `page-number-btn ${i === currentPage ? 'active' : ''}`;
                button.textContent = i;
                button.onclick = () => loadTaskHistoryPage(i);
                pageNumbers.appendChild(button);
            }
        }

        // 加载指定页面的任务历史
        function loadTaskHistoryPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                loadTaskHistory(page);
            }
        }


        // 退出登录
        function logout() {
            fetch('/api/logout', { 
                method: 'POST',
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('退出登录成功');
                })
                .catch(error => {
                    console.error('退出登录时发生错误:', error);
                })
                .finally(() => {
                    clearLocalStorage();
                    window.location.href = 'login';
                });
        }

        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('userId');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('lastAuthCheck');
        }


        // 全局变量
        let currentTaskId = null;
        let statusCheckInterval = null;


        // 开始定时查询任务状态
        function startStatusCheck(taskId) {
            // 清除之前的定时器
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            
            // 立即查询一次
            checkTaskStatus(taskId);
            
            // 设置定时查询（每5秒查询一次）
            statusCheckInterval = setInterval(() => {
                checkTaskStatus(taskId);
            }, 5000);
        }

        // 查询任务状态
        async function checkTaskStatus(taskId) {
            try {
                const response = await apiRequest(`/api/molmap/tasks/${taskId}/results`);
                
                if (response.success) {
                    updateTaskStatus(response);
                    
                    // 如果任务完成或失败，停止查询
                    if (response.status === 'completed' || response.status === 'failed') {
                        if (statusCheckInterval) {
                            clearInterval(statusCheckInterval);
                            statusCheckInterval = null;
                        }
                    }
                } else {
                    console.error('查询任务状态失败:', response.message);
                }
            } catch (error) {
                console.error('查询任务状态时出错:', error);
            }
        }

        // 更新任务状态显示
        function updateTaskStatus(response) {
            const t = translations[currentLanguage];
            
            // 更新状态指示器
            const statusIndicator = document.querySelector('.task-status-indicator');
            if (statusIndicator) {
                statusIndicator.className = `task-status-indicator ${response.status}`;
                
                let statusText = '';
                switch (response.status) {
                    case 'completed':
                        statusText = t['completed'] || '已完成';
                        break;
                    case 'running':
                        statusText = t['processing'] || '处理中';
                        break;
                    case 'failed':
                        statusText = t['failed'] || '失败';
                        break;
                    default:
                        statusText = response.status;
                }
                statusIndicator.textContent = statusText;
            }
            
            // 如果任务完成，显示结果
            if (response.status === 'completed' && response.result_files && response.result_files.length > 0) {
                showMoleculeResults(response);
            }
        }

        let currentResponseData = null; // 添加全局变量存储当前响应数据

        // 修改showMoleculeResults函数，支持多个分子选择
        function showMoleculeResults(response) {
            currentResponseData = response;
            const resultsContainer = document.getElementById('resultsContainer');
            const moleculeResults = document.getElementById('moleculeResults');
            const t = translations[currentLanguage];
            
            // 清空之前的结果
            moleculeResults.innerHTML = '';
            
            // console.log('显示分子结果:', response);
            
            // 处理results数据结构
            let moleculesData = [];
            
            if (response.results) {
                // 检查是否为多个分子还是单个分子
                if (Array.isArray(response.smiles) && response.smiles.length > 1) {
                    // 多个分子的情况 - 从字典中提取
                    if (typeof response.results === 'object' && !Array.isArray(response.results)) {
                        Object.keys(response.results).forEach((moleculeKey, index) => {
                            const moleculeData = response.results[moleculeKey];
                            let smiles = '';
                            
                            // 获取对应的SMILES
                            if (response.smiles && response.smiles.length > index) {
                                smiles = response.smiles[index];
                            } else {
                                smiles = `分子 ${index + 1}`;
                            }
                            
                            moleculesData.push({
                                molecule_key: moleculeKey,
                                data: moleculeData,
                                smiles: smiles,
                                index: index
                            });
                        });
                    }
                } else {
                    // 单个分子的情况 - results直接作为ADMET数据
                    const smiles = Array.isArray(response.smiles) ? response.smiles[0] : response.smiles;
                    moleculesData.push({
                        molecule_key: 'molecule_0',
                        data: response.results,
                        smiles: smiles || 'N/A',
                        index: 0
                    });
                }
            }
            
            console.log('处理后的分子数据:', moleculesData);
            
            // 如果没有分子数据，显示提示
            if (moleculesData.length === 0) {
                moleculeResults.innerHTML = `
                    <div class="no-results">
                        <p>暂无分子结果数据</p>
                    </div>
                `;
                resultsContainer.classList.add('show');
                return;
            }
            
            // 添加分子选择器（如果有多个分子）
            if (response.smiles.length > 1) {
                const selectorDiv = document.createElement('div');
                selectorDiv.className = 'molecule-selector';
                selectorDiv.innerHTML = `
                    <h4>选择要查看的分子：</h4>
                    <div class="molecule-tabs">
                        ${moleculesData.map((molecule, index) => `
                            <button class="molecule-tab ${index === 0 ? 'active' : ''}" 
                                    onclick="selectMolecule(${index})">
                                ${molecule.molecule_key}
                            </button>
                        `).join('')}
                    </div>
                `;
                moleculeResults.appendChild(selectorDiv);
            }

            // 添加当前选中分子的SMILES显示区域
            const smilesDisplayDiv = document.createElement('div');
            smilesDisplayDiv.className = 'current-smiles-display';
            smilesDisplayDiv.id = 'current-smiles-display';
            
            // 根据是否有多个分子来决定显示方式
            let initialSmiles = '';
            if (moleculesData.length > 1 && response.smiles && response.smiles.length > 0) {
                // 多个分子，使用smiles_list
                initialSmiles = response.smiles[0] || 'N/A';
            } else {
                // 单个分子，使用smiles
                initialSmiles = response.smiles || 'N/A';
            }
            

            
            // 为每个分子创建结果项（默认只显示第一个）
            moleculesData.forEach((molecule, index) => {
                const moleculeDiv = document.createElement('div');
                moleculeDiv.className = `molecule-item ${index === 0 ? 'active' : 'hidden'}`;
                moleculeDiv.id = `molecule-${index}`;
                
                // 计算ADMET属性数量
                const admetProperties = Object.keys(molecule.data).filter(key => {
                    const propData = molecule.data[key];
                    return propData && 
                        typeof propData === 'object' && 
                        propData.description !== undefined && 
                        propData.unit !== undefined && 
                        propData.value !== undefined;
                });
                
                moleculeDiv.innerHTML = `
                    <div class="molecule-header">
                        <div class="molecule-name">${molecule.molecule_key}</div>
                        <div class="molecule-info">
                            <span class="admet-count">${admetProperties.length} 个ADMET属性</span>
                            <button class="view-details-btn" onclick="toggleMoleculeDetails('${molecule.molecule_key}', ${index})">
                                查看详情
                        </button>
                        </div>
                    </div>
                    <div class="smiles-display" id="smiles-${index}">
                        ${molecule.smiles}
                    </div>
                    <div class="molecule-details" id="details-${molecule.molecule_key}" style="display: none;">
                        <div class="admet-table-container">
                            ${generateADMETTable(molecule.data)}
                        </div>
                    </div>
                `;
                
                moleculeResults.appendChild(moleculeDiv);
            });
            
            resultsContainer.classList.add('show');
        }

        // 修复generateADMETTable函数，正确识别ADMET属性
        function generateADMETTable(moleculeData) {
            // console.log('生成ADMET表格，输入数据:', moleculeData);
            
            // 正确识别ADMET属性：检查是否有description、unit、value字段
            const admetProperties = Object.keys(moleculeData).filter(key => {
                const propData = moleculeData[key];
                return propData && 
                    typeof propData === 'object' && 
                    propData.description !== undefined && 
                    propData.unit !== undefined && 
                    propData.value !== undefined;
            });
            
            // console.log('识别到的ADMET属性:', admetProperties);
            
            if (admetProperties.length === 0) {
                return '<p>暂无ADMET属性数据</p>';
            }
            
            let tableHtml = `
                <table class="admet-table">
                    <thead>
                        <tr>
                            <th>属性名称</th>
                            <th>描述</th>
                            <th>数值</th>
                            <th>单位</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            admetProperties.forEach(property => {
                const propData = moleculeData[property];
                const value = propData.value;
                const description = propData.description || '';
                const unit = propData.unit || '';
                
                // 格式化数值显示
                let formattedValue = value;
                if (typeof value === 'number') {
                    if (value < 0.01 || value > 1000) {
                        formattedValue = value.toExponential(3);
                    } else {
                        formattedValue = value.toFixed(4);
                    }
                } else if (typeof value === 'string') {
                    // 如果是字符串形式的数字，尝试转换为数字
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue)) {
                        if (numValue < 0.01 || numValue > 1000) {
                            formattedValue = numValue.toExponential(3);
                        } else {
                            formattedValue = numValue.toFixed(4);
                        }
                    }
                }
                
                // 根据属性类型添加颜色标识
                let rowClass = '';
                if (property.includes('CYP') || property.includes('hERG')) {
                    rowClass = 'cyp-row';
                } else if (property.includes('AMES') || property.includes('Carcinogens') || property.includes('DILI') || property.includes('LD50')) {
                    rowClass = 'toxicity-row';
                } else if (property.includes('Bioavailability') || property.includes('HIA') || property.includes('Caco2') || property.includes('PPBR')) {
                    rowClass = 'absorption-row';
                } else if (property.includes('MolWeight') || property.includes('MolLogP') || property.includes('TPSA') || property.includes('MolFraction') || property.includes('Num')) {
                    rowClass = 'property-row';
                } else if (property.includes('NR-') || property.includes('SR-')) {
                    rowClass = 'receptor-row';
                }
                
                tableHtml += `
                    <tr class="${rowClass}">
                        <td class="property-name">${property}</td>
                        <td class="property-description">${description}</td>
                        <td class="property-value">${formattedValue}</td>
                        <td class="property-unit">${unit}</td>
                    </tr>
                `;
            });
            
            tableHtml += `
                    </tbody>
                </table>
            `;
            
            return tableHtml;
        }

        // 切换分子详情显示
        function toggleMoleculeDetails(moleculeKey, index) {
            const detailsDiv = document.getElementById(`details-${moleculeKey}`);
            const button = event.target;
            
            if (detailsDiv.style.display === 'none') {
                detailsDiv.style.display = 'block';
                button.textContent = '隐藏详情';
                button.classList.add('active');
                
                // 滚动到详情区域
                detailsDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            } else {
                detailsDiv.style.display = 'none';
                button.textContent = '查看详情';
                button.classList.remove('active');
            }
        }


        // 添加分子选择函数
        function selectMolecule(index) {
            // 隐藏所有分子
            document.querySelectorAll('.molecule-item').forEach(item => {
                item.classList.remove('active');
                item.classList.add('hidden');
            });
            
            // 显示选中的分子
            const selectedMolecule = document.getElementById(`molecule-${index}`);
            if (selectedMolecule) {
                selectedMolecule.classList.remove('hidden');
                selectedMolecule.classList.add('active');
            }
            
            // 更新标签状态
            document.querySelectorAll('.molecule-tab').forEach((tab, tabIndex) => {
                tab.classList.toggle('active', tabIndex === index);
            });

            // 更新SMILES显示 - 根据选择的分子索引
            if (currentResponseData) {
                let selectedSmiles = '';
                
                if (currentResponseData.smiles && Array.isArray(currentResponseData.smiles) && currentResponseData.smiles.length > index) {
                    // 多个分子，从smiles数组中获取对应索引的SMILES
                    selectedSmiles = currentResponseData.smiles[index];
                } else if (currentResponseData.smiles) {
                    // 单个分子，直接使用smiles
                    selectedSmiles = currentResponseData.smiles;
                } else {
                    selectedSmiles = `分子 ${index + 1} 的SMILES`;
                }
                
                // 直接更新对应分子的smiles-display元素
                const smilesDisplay = document.getElementById(`smiles-${index}`);
                if (smilesDisplay) {
                    smilesDisplay.textContent = selectedSmiles;
                }
            }
            
            // 滚动到选中的分子
            if (selectedMolecule) {
                selectedMolecule.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        }


        // 修复downloadMolMapTaskResultsZip函数中的await语法错误
        async function downloadMolMapTaskResultsZip(taskId) {
            console.log('=== Download MolMap Task Results ZIP ===');
            console.log('Task ID:', taskId);
            
            const t = translations[currentLanguage];
            
            // 显示加载提示
            const loadingNotification = document.createElement('div');
            loadingNotification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 4px;">📦 准备下载任务结果</div>
                <div style="font-size: 12px;">任务ID: ${taskId}</div>
                <div style="font-size: 12px;">正在获取文件列表...</div>
            `;
            loadingNotification.style.position = 'fixed';
            loadingNotification.style.top = '20px';
            loadingNotification.style.right = '20px';
            loadingNotification.style.background = '#3b82f6';
            loadingNotification.style.color = 'white';
            loadingNotification.style.padding = '12px 16px';
            loadingNotification.style.borderRadius = '8px';
            loadingNotification.style.zIndex = '10000';
            loadingNotification.style.fontSize = '13px';
            loadingNotification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
            loadingNotification.style.maxWidth = '300px';
            
            document.body.appendChild(loadingNotification);
            
            try {
                // 获取任务结果
                const response = await fetch(`/api/molmap/tasks/${taskId}/results`);
                const data = await response.json();
                
                // 移除加载提示
                if (document.body.contains(loadingNotification)) {
                    document.body.removeChild(loadingNotification);
                }
                
                let resultFiles = [];

                if (data.result_files) {
                    if (Array.isArray(data.result_files)) {
                        // 如果是数组格式
                        resultFiles = data.result_files;
                    } else if (typeof data.result_files === 'object') {
                        // 如果是对象格式（MolMap的格式），提取所有文件URL
                        resultFiles = Object.values(data.result_files).filter(url => url && typeof url === 'string');
                    }
                }


                if (resultFiles.length > 0) {
                    const totalFiles = resultFiles.length;
                    
                    console.log(`准备下载任务 ${taskId} 的所有结果: ${totalFiles} 个文件`);
                    
                    // 显示下载进度提示
                    const notification = document.createElement('div');
                    notification.innerHTML = `
                        <div style="font-weight: bold; margin-bottom: 4px;">📦 下载任务结果</div>
                        <div style="font-size: 12px;">任务ID: ${taskId}</div>
                        <div style="font-size: 12px;">正在下载 ${totalFiles} 个文件...</div>
                    `;
                    notification.style.position = 'fixed';
                    notification.style.top = '20px';
                    notification.style.right = '20px';
                    notification.style.background = '#f57c00';
                    notification.style.color = 'white';
                    notification.style.padding = '12px 16px';
                    notification.style.borderRadius = '8px';
                    notification.style.zIndex = '10000';
                    notification.style.fontSize = '13px';
                    notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                    notification.style.maxWidth = '300px';
                    
                    document.body.appendChild(notification);
                    
                    // 显示详细的下载进度
                    const progressNotification = document.createElement('div');
                    progressNotification.innerHTML = `
                        <div style="font-size: 12px; opacity: 0.9;">下载进度: 0/${totalFiles}</div>
                        <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; margin-top: 4px;">
                            <div id="molmap-progress-bar" style="width: 0%; height: 100%; background: white; border-radius: 2px; transition: width 0.2s;"></div>
                        </div>
                    `;
                    progressNotification.style.position = 'fixed';
                    progressNotification.style.top = '110px';
                    progressNotification.style.right = '20px';
                    progressNotification.style.background = '#e65100';
                    progressNotification.style.color = 'white';
                    progressNotification.style.padding = '8px 12px';
                    progressNotification.style.borderRadius = '6px';
                    progressNotification.style.zIndex = '10000';
                    progressNotification.style.fontSize = '12px';
                    
                    document.body.appendChild(progressNotification);
                    
                    // 模拟打包进度更新
                    let packingProgress = 0;
                    const progressInterval = setInterval(async () => {
                        packingProgress++;
                        const progressPercent = (packingProgress / totalFiles) * 80; // 打包进度占80%
                        
                        // 更新进度文字和进度条
                        const progressText = progressNotification.querySelector('div');
                        const progressBar = document.getElementById('molmap-progress-bar');
                        
                        if (progressText) {
                            progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">打包进度: ${packingProgress}/${totalFiles} 个文件</div>`;
                        }
                        if (progressBar) {
                            progressBar.style.width = `${progressPercent}%`;
                        }
                        
                        if (packingProgress >= totalFiles) {
                            clearInterval(progressInterval);
                            
                            // 开始调用压缩包API
                            if (progressText) {
                                progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">正在生成压缩包...</div>`;
                            }
                            
                            try {
                                // 调用后端API创建压缩包
                                const zipResponse = await fetch(`/api/tasks/${taskId}/download-zip`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                });
                                
                                if (!zipResponse.ok) {
                                    throw new Error(`HTTP ${zipResponse.status}: ${zipResponse.statusText}`);
                                }
                                
                                const zipData = await zipResponse.json();


                                // 添加调试信息
                                console.log('压缩包API返回数据:', zipData);
                                console.log('zipData.success:', zipData.success);
                                console.log('zipData.download_url:', zipData.download_url);
                                console.log('zipData.message:', zipData.message);
                                
                                // 完成进度到100%
                                if (progressBar) {
                                    progressBar.style.width = '100%';
                                }
                                if (progressText) {
                                    progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">✅ 压缩包创建完成!</div>`;

                                    // 2秒后自动隐藏进度通知
                                    setTimeout(() => {
                                        if (document.body.contains(progressNotification)) {
                                            document.body.removeChild(progressNotification);
                                        }
                                    }, 2000);
                                }
                                
                                if (zipData.success && zipData.download_url) {
                                    // 更新主通知
                                    notification.innerHTML = `
                                        <div style="font-weight: bold; margin-bottom: 4px;">📦 开始下载压缩包</div>
                                        <div style="font-size: 12px;">文件: ${zipData.filename || `molmap_task_${taskId}_results.zip`}</div>
                                        <div style="font-size: 12px;">包含: ${totalFiles} 个结果文件</div>
                                        ${zipData.file_size ? `<div style="font-size: 12px;">大小: ${zipData.file_size}</div>` : ''}
                                    `;
                                    
                                    // 创建下载链接并触发下载
                                    const link = document.createElement('a');
                                    link.href = zipData.download_url;
                                    link.download = zipData.filename || `molmap_task_${taskId}_results.zip`;
                                    link.style.display = 'none';
                                    
                                    document.body.appendChild(link);
                                    link.click();
                                    document.body.removeChild(link);
                                    
                                    console.log('压缩包下载开始:', zipData.filename);
                                    
                                    // 显示成功通知
                                    // showNotification(`开始下载任务 ${taskId} 的结果压缩包 (${totalFiles}个文件)`, 'success');
                                    
                } else {
                                    throw new Error(zipData.message || '创建压缩包失败');
                                }
                            } catch (zipError) {
                                console.error('创建压缩包失败:', zipError);
                                
                                if (progressText) {
                                    progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">❌ 创建压缩包失败</div>`;
                                }
                                
                                // 根据错误类型显示不同的提示
                                let errorMessage = '创建压缩包失败，请稍后重试';
                                if (zipError.message.includes('404')) {
                                    errorMessage = '该任务的结果文件不可用';
                                } else if (zipError.message.includes('500')) {
                                    errorMessage = '服务器创建压缩包时出错';
                                }
                                
                                showNotification(errorMessage, 'error');
                            }
                        }
                    }, 100); // 快速更新进度，让用户看到进展
                    
                    // 8秒后移除通知（给足够时间完成整个流程）
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                        if (document.body.contains(progressNotification)) {
                            document.body.removeChild(progressNotification);
                        }
                    }, 8000);
                    
                } else {
                    showNotification('该任务暂无可下载的结果文件', 'warning');
                }
            } catch (error) {
                // 移除加载提示
                if (document.body.contains(loadingNotification)) {
                    document.body.removeChild(loadingNotification);
                }
                
                console.error('获取任务结果失败:', error);
                showNotification('获取任务结果失败: ' + error.message, 'error');
            }
        }



    </script>
{% endblock %}