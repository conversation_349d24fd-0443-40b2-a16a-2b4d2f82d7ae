<template>
  <div class='terms-page'>
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'zh' }" 
        @click="switchLanguage('zh')"
      >
        🇨🇳 中文
      </button>
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'en' }" 
        @click="switchLanguage('en')"
      >
        🇺🇸 EN
      </button>
    </div>

    <div class="agreement-container">
      <div class="header">
        <div class="logo">
          <div class="logo-icon"></div>
          <span>{{ t('terms.platform-name') }}</span>
        </div>
        <p class="subtitle">{{ t('terms.platform-subtitle') }}</p>
      </div>
      
      <div class="content">
        <div class="section">
          <h2 class="section-title">{{ t('terms.agreement-title') }}</h2>
          <p>{{ t('terms.agreement-intro') }}</p>
        </div>

        <div class="section">
          <h2 class="section-title">{{ t('terms.service-description-title') }}</h2>
          <p>{{ t('terms.service-description-intro') }}</p>
          <div class="subsection">
            <p>{{ t('terms.service-item-1') }}</p>
            <p>{{ t('terms.service-item-2') }}</p>
            <p>{{ t('terms.service-item-3') }}</p>
            <p>{{ t('terms.service-item-4') }}</p>
          </div>
        </div>
        
        <div class="section">
          <h2 class="section-title">{{ t('terms.user-responsibility-title') }}</h2>
          <div class="subsection">
            <p>{{ t('terms.responsibility-item-1') }}</p>
            <p>{{ t('terms.responsibility-item-2') }}</p>
            <p>{{ t('terms.responsibility-item-3') }}</p>
            <p>{{ t('terms.responsibility-item-4') }}</p>
          </div>
        </div>

        <div class="highlight-box">
          <h3 class="highlight-title">{{ t('terms.disclaimer-title') }}</h3>
          <div class="subsection">
            <p>{{ t('terms.disclaimer-item-1') }}</p>
            <p>{{ t('terms.disclaimer-item-2') }}</p>
            <p>{{ t('terms.disclaimer-item-3') }}</p>
            <p>{{ t('terms.disclaimer-item-4') }}</p>
            <p>{{ t('terms.disclaimer-item-5') }}</p>
          </div>
        </div>
        
        <div class="section">
          <h2 class="section-title">{{ t('terms.data-processing-title') }}</h2>
          <div class="subsection">
            <p v-html="t('terms.data-processing-item-1')"></p>
            <p>{{ t('terms.data-processing-item-2') }}</p>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">{{ t('terms.intellectual-property-title') }}</h2>
          <p>{{ t('terms.intellectual-property-content') }}</p>
        </div>

        <div class="section">
          <h2 class="section-title">{{ t('terms.agreement-update-title') }}</h2>
          <p>{{ t('terms.agreement-update-content') }}</p>
        </div>
      </div>

      <div class="footer">
        <p>{{ t('terms.footer-copyright') }}</p>
        <p>{{ t('terms.footer-contact') }}</p>
      </div>
    </div>
  </div>

</template>

<script setup>
import { onMounted, watch } from 'vue'
import { t, switchLanguage, currentLanguage } from '../utils/i18n'

// 页面标题更新
const updatePageTitle = () => {
  document.title = t('terms.page-title')
}

// 监听语言变化，更新页面标题
watch(currentLanguage, () => {
  updatePageTitle()
}, { immediate: true })

onMounted(() => {
  updatePageTitle()
})
</script>

<style scoped>
@import '../assets/styles/terms-of-service.css';

/* 确保页面能够正确显示和滚动 */
.terms-page {
  position: relative;
  z-index: 1;
}
</style>