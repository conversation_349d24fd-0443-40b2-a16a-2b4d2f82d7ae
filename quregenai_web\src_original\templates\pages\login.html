<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - QureGenAI 药物设计平台</title>
    <link rel="icon" type="image/png" href="/src/images/icon.png">
    <link rel="stylesheet" href="/src/styles/pages/login.css">
</head>
<body>
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
        <button class="language-btn" data-lang="zh" onclick="switchLanguage('zh')">
            🇨🇳 中文
        </button>
        <button class="language-btn active" data-lang="en" onclick="switchLanguage('en')">
            🇺🇸 EN
        </button>
    </div>

    <div class="login-container">
        <div class="login-header">
            <h1 data-i18n="title">QureGenAI Drug Design & TyxonQ Quantum Computing Platform</h1>
            <p data-i18n="subtitle">Please enter your account information to continue</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="contactInfo" data-i18n="mobile-label">Mobile Number/Email</label>
                <input type="text" id="contactInfo" name="contactInfo" required data-i18n-placeholder="mobile-placeholder" placeholder="请输入手机号码/邮箱">
                <!-- <label for="mobile" data-i18n="mobile-label">手机号码</label>
                <input type="tel" id="mobile" name="mobile" required data-i18n-placeholder="mobile-placeholder" placeholder="请输入手机号码"> -->
            </div>
            
            <div class="form-group">
                <!-- <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码"> -->

                <label for="password" data-i18n="password-label">Password</label>
                <input type="password" id="password" name="password" required data-i18n-placeholder="password-placeholder" placeholder="Enter your password">
                <div style="text-align: right; margin-top: 0.5rem;">
                    <a href="reset_password" style="color: #667eea; text-decoration: none; font-size: 0.875rem;" data-i18n="reset-password">Forgot your password?</a>
                </div>
            </div>
            
            <div class="error-message" id="errorMessage">
                <span data-i18n="login-error">Mobile number or password is incorrect, please try again</span>
            </div>
            
            <button type="submit" class="login-btn" data-i18n="login-btn">Login</button>
            
            <div class="auth-divider">
                <span data-i18n="no-account">Don't have an account?</span>
            </div>
            
            <button type="button" class="register-btn" onclick="goToRegister()" data-i18n="register-btn">Register New Account</button>
        </form>
        
        <div class="features">
            <h3 data-i18n="features-title">Platform Features</h3>
            <div class="feature-list">
                <span class="feature-item" data-i18n="feature-hts">High-throughput Screening</span>
                <span class="feature-item" data-i18n="feature-pocket">Pocket Discovery</span>
                <span class="feature-item" data-i18n="feature-folding">Protein Folding</span>
                <span class="feature-item" data-i18n="feature-quantum">Quantum Computing</span>
                <span class="feature-item" data-i18n="feature-ai-mcp">AI Models & MCP Services</span>
            </div>
        </div>
    </div>

    <script>
        // 多语言文本配置
        const translations = {
            zh: {
                'title': 'QureGenAI 药物设计 & TyxonQ 量子计算平台',
                'subtitle': '请输入您的账户信息以继续',
                'mobile-label': '手机号码/邮箱',
                'mobile-placeholder': '请输入手机号码/邮箱',
                'password-label': '密码',
                'password-placeholder': '请输入密码',
                'login-error': '手机号码或密码错误，请重试',
                'login-btn': '登录',
                'no-account': '还没有账户？',
                'register-btn': '注册新账户',
                'features-title': '平台功能',
                'feature-hts': '高通量筛选',
                'feature-pocket': '口袋发现',
                'feature-folding': '蛋白质折叠',
                'feature-quantum': '量子计算',
                'feature-ai-mcp': 'AI模型与MCP服务',
                'mobile-invalid': '请输入有效的手机号码',
                'network-error': '发生网络错误，请稍后重试',
                'login-failed': '登录失败，请重试',
                'reset-password': '忘记密码？'
            },
            en: {
                'title': 'QureGenAI Drug Design & TyxonQ Quantum Computing Platform',
                'subtitle': 'Please enter your account information to continue',
                'mobile-label': 'Mobile Number/Email',
                'mobile-placeholder': 'Enter your mobile number/email',
                'password-label': 'Password',
                'password-placeholder': 'Enter your password',
                'login-error': 'Mobile number or password is incorrect, please try again',
                'login-btn': 'Login',
                'no-account': 'Don\'t have an account?',
                'register-btn': 'Register New Account',
                'features-title': 'Platform Features',
                'feature-hts': 'High-throughput Screening',
                'feature-pocket': 'Pocket Discovery',
                'feature-folding': 'Protein Folding',
                'feature-quantum': 'Quantum Computing',
                'feature-ai-mcp': 'AI Models & MCP Services',
                'mobile-invalid': 'Please enter a valid mobile number',
                'network-error': 'Network error occurred, please try again later',
                'login-failed': 'Login failed, please try again',
                'reset-password': 'Forgot your password?'
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });

            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';

            // 更新文档标题
            document.title = lang === 'zh' ? '登录 - 分子设计平台' : 'Login - Molecular Design Platform';

            // 更新页面文本
            updatePageText(lang);
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];

            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t[key]) {
                    element.textContent = t[key];
                }
            });

            // 更新占位符文本
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (t[key]) {
                    element.placeholder = t[key];
                }
            });
        }


        // 验证手机号码或邮箱格式
        function validateContact(contact) {
            const mobileRegex = /^1[3-9]\d{9}$/;
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return mobileRegex.test(contact) || emailRegex.test(contact);
        }

        // 清除本地存储（如果有残留的登录状态）
        function clearStaleLoginState() {
            // 只清除客户端存储，不发送请求
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('userId');
            localStorage.removeItem('loginTime');
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            clearStaleLoginState();

            // 初始化语言
            switchLanguage(currentLanguage);
        });

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const contactInfo = document.getElementById('contactInfo').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            const t = translations[currentLanguage];

            // 验证手机号码或邮箱格式
            if (!validateContact(contactInfo)) {
                errorMessage.textContent = '请输入有效的手机号码或邮箱地址';
                errorMessage.style.display = 'block';
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
                return;
            }
            
            fetch('/api/weblogin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ contactInfo, password }),
                credentials: 'include'  // 确保包含cookie
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.loginStatus) {
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('username', data.user.mobile);
                    localStorage.setItem('userId', data.user.user_id);
                    localStorage.setItem('loginTime', new Date().toISOString());
                    window.location.href = 'home';
                } else {
                    errorMessage.querySelector('[data-i18n="login-error"]').textContent = data.message || t['login-failed'];
                    errorMessage.style.display = 'block';
                    setTimeout(() => {
                        errorMessage.style.display = 'none';
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                errorMessage.querySelector('[data-i18n="login-error"]').textContent = t['network-error'];
                errorMessage.style.display = 'block';
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
            });
        });

        function goToRegister() {
            window.location.href = 'sign';
        }
    </script>
</body>
</html>