<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketVina 任务详情 - QureGenAI 药物设计平台</title>
    <link rel="icon" type="image/png" href="/src/images/icon.png">
    <!-- 在head部分添加3Dmol.js库 -->
    <script src="/src/scripts/3dmol.js"></script>
    <link rel="stylesheet" href="/src/styles/pages/pocketvina_detail.css">
</head>
<body>
    <header class="header">
        <a href="home" style="text-decoration: none;"><div class="logo">QureGenAI 药物设计平台</div></a>
        <a href="pocketvina" class="back-btn">
            ← 返回 PocketVina
        </a>
    </header>

    <div class="content">
        <div id="loadingContainer" class="loading">
            <div>⏳ 加载任务详情中...</div>
        </div>

        <div id="errorContainer" class="error" style="display: none;">
            <div>❌ 加载任务详情失败</div>
        </div>

        <div id="taskContainer" style="display: none;">
            <div class="page-title">
                <div>🎯</div>
                <div>
                    <h1 id="taskTitle">PocketVina 任务详情</h1>
                </div>
            </div>

            <div class="task-info-card">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">任务状态</div>
                        <div class="info-value">
                            <div id="taskStatus" class="status-badge">-</div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">分子数量</div>
                        <div class="info-value" id="moleculeCount">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">蛋白质文件</div>
                        <div class="info-value" id="proteinFiles">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">完成时间</div>
                        <div class="info-value" id="completeTime">-</div>
                    </div>
                </div>
            </div>

           <!-- 3D分子渲染区域 -->
           <div class="molecule-rendering-section">
            <div class="section-title">
                🧬 3D 分子渲染
            </div>
            
            <div class="rendering-container">
                <div class="viewer-container">
                    <div class="viewer-controls">
                        <button class="control-btn" onclick="clearAllRendering()">
                            🗑️ 清空所有渲染
                        </button>
                    </div>
                    <div id="moleculeViewer" class="molecule-viewer">
                        <div class="viewer-placeholder">
                            <div class="viewer-placeholder-icon">🧬</div>
                            <p>选择结构文件进行3D渲染</p>
                            <p style="font-size: 0.75rem;">支持 PDB 和 PDBQT 格式</p>
                        </div>
                    </div>
                </div>
                
                <div class="file-selector">
                    <h4>🗂️ 结构文件列表</h4>
                    <div id="fileTabsContainer">
                        <!-- 标签页将在这里动态生成 -->
                    </div>
                    <div id="structureFilesList">
                        <!-- 文件列表将在这里动态生成 -->
                    </div>
                </div>
            </div>


            <div class="files-section">
                <div class="section-title">
                    📁 结果文件
                </div>
                
                <!-- 文件分类显示 -->
                <div class="files-categories">
                    <!-- 汇总文件 -->
                    <div class="file-category">
                        <h4 class="category-title">📊 汇总文件</h4>
                        <div id="summaryFilesContainer" class="category-files">
                            <!-- 汇总文件将在这里显示 -->
                        </div>
                    </div>

                    <!-- 蛋白质相关文件 -->
                    <div class="file-category">
                        <h4 class="category-title">🧬 蛋白质文件</h4>
                        <div id="proteinFilesContainer" class="category-files">
                            <!-- 蛋白质文件将在这里显示 -->
                        </div>
                    </div>

                    <!-- 对接结果文件 -->
                    <div class="file-category">
                        <h4 class="category-title">🎯 对接结果</h4>
                        <div id="dockingFilesContainer" class="category-files">
                            <!-- 对接结果文件将在这里显示 -->
                        </div>
                    </div>

                    <!-- 日志文件 -->
                    <div class="file-category">
                        <h4 class="category-title">📋 日志文件</h4>
                        <div id="logFilesContainer" class="category-files">
                            <!-- 日志文件将在这里显示 -->
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button id="downloadAllBtn" class="download-all-btn">
                        📦 打包下载所有文件
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let viewer3d = null;
        let selectedFiles = new Set();
        let loadedModels = new Map(); // 存储已加载的模型信息

        

        // 获取URL参数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // 加载任务详情
        async function loadTaskDetail(taskId) {
            try {
                const response = await fetch(`/api/pocketvina/tasks/${taskId}/results`, {
                    method: 'GET',
                    credentials: 'include'
                });

                const data = await response.json();

                if (data.success) {
                    displayTaskDetail(data);
                } else {
                    showError(data.message || '加载任务详情失败');
                }
            } catch (error) {
                console.error('加载任务详情失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 显示任务详情
        function displayTaskDetail(data) {
            document.getElementById('loadingContainer').style.display = 'none';
            document.getElementById('taskContainer').style.display = 'block';
            // 更新任务基本信息
            document.getElementById('taskTitle').textContent = `任务 ${data.task_id}`;
            
            const statusElement = document.getElementById('taskStatus');
            statusElement.textContent = data.status === 'completed' ? '已完成' : 
                                      data.status === 'failed' ? '失败' : '运行中';
            statusElement.className = `status-badge status-${data.status}`;

            // 更新任务统计信息
            const summary = data.summary || {};
            document.getElementById('moleculeCount').textContent = summary.total_molecules || '-';
            document.getElementById('proteinFiles').textContent = 
                Array.isArray(summary.protein_files) ? summary.protein_files.join(', ') : '-';
            document.getElementById('completeTime').textContent = 
                summary.complete_time ? new Date(summary.complete_time).toLocaleString() : '-';

            // 显示结果文件
            displayResultFiles(data.result_files || []);

            // 显示3D渲染文件列表
            displayStructureFiles(data.result_files || []);
            
            // 设置打包下载按钮
            setupDownloadAll(data.task_id, data.result_files || []);
        }

        // 显示结果文件
        function displayResultFiles(files) {
            const summaryContainer = document.getElementById('summaryFilesContainer');
            const proteinContainer = document.getElementById('proteinFilesContainer');
            const dockingContainer = document.getElementById('dockingFilesContainer');
            const logContainer = document.getElementById('logFilesContainer');
            
            if (files.length === 0) {
                const noFilesMessage = '<div class="no-files-category">暂无文件</div>';
                summaryContainer.innerHTML = noFilesMessage;
                proteinContainer.innerHTML = noFilesMessage;
                dockingContainer.innerHTML = noFilesMessage;
                logContainer.innerHTML = noFilesMessage;
                return;
            }

            // 文件分类
            const summaryFiles = [];
            const proteinFiles = [];
            const dockingFiles = [];
            const logFiles = [];

            files.forEach(file => {
                if (file.category === 'summary' || file.name.includes('combined_results')) {
                    summaryFiles.push(file);
                } else if (file.category === 'pocket_info' || file.category === 'protein_structure') {
                    proteinFiles.push(file);
                } else if (file.category === 'docking_results') {
                    dockingFiles.push(file);
                } else if (file.category === 'log' || file.type === 'log') {
                    logFiles.push(file);
                } else {
                    // 默认归类到对接结果
                    dockingFiles.push(file);
                }
            });

            // 渲染各类别文件
            summaryContainer.innerHTML = renderFileCategory(summaryFiles, true) || // 启用预览功能
                '<div class="no-files-category">暂无汇总文件</div>';
            proteinContainer.innerHTML = renderFileCategory(proteinFiles) || 
                '<div class="no-files-category">暂无蛋白质文件</div>';
            dockingContainer.innerHTML = renderFileCategory(dockingFiles) || 
                '<div class="no-files-category">暂无对接结果文件</div>';
            logContainer.innerHTML = renderFileCategory(logFiles) || 
                '<div class="no-files-category">暂无日志文件</div>';
        }

        // 渲染文件类别
        function renderFileCategory(files, enablePreview = false) {
            if (files.length === 0) return '';
            
            return files.map((file, index) => {
                const typeClass = file.type.toLowerCase();
                const fileId = `file-${Date.now()}-${index}`;
                
                // 检查是否为CSV文件且需要预览功能
                const isCsvFile = file.type.toLowerCase() === 'csv';
                const showPreview = enablePreview && isCsvFile;


                return `
                    <div class="file-item">
                        <div class="file-name">${file.name}</div>
                        <div class="file-meta">
                            <span class="file-type ${typeClass}">${file.type}</span>
                            <div class="file-actions">
                                ${showPreview ? `
                                    <button class="preview-btn" onclick="previewCsvFile('${file.url}', '${fileId}', '${file.name}')">
                                        👁️ 预览
                                    </button>
                                ` : ''}
                                <a href="${file.url}" class="download-btn" target="_blank">
                                    ⬇️ 下载
                                </a>
                            </div>
                        </div>
                        ${showPreview ? `
                            <div id="preview-${fileId}" class="csv-preview" style="display: none;">
                                <div class="preview-header">
                                    <span>预览: ${file.name}</span>
                                    <button class="close-preview-btn" onclick="closeCsvPreview('${fileId}')">✕</button>
                                </div>
                                <div class="preview-content">
                                    <div class="loading-preview">正在加载预览...</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // 获取文件类型颜色
        function getFileTypeColor(type) {
            const colors = {
                'json': '#fef3c7',
                'pdbqt': '#dbeafe',
                'csv': '#d1fae5',
                'log': '#f3e8ff',
                'pdb': '#fed7d7'
            };
            return colors[type] || '#e1e5e9';
        }

        // 设置打包下载功能
        function setupDownloadAll(taskId, files) {
            const btn = document.getElementById('downloadAllBtn');
            
            if (files.length === 0) {
                btn.disabled = true;
                btn.textContent = '暂无文件可下载';
                return;
            }

            btn.onclick = async () => {
                btn.disabled = true;
                btn.textContent = '正在准备下载...';
                
                try {
                    const response = await fetch(`/api/tasks/${taskId}/download-zip`, {
                        method: 'POST',
                        credentials: 'include'
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.download_url) {
                            window.open(data.download_url, '_blank');
                        }
                    }
                } catch (error) {
                    console.error('下载失败:', error);
                } finally {
                    btn.disabled = false;
                    btn.textContent = '📦 打包下载所有文件';
                }
            };
        }

        // 显示错误
        function showError(message) {
            document.getElementById('loadingContainer').style.display = 'none';
            document.getElementById('errorContainer').style.display = 'block';
            document.getElementById('errorContainer').innerHTML = `<div>❌ ${message}</div>`;
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const taskId = getUrlParameter('task_id');
            if (taskId) {
                loadTaskDetail(taskId);
            } else {
                showError('缺少任务ID参数');
            }
        });


        // 显示结构文件列表
        function displayStructureFiles(files) {
            const fileTabsContainer = document.getElementById('fileTabsContainer');
            const structureFilesList = document.getElementById('structureFilesList');

            // // 获取原始蛋白质文件名列表
            // const originalProteinFiles = protein_files || [];
            
            // 过滤文件：
            // 1. 只要PDB和PDBQT格式
            // 2. 对于蛋白质文件，只保留PDB格式且在summary.protein_files中的文件
            // 3. 对于小分子文件，保留所有PDBQT格式的对接结果
            const structureFiles = files.filter(file => {
                const fileType = file.type.toLowerCase();
                const fileName = file.name.toLowerCase();
                
                // 只处理PDB和PDBQT文件
                if (fileType !== 'pdb' && fileType !== 'pdbqt') {
                    return false;
                }
                
                // 如果是蛋白质相关文件
                if ((file.category === 'protein_structure' ||
                     fileName.includes('clean')) &&
                    !fileName.includes('pocket')) {
                    
                    // 蛋白质文件：只保留PDB格式，且必须在summary.protein_files中
                    if (fileType === 'pdbqt') {
                        console.log(`Filtering out PDBQT protein file: ${file.name}`);
                        return false; // 过滤掉PDBQT格式的蛋白质文件
                    }
                    
                    // 检查是否在原始蛋白质文件列表中
                    // const isOriginalProtein = originalProteinFiles.some(originalName => 
                    //     file.name.includes(originalName.replace(/\.(pdb|pdbqt)$/i, '')) ||
                    //     originalName.includes(file.name.replace(/\.(pdb|pdbqt)$/i, ''))
                    // );
                    
                    // if (!isOriginalProtein) {
                    //     console.log(`Filtering out non-original protein file: ${file.name}`);
                    //     return false;
                    // }
                    
                    return true;
                } else {
                    // 小分子对接结果文件：保留所有PDBQT格式
                    return fileType === 'pdbqt';
                }
            });
            
            if (structureFiles.length === 0) {
                fileTabsContainer.innerHTML = '';
                structureFilesList.innerHTML = `
                    <div style="text-align: center; color: #9ca3af; padding: 2rem;">
                        暂无结构文件
                    </div>
                `;
                return;
            }

            // 将文件按类别分组：蛋白质文件优先
            const proteinFiles = structureFiles.filter(file => {
                        const fileName = file.name.toLowerCase();
                        // 蛋白质文件：不包含pocket字样，且是PDB格式
                        return (file.category === 'protein_structure') &&
                            !fileName.includes('pocket') &&
                            file.type.toLowerCase() === 'pdb';
                    });

            const dockingFiles = structureFiles.filter(file => {
                const fileName = file.name.toLowerCase();
                // 对接结果文件：包含pocket字样或属于docking_results类别，且是PDBQT格式
                return (file.category === 'docking_results' ||
                        fileName.includes('pocket')) &&
                        file.type.toLowerCase() === 'pdbqt';
            });

            // 按文件名第一部分（下划线分割）分组
            const fileGroups = {};
            structureFiles.forEach(file => {
                let firstPart;
                // 优先使用横线分割，因为文件名格式通常是 protein-pocket-molecule_suffix
                if (file.name.includes('-')) {
                    firstPart = file.name.split('-')[0];
                } else if (file.name.includes('_')) {
                    firstPart = file.name.split('_')[0];
                } else {
                    firstPart = file.name.split('.')[0];
                }
                if (!fileGroups[firstPart]) {
                    fileGroups[firstPart] = [];
                }

                fileGroups[firstPart].push(file);
            });



            console.log('File groups:', fileGroups);

            // 生成标签页
            const tabNames = Object.keys(fileGroups);
            if (tabNames.length <= 1) {
                // 如果只有一个组或没有分组，不显示标签页
                fileTabsContainer.innerHTML = '';
                displayFileGroup(structureFiles, 'all');
            } else {
                // 生成多个标签页
                const tabsHtml = tabNames.map((tabName, index) => `
                    <div class="file-tab ${index === 0 ? 'active' : ''}" 
                         onclick="switchFileTab('${tabName}', this)">
                        ${tabName} (${fileGroups[tabName].length})
                    </div>
                `).join('');

                fileTabsContainer.innerHTML = `<div class="file-tabs">${tabsHtml}</div>`;

                // 生成标签页内容容器
                const tabContentsHtml = tabNames.map((tabName, index) => `
                    <div id="tab-content-${tabName}" class="file-tab-content ${index === 0 ? 'active' : ''}">
                        <!-- 文件列表将在这里显示 -->
                    </div>
                `).join('');

                structureFilesList.innerHTML = tabContentsHtml;

                // 为每个标签页生成文件列表
                tabNames.forEach((tabName, index) => {
                    displayFileGroup(fileGroups[tabName], tabName, index === 0);
                });
            }
        }

        // 显示文件组
        function displayFileGroup(files, groupName, isActive = true) {
            const containerId = groupName === 'all' ? 'structureFilesList' : `tab-content-${groupName}`;
            const container = document.getElementById(containerId);
            
            if (!container) {
                console.error(`Container not found: ${containerId}`);
                return;
            }

            // 将文件按类别分组：蛋白质文件优先
            const proteinFiles = files.filter(file => {
                return file.category === 'protein_structure' && 
                       !file.name.toLowerCase().includes('pocket') &&
                       file.type.toLowerCase() === 'pdb';
            });
 
            const dockingFiles = files.filter(file => {
                const fileName = file.name.toLowerCase();
                return (file.category === 'docking_results' ||
                       fileName.includes('pocket')) &&
                       file.type.toLowerCase() === 'pdbqt';
            });

            // 合并文件列表：蛋白质文件在前
            const orderedFiles = [...proteinFiles, ...dockingFiles];

            const fileListHtml = orderedFiles.map((file, index) => {
                const fileId = `file-${groupName}-${index}`;
                
                // 文件类别判断
                let categoryIcon, categoryName;
                if (file.category === 'protein_structure' && 
                    !file.name.toLowerCase().includes('pocket') &&
                    file.type.toLowerCase() === 'pdb') {
                    categoryIcon = '🧬';
                    categoryName = '蛋白质文件';
                } else {
                    categoryIcon = '🎯';
                    categoryName = '对接结果';
                }
                
                return `
                    <div class="file-list-item" data-file-id="${fileId}" onclick="toggleFileSelection('${fileId}', '${encodeURIComponent(file.url)}', '${encodeURIComponent(file.name)}', '${file.category}')">
                        <input type="checkbox" id="${fileId}" onchange="event.stopPropagation();">
                        <div class="file-info">
                            <div class="file-info-name">${file.name}</div>
                            <div class="file-info-type">${categoryIcon} ${categoryName} (${file.type.toUpperCase()})</div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = fileListHtml;
        }


        // 切换标签页
        function switchFileTab(tabName, clickedTab) {
            // 更新标签页状态
            document.querySelectorAll('.file-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            clickedTab.classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.file-tab-content').forEach(content => {
                content.classList.remove('active');
            });
            const targetContent = document.getElementById(`tab-content-${tabName}`);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        }


        // 切换文件选择状态
        function toggleFileSelection(fileId, encodedFileUrl, encodedFileName, category) {
            const fileUrl = decodeURIComponent(encodedFileUrl);
            const fileName = decodeURIComponent(encodedFileName);
            const checkbox = document.getElementById(fileId);
            const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
            
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                fileItem.classList.add('selected');
                selectedFiles.add(fileId);
                
                // 显示加载状态
                const fileInfo = fileItem.querySelector('.file-info');
                const originalContent = fileInfo.innerHTML;
                fileInfo.innerHTML = `
                    <div class="file-info-name">${fileName}</div>
                    <div class="file-info-type">⏳ 正在加载...</div>
                `;
                
                // 加载结构文件
                loadStructureFile(fileId, fileUrl, fileName, category).finally(() => {
                    // 恢复原始内容
                    fileInfo.innerHTML = originalContent;
                });
            } else {
                fileItem.classList.remove('selected');
                selectedFiles.delete(fileId);
                removeStructureFromViewer(fileId);
            }
        }

        // 加载结构文件并渲染
        async function loadStructureFile(fileId, fileUrl, fileName, category) {
            try {
                // 初始化3D查看器（如果还没有）
                if (!viewer3d) {
                    initializeViewer();
                }

                let fileContent;
                let fileType = fileName.toLowerCase().endsWith('.pdb') ? 'pdb' : 'pdbqt';

                // 如果是小分子对接结果文件（PDBQT格式），调用新接口提取第一个model
                if (category === 'docking_results' || fileName.toLowerCase().includes('pocket')) {
                    console.log('检测到对接结果文件，调用extract_model接口');
                    
                    const response = await fetch('/api/pocketvina/show_docking_model', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            file_url: fileUrl,
                            file_name: fileName
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`提取model失败: ${response.status}`);
                    }

                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.message || '提取model失败');
                    }

                    fileContent = data.extracted_content;
                    console.log(`成功提取第一个model，内容长度: ${fileContent.length}`);
                    
                } else {
                    // 对于蛋白质文件，直接下载
                    console.log('下载蛋白质文件:', fileUrl);
                    
                    const response = await fetch('/api/proxy/file', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            url: fileUrl
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`下载文件失败: ${response.status}`);
                    }

                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.message || '下载文件失败');
                    }

                    fileContent = data.content;
                }


                // 添加模型到查看器
                const modelIndex = viewer3d.addModel(fileContent, fileType);
                
                // 根据文件类别设置渲染样式
                if (category === 'docking_results' || fileName.toLowerCase().includes('pocket')) {
                    // 对接结果文件使用球棍模型
                    viewer3d.setStyle({model: modelIndex}, {
                        stick: {colorscheme: 'default', radius: 0.15},
                        sphere: {colorscheme: 'default', scale: 0.3}
                    });
                } else {
                    // 蛋白质文件使用cartoon渲染
                    viewer3d.setStyle({model: modelIndex}, {cartoon: {color: 'spectrum'}});
                }

                // 保存模型信息
                loadedModels.set(fileId, {
                    modelIndex: modelIndex,
                    fileName: fileName,
                    category: category
                });

                // 重新渲染
                viewer3d.zoomTo();
                viewer3d.render();

                console.log(`Successfully loaded ${fileName} as model ${modelIndex}`);

            } catch (error) {
                console.error('加载结构文件失败:', error);
                
                // 取消选择状态
                const checkbox = document.getElementById(fileId);
                const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
                checkbox.checked = false;
                fileItem.classList.remove('selected');
                selectedFiles.delete(fileId);
                
                alert(`加载文件失败: ${error.message}`);
            }
        }

        // 初始化3D查看器
        function initializeViewer() {
            // 检查3Dmol是否可用
            if (typeof $3Dmol === 'undefined') {
                console.error('3Dmol.js 库未加载');
                return;
            }

            const viewerContainer = document.getElementById('moleculeViewer');
            viewerContainer.innerHTML = ''; // 清空占位符内容

            // 创建3Dmol查看器

            viewer3d = $3Dmol.createViewer(viewerContainer, {
                defaultcolors: $3Dmol.rasmolElementColors,
                backgroundColor: 'black'
            });

            console.log('3D viewer initialized');
        }

        // 从查看器中移除结构
        function removeStructureFromViewer(fileId) {
            if (!viewer3d || !loadedModels.has(fileId)) {
                return;
            }

            const modelInfo = loadedModels.get(fileId);
            
            // 移除模型
            viewer3d.removeModel(modelInfo.modelIndex);
            loadedModels.delete(fileId);

            // 重新渲染
            viewer3d.render();

            console.log(`Removed model ${modelInfo.fileName}`);
        }

        // 清空所有渲染
        function clearAllRendering() {
            // 取消所有文件选择
            selectedFiles.forEach(fileId => {
                const checkbox = document.getElementById(fileId);
                const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
                if (checkbox) checkbox.checked = false;
                if (fileItem) fileItem.classList.remove('selected');
            });

            selectedFiles.clear();
            loadedModels.clear();

            // 清空查看器
            if (viewer3d) {
                viewer3d.clear();
                viewer3d.render();
            }

            // 恢复占位符
            const viewerContainer = document.getElementById('moleculeViewer');
            viewerContainer.innerHTML = `
                <div class="viewer-placeholder">
                    <div class="viewer-placeholder-icon">🧬</div>
                    <p>选择结构文件进行3D渲染</p>
                    <p style="font-size: 0.75rem;">支持 PDB 和 PDBQT 格式</p>
                </div>
            `;
            
            viewer3d = null;

            console.log('All rendering cleared');
        }


        // 设置打包下载功能
        function setupDownloadAll(taskId, files) {
            const btn = document.getElementById('downloadAllBtn');
            
            if (files.length === 0) {
                btn.disabled = true;
                btn.textContent = '暂无文件可下载';
                return;
            }

            btn.onclick = () => {
                downloadPocketVinaTaskAllResults(taskId);
            };
        }

        // 一键下载PocketVina任务的所有结果文件
        function downloadPocketVinaTaskAllResults(taskId) {
            console.log('=== Download PocketVina Task All Results Debug ===');
            console.log('Task ID:', taskId);
            
            // 显示加载提示
            const loadingNotification = document.createElement('div');
            loadingNotification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 4px;">📦 准备下载任务结果</div>
                <div style="font-size: 12px;">任务ID: ${taskId}</div>
                <div style="font-size: 12px;">正在获取文件列表...</div>
            `;
            loadingNotification.style.position = 'fixed';
            loadingNotification.style.top = '20px';
            loadingNotification.style.right = '20px';
            loadingNotification.style.background = '#3b82f6';
            loadingNotification.style.color = 'white';
            loadingNotification.style.padding = '12px 16px';
            loadingNotification.style.borderRadius = '8px';
            loadingNotification.style.zIndex = '10000';
            loadingNotification.style.fontSize = '13px';
            loadingNotification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
            loadingNotification.style.maxWidth = '300px';
            
            document.body.appendChild(loadingNotification);
            
            // 获取任务结果
            fetch(`/api/pocketvina/tasks/${taskId}/results`)
                .then(response => response.json())
                .then(data => {
                    // 移除加载提示
                    if (document.body.contains(loadingNotification)) {
                        document.body.removeChild(loadingNotification);
                    }
                    
                    if (data.success && data.result_files && data.result_files.length > 0) {
                        const resultFiles = data.result_files;
                        const totalFiles = resultFiles.length;
                        
                        console.log(`准备下载任务 ${taskId} 的所有结果: ${totalFiles} 个文件`);
                        
                        // 显示下载进度提示
                        const notification = document.createElement('div');
                        notification.innerHTML = `
                            <div style="font-weight: bold; margin-bottom: 4px;">📦 下载任务结果</div>
                            <div style="font-size: 12px;">任务ID: ${taskId}</div>
                            <div style="font-size: 12px;">正在下载 ${totalFiles} 个文件...</div>
                        `;
                        notification.style.position = 'fixed';
                        notification.style.top = '20px';
                        notification.style.right = '20px';
                        notification.style.background = '#f57c00';
                        notification.style.color = 'white';
                        notification.style.padding = '12px 16px';
                        notification.style.borderRadius = '8px';
                        notification.style.zIndex = '10000';
                        notification.style.fontSize = '13px';
                        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                        notification.style.maxWidth = '300px';
                        
                        document.body.appendChild(notification);
                        
                        // 显示详细的下载进度
                        const progressNotification = document.createElement('div');
                        progressNotification.innerHTML = `
                            <div style="font-size: 12px; opacity: 0.9;">下载进度: 0/${totalFiles}</div>
                            <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; margin-top: 4px;">
                                <div id="pocketvina-progress-bar" style="width: 0%; height: 100%; background: white; border-radius: 2px; transition: width 0.2s;"></div>
                            </div>
                        `;
                        progressNotification.style.position = 'fixed';
                        progressNotification.style.top = '110px';
                        progressNotification.style.right = '20px';
                        progressNotification.style.background = '#e65100';
                        progressNotification.style.color = 'white';
                        progressNotification.style.padding = '8px 12px';
                        progressNotification.style.borderRadius = '6px';
                        progressNotification.style.zIndex = '10000';
                        progressNotification.style.fontSize = '12px';
                        
                        document.body.appendChild(progressNotification);
                        
                        // 模拟打包进度更新
                        let packingProgress = 0;
                        const progressInterval = setInterval(() => {
                            packingProgress++;
                            const progressPercent = (packingProgress / totalFiles) * 80; // 打包进度占80%
                            
                            // 更新进度文字和进度条
                            const progressText = progressNotification.querySelector('div');
                            const progressBar = document.getElementById('pocketvina-progress-bar');
                            
                            if (progressText) {
                                progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">打包进度: ${packingProgress}/${totalFiles} 个文件</div>`;
                            }
                            if (progressBar) {
                                progressBar.style.width = `${progressPercent}%`;
                            }
                            
                            if (packingProgress >= totalFiles) {
                                clearInterval(progressInterval);
                                
                                // 开始调用压缩包API
                                if (progressText) {
                                    progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">正在生成压缩包...</div>`;
                                }
                                
                                // 调用后端API创建压缩包
                                fetch(`/api/tasks/${taskId}/download-zip`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                    }
                                    return response.json();
                                })
                                .then(zipData => {
                                    // 完成进度到100%
                                    if (progressBar) {
                                        progressBar.style.width = '100%';
                                    }
                                    if (progressText) {
                                        progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">✅ 压缩包创建完成!</div>`;
                                    }
                                    
                                    if (zipData.success && zipData.download_url) {
                                        // 更新主通知
                                        notification.innerHTML = `
                                            <div style="font-weight: bold; margin-bottom: 4px;">📦 开始下载压缩包</div>
                                            <div style="font-size: 12px;">文件: ${zipData.filename || `task_${taskId}_results.zip`}</div>
                                            <div style="font-size: 12px;">包含: ${totalFiles} 个结果文件</div>
                                            ${zipData.file_size ? `<div style="font-size: 12px;">大小: ${zipData.file_size}</div>` : ''}
                                        `;
                                        
                                        // 创建下载链接并触发下载
                                        const link = document.createElement('a');
                                        link.href = zipData.download_url;
                                        link.download = zipData.filename || `task_${taskId}_results.zip`;
                                        link.style.display = 'none';
                                        
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                        
                                        console.log('压缩包下载开始:', zipData.filename);
                                        
                                    } else {
                                        throw new Error(zipData.message || '创建压缩包失败');
                                    }
                                })
                                .catch(error => {
                                    console.error('创建压缩包失败:', error);
                                    
                                    // 更新通知显示错误
                                    notification.innerHTML = `
                                        <div style="font-weight: bold; margin-bottom: 4px;">❌ 下载失败</div>
                                        <div style="font-size: 12px;">错误: ${error.message}</div>
                                    `;
                                    notification.style.background = '#dc2626';
                                    
                                    if (progressText) {
                                        progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">❌ 创建压缩包失败</div>`;
                                    }
                                })
                                .finally(() => {
                                    // 5秒后移除通知
                                    setTimeout(() => {
                                        if (document.body.contains(notification)) {
                                            document.body.removeChild(notification);
                                        }
                                        if (document.body.contains(progressNotification)) {
                                            document.body.removeChild(progressNotification);
                                        }
                                    }, 5000);
                                });
                            }
                        }, 100); // 每100ms更新一次进度
                        
                    } else {
                        // 显示错误提示
                        const errorNotification = document.createElement('div');
                        errorNotification.innerHTML = `
                            <div style="font-weight: bold; margin-bottom: 4px;">❌ 下载失败</div>
                            <div style="font-size: 12px;">任务ID: ${taskId}</div>
                            <div style="font-size: 12px;">原因: ${data.message || '没有可下载的文件'}</div>
                        `;
                        errorNotification.style.position = 'fixed';
                        errorNotification.style.top = '20px';
                        errorNotification.style.right = '20px';
                        errorNotification.style.background = '#dc2626';
                        errorNotification.style.color = 'white';
                        errorNotification.style.padding = '12px 16px';
                        errorNotification.style.borderRadius = '8px';
                        errorNotification.style.zIndex = '10000';
                        errorNotification.style.fontSize = '13px';
                        errorNotification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                        errorNotification.style.maxWidth = '300px';
                        
                        document.body.appendChild(errorNotification);
                        
                        // 3秒后移除错误通知
                        setTimeout(() => {
                            if (document.body.contains(errorNotification)) {
                                document.body.removeChild(errorNotification);
                            }
                        }, 3000);
                    }
                })
                .catch(error => {
                    console.error('获取任务结果失败:', error);
                    
                    // 移除加载提示
                    if (document.body.contains(loadingNotification)) {
                        document.body.removeChild(loadingNotification);
                    }
                    
                    // 显示网络错误提示
                    const errorNotification = document.createElement('div');
                    errorNotification.innerHTML = `
                        <div style="font-weight: bold; margin-bottom: 4px;">❌ 网络错误</div>
                        <div style="font-size: 12px;">任务ID: ${taskId}</div>
                        <div style="font-size: 12px;">错误: ${error.message}</div>
                    `;
                    errorNotification.style.position = 'fixed';
                    errorNotification.style.top = '20px';
                    errorNotification.style.right = '20px';
                    errorNotification.style.background = '#dc2626';
                    errorNotification.style.color = 'white';
                    errorNotification.style.padding = '12px 16px';
                    errorNotification.style.borderRadius = '8px';
                    errorNotification.style.zIndex = '10000';
                    errorNotification.style.fontSize = '13px';
                    errorNotification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                    errorNotification.style.maxWidth = '300px';
                    
                    document.body.appendChild(errorNotification);
                    
                    // 5秒后移除错误通知
                    setTimeout(() => {
                        if (document.body.contains(errorNotification)) {
                            document.body.removeChild(errorNotification);
                        }
                    }, 5000);
                });
        }


       // 预览CSV文件
       async function previewCsvFile(fileUrl, fileId, fileName) {
            const previewContainer = document.getElementById(`preview-${fileId}`);
            const previewContent = previewContainer.querySelector('.preview-content');
            
            // 显示预览容器
            previewContainer.style.display = 'block';
            
            try {
                // 调用后端接口处理CSV文件
                const response = await fetch('/api/pocketvina/rank_csv', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        csv_url: fileUrl
                    })
                });

                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status}`);
                }

                const data = await response.json();
                if (!data.success) {
                    throw new Error(data.message || '处理CSV文件失败');
                }

                // 生成表格HTML
                const tableHtml = generateCsvTable(data);
                previewContent.innerHTML = tableHtml;

            } catch (error) {
                console.error('预览CSV文件失败:', error);
                previewContent.innerHTML = `<div class="preview-error">预览失败: ${error.message}</div>`;
            }
        }

        // 生成CSV表格
        function generateCsvTable(data) {
            if (!data.columns || !data.rows || data.rows.length === 0) {
                return '<div class="no-preview-data">无数据可显示</div>';
            }

            let tableHtml = `
                <div class="csv-info">
                    <div class="csv-stats">
                        <span class="stat-item">总行数: ${data.total_rows}</span>
                        ${data.filter_applied ? `<span class="stat-item">过滤后: ${data.filtered_rows}</span>` : ''}
                        <span class="stat-item">显示: ${data.displayed_rows}</span>
                    </div>
                    <div class="csv-message">${data.message}</div>
                </div>
                <div class="csv-table-container">
                    <table class="csv-table">
                        <thead>
                            <tr>
                                ${data.columns.map(col => `<th>${col}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
            `;

            data.rows.forEach(row => {
                tableHtml += `
                    <tr>
                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            return tableHtml;
        }

        // 关闭CSV预览
        function closeCsvPreview(fileId) {
            const previewContainer = document.getElementById(`preview-${fileId}`);
            previewContainer.style.display = 'none';
        }


    </script>
</body>
</html>