import { logout, isLogin } from '../api/login'
import router from '../router'

// 登出并清除本地存储
export function doLogout() {
  // 调用登出API
  logout()
    .then(() => {
      // 清除本地存储
      clearAuthStorage()
      // 重定向到登录页
      router.push('/login')
    })
    .catch(error => {
      console.error('登出错误:', error)
      // 即使API调用失败，也清除本地存储
      clearAuthStorage()
      router.push('/login')
    })
}

// 清除认证相关的本地存储
export function clearAuthStorage() {
  localStorage.removeItem('isLoggedIn')
  localStorage.removeItem('mobile')
  localStorage.removeItem('username')
  localStorage.removeItem('user_id')
  localStorage.removeItem('userId')
  localStorage.removeItem('lastLoginTime')
  localStorage.removeItem('loginTime')
}

// 检查是否已登录
export function isAuthenticated() {
  return localStorage.getItem('isLoggedIn') === 'true'
}

// 获取用户信息
export function getUserInfo() {
  if (!isAuthenticated()) {
    return null
  }

  return {
    mobile: localStorage.getItem('mobile') || localStorage.getItem('username'),
    user_id: localStorage.getItem('user_id') || localStorage.getItem('userId'),
    lastLoginTime: localStorage.getItem('lastLoginTime') || localStorage.getItem('loginTime')
  }
}

// 检查服务器端认证状态
export function checkAuthStatus() {
  return new Promise((resolve, reject) => {
    isLogin()
      .then(data => {
        if (data.loginStatus) {
          // 如果服务器说已登录，更新localStorage
          localStorage.setItem('isLoggedIn', 'true')
          localStorage.setItem('mobile', data.userInfo.mobile)
          localStorage.setItem('username', data.userInfo.mobile)
          localStorage.setItem('user_id', data.userInfo.user_id)
          localStorage.setItem('userId', data.userInfo.user_id)
          localStorage.setItem('lastLoginTime', new Date().toISOString())
          localStorage.setItem('loginTime', new Date().toISOString())
          resolve({ isLoggedIn: true, userInfo: data.userInfo })
        } else {
          // 如果服务器说未登录，清除任何过期的客户端登录状态
          clearAuthStorage()
          resolve({ isLoggedIn: false })
        }
      })
      .catch(error => {
        console.error('Error checking auth status:', error)
        reject(error)
      })
  })
} 