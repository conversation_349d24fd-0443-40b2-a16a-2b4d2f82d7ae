<!--
    Simple Layout
    简单布局模板

    功能: 提供不带导航栏的简单页面布局，用于登录、注册等页面
    继承: base.html
    被继承: login.html, sign.html, reset_password.html

    使用示例:
    {# extends "layout_simple.html" #}
    {# block content #}登录表单内容{# endblock #}
-->
{% extends "base.html" %}

{% block extra_css %}
<!-- 认证页面样式 -->
<link rel="stylesheet" href="/src/styles/auth.css">
{% endblock %}

{% block body_class %}layout-simple{% endblock %}

{% block body %}
    <!-- 简单容器 -->
    <div class="simple-container">
        {% block content %}
        <!-- 页面具体内容在这里 -->
        {% endblock %}
    </div>
{% endblock %}

{% block extra_js %}
<!-- 认证相关脚本 -->
<script src="/src/scripts/auth.js"></script>
{% endblock %}
