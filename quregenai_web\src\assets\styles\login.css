* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

button:focus {
  outline: none;
}

/* 语言切换按钮 */
.language-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.language-btn {
  padding: 8px 16px;
  border: 2px solid #667eea;
  background: white;
  color: #667eea;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.language-btn:hover {
  background: #f0f2ff;
}

.language-btn.active {
  background: #667eea;
  color: white;
}

/* 登录页面样式 */
.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  background-size: cover;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -1;
}

.login-container {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  margin-top: -3rem;
  min-height: 520px;
  overflow-y: auto;
  max-height: calc(100vh - 4rem);
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.login-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-top: 0.5rem;
}

.login-header h1 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  word-wrap: break-word;
  overflow: visible;
}

.login-header p {
  color: #666;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 700;
  text-align: left;
}

.form-group input[type="text"],
.form-group input[type="password"] {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus {
  outline: none;
  border-color: #667eea;
}

.forgot-password {
  text-align: right;
  margin-top: 0.5rem;
}

.forgot-password a {
  color: #667eea;
  text-decoration: none;
  font-size: 0.875rem;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  display: block;
  background: #ffeaea;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border-left: 4px solid #e74c3c;
}

.login-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1rem;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
  transform: translateY(0);
}

.auth-divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.auth-divider span {
  position: relative;
  background: white;
  padding: 0 1rem;
  color: #666;
  font-size: 0.875rem;
}

.register-btn {
  width: 100%;
  padding: 0.75rem;
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.register-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.register-btn:active {
  transform: translateY(0);
}

.features {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.features h3 {
  color: #333;
  font-size: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.feature-list {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-item {
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    margin: 0.5rem;
    padding: 1.5rem;
    min-height: 450px;
    margin-top: 0;
  }
  
  .login-header h1 {
    font-size: 1.6rem;
    line-height: 1.3;
  }
  
  .language-switcher {
    top: 10px;
    right: 10px;
  }
  
  .language-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-height: 700px) {
  .login-container {
    min-height: auto;
    margin-top: 3rem;
  }
  
  .login-header {
    margin-bottom: 1.5rem;
  }
  
  .features {
    margin-top: 1rem;
    padding-top: 1rem;
  }
}