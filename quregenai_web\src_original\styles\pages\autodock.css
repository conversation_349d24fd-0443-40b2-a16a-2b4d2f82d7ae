.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page-header {
    background: linear-gradient(135deg, #81c784 0%, #a5d6a7 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 25px rgba(129, 199, 132, 0.2);
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-icon {
    font-size: 3rem;
}

.page-icon img {
    width: 48px;
    height: 48px;
    object-fit: contain;
}

.page-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* 新的分子查看器样式 */
.molecule-viewer-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.viewer-container {
    display: flex;
    gap: 20px;
    max-width: 1600px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.viewer-section {
    flex: 3;
}

.controls-section {
    flex: 1;
    min-width: 250px;
    max-width: 280px;
}

#viewer-container {
    width: 100%;
    height: 450px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background-color: #000;
    position: relative;
}

#file-list-container {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    max-height: 300px;
    overflow-y: auto;
}

/* 自定义滚动条样式 */
#file-list-container::-webkit-scrollbar {
    width: 8px;
}

#file-list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#file-list-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#file-list-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.upload-section {
    margin-bottom: 20px;
}

.upload-btn {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

#file-input {
    display: none;
}

.file-type-info {
    margin-top: 10px;
    font-size: 0.8rem;
    color: #666;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #2e7d32;
}

#loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 20px 30px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: none;
    z-index: 10000;
}

#error-message {
    display: none;
    color: #d32f2f;
    padding: 12px;
    margin: 10px 0;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 6px;
    border-left: 4px solid #d32f2f;
}

/* 文件列表样式 */
#file-list-container h3 {
    color: #2e7d32;
    margin-bottom: 15px;
    font-size: 0.95rem;
    border-bottom: 2px solid #e8f5e8;
    padding-bottom: 8px;
}

#file-list-container ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#file-list-container li {
    padding: 8px;
    margin: 5px 0;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
    transition: all 0.2s ease;
    cursor: pointer;
}

#file-list-container li:hover {
    background-color: #e8f5e8;
    border-color: #4caf50;
    transform: translateX(2px);
}

#file-list-container input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(0.9);
}

#file-list-container label {
    cursor: pointer;
    font-weight: 500;
    color: #333;
    font-size: 0.85rem;
}

/* 功能区域样式 */
.feature-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e1e5e9;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature-card {
    background: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border-left: 4px solid #2e7d32;
    transition: transform 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
    color: #2e7d32;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.feature-badge {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-primary {
    background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

.btn-secondary {
    background: white;
    color: #2e7d32;
    border: 2px solid #2e7d32;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.1);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background: #2e7d32;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
    border-color: #1b5e20;
}

.btn-icon {
    font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content {
        padding: 1rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .viewer-container {
        flex-direction: column;
        gap: 15px;
    }

    .viewer-section, .controls-section {
        flex: none;
        width: 100%;
    }

    #viewer-container {
        height: 350px;
    }
}

/* 原子信息列表样式 */
.atom-info-section {
    margin-top: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e1e5e9;
}

.atom-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e8f5e8;
}

.atom-info-header h3 {
    color: #2e7d32;
    font-size: 0.95rem;
    margin: 0;
}

.clear-atoms-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.clear-atoms-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.clear-atoms-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.atom-info-list {
    max-height: 350px;
    overflow-y: auto;
}

.atom-info-list::-webkit-scrollbar {
    width: 6px;
}

.atom-info-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.atom-info-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.atom-info-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.no-atoms-message {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
    margin: 0;
    font-size: 0.85rem;
}

.atom-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 6px;
    margin: 2px 0;
    border: 1px solid #ddd;
    background: white;
}

/* 添加到autodock.html的样式中 */
.atom-item.selected-for-center {
    background-color: #e8f5e8;
    border-color: #4caf50;
    box-shadow: 0 1px 3px rgba(76, 175, 80, 0.2);
}

.atom-item.excluded-from-center {
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%) !important;
    border: 2px solid #9e9e9e !important;
    box-shadow: 0 2px 8px rgba(158, 158, 158, 0.2) !important;
    opacity: 0.6;
    filter: grayscale(0.3);
}

.atom-item:hover {
    background-color: #f0f8ff;
    border-color: #e3f2fd;
}

.atom-item.selected-for-autodock {
    background-color: #e8f5e8;
    border-color: #4caf50;
    box-shadow: 0 1px 3px rgba(76, 175, 80, 0.2);
}

.atom-item.selected-for-autodock:hover {
    background-color: #d4edda;
    border-color: #28a745;
}

.atom-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
}

.atom-serial {
    font-weight: 600;
    color: #333;
    font-size: 0.85em;
}

.atom-file {
    font-size: 0.7em;
    color: #666;
    background: #f5f5f5;
    padding: 1px 4px;
    border-radius: 2px;
}

.atom-details {
    font-size: 0.75em;
    color: #555;
    line-height: 1.2;
}

.atom-detail {
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 1px;
}

.atom-detail-label {
    font-weight: 500;
    color: #666;
}

.atom-detail-value {
    color: #333;
    margin-left: 2px;
}

.atom-coordinates {
    margin-top: 2px;
    font-family: monospace;
    font-size: 0.7em;
    color: #444;
    background: #f8f9fa;
    padding: 1px 3px;
    border-radius: 2px;
    display: inline-block;
}

.remove-atom-btn {
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    cursor: pointer;
    position: absolute;
    top: 2px;
    right: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.remove-atom-btn:hover {
    background: #cc0000;
}

/* AutoDock 输入参数设置样式 */
.input-files-section {
    background: #ffffff;
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.input-files-section .section-title {
    color: #1f2937;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid #3b82f6;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.input-files-section .section-title::before {
    content: "⚙️";
    font-size: 1.5rem;
}

.input-files-section .section-title::after {
    content: "";
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
}

/* 参数分组样式 */
.parameter-group {
    margin-bottom: 2.5rem;
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.parameter-group:last-of-type {
    margin-bottom: 2rem;
}

.group-header {
    margin-bottom: 1.5rem;
}

.group-title {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.group-description {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
    font-style: italic;
}

.input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    align-items: start;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-group.full-width {
    grid-column: 1 / -1;
}

.input-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.label-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem; /* 增大主要标签字体 */
    font-weight: 600; /* 增加字重 */
}

.required {
    color: #ef4444;
    font-weight: 700;
}

.file-select, .smiles-input, .threads-input {
    padding: 0.75rem 1rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.file-select:focus, .smiles-input:focus, .threads-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.file-select:hover, .smiles-input:hover, .threads-input:hover {
    border-color: #8fa8f3;
}

/* 小分子输入容器样式 */
.ligand-input-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Tab样式 */
.ligand-tabs {
    display: flex;
    background: #f8fafc;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 1.5rem;
    border: 1px solid #e2e8f0;
}

.ligand-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-align: center;
}

.ligand-tab:hover {
    background: #e2e8f0;
    color: #475569;
}

.ligand-tab.active {
    background: white;
    color: #3b82f6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

.ligand-tab-content {
    display: none;
}

.ligand-tab-content.active {
    display: block;
}

.or-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0.5rem 0;
}

.or-divider::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #d1d5db;
}

.or-divider span {
    background: #f8fafc;
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.smiles-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.smiles-input-wrapper .smiles-input {
    flex: 1;
    padding: 0.75rem 1rem;
}

.validate-smiles-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.validate-smiles-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.validate-smiles-btn:active {
    transform: translateY(0);
}

.validate-smiles-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.validate-smiles-btn.validating {
    background: #f59e0b;
    cursor: wait;
}

.validate-smiles-btn.success {
    background: #10b981;
}

.validate-smiles-btn.error {
    background: #ef4444;
}

.input-icon {
    /* 已删除：不再需要的样式 */
}

/* 坐标输入容器样式 */
.coordinate-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr) auto;
    gap: 1rem;
    align-items: end;
}

.coordinate-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.coordinate-input-group label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.coordinate-input-group input {
    padding: 0.5rem 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    text-align: center;
    transition: all 0.2s ease;
}

.coordinate-input-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.auto-fill-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.auto-fill-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.auto-fill-btn:active {
    transform: translateY(0);
}

/* 尺寸输入容器样式 */
.size-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.size-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.size-input-group label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.size-input-group input {
    padding: 0.5rem 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    text-align: center;
    transition: all 0.2s ease;
}

.size-input-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 线程数输入样式 */
.threads-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.threads-input-wrapper .threads-input {
    flex: 1;
    padding-right: 4rem;
}

.input-unit {
    position: absolute;
    right: 1rem;
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 500;
    pointer-events: none;
}

/* 提示文本样式 */
.input-hint {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    line-height: 1.4;
}

/* 操作按钮容器样式 */
.action-buttons-container {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.btn-primary, .btn-secondary {
    padding: 0.875rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 140px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
    background: white;
    color: #2e7d32;
    border: 2px solid #2e7d32;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.1);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background: #2e7d32;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
    border-color: #1b5e20;
}

.btn-icon {
    font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .input-grid {
        grid-template-columns: 1fr;
    }

    .coordinate-container {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .auto-fill-btn {
        justify-self: stretch;
    }

    .size-container {
        grid-template-columns: 1fr;
    }

    .action-buttons-container {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-primary, .btn-secondary {
        min-width: auto;
    }
}

/* 对接结果栏样式 */
.docking-results-section {
    margin-top: 2rem;
    padding: 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.results-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.hide-results-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hide-results-btn:hover {
    background: #e5e7eb;
    color: #374151;
    border-color: #9ca3af;
}

.hide-results-btn:active {
    transform: scale(0.95);
}

.results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.results-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-text {
    font-size: 1rem;
    font-weight: 500;
    color: #666;
}

.status-text.status-waiting {
    color: #f59e0b;
}

.status-text.status-running {
    color: #3b82f6;
}

.status-text.status-completed {
    color: #10b981;
}

.status-text.status-failed {
    color: #ef4444;
}

.status-text.status-error {
    color: #ef4444;
}

.status-text.status-timeout {
    color: #f59e0b;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.results-content {
    margin-top: 1rem;
}

.result-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.card-header h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.card-content {
    margin-top: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-label {
    font-weight: 500;
    color: #666;
}

.info-value {
    font-weight: 600;
    color: #333;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background-color: #e8f5e8;
    color: #2e7d32;
    font-weight: 500;
}

.toggle-btn {
    background: none;
    border: none;
    color: #3b82f6;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0;
}

.toggle-btn:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.human-readable-results {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #3b82f6;
}

.human-readable-results pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #374151;
}

.detailed-results {
    margin-top: 1rem;
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
}

.results-tabs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: #3b82f6;
}

.tab-btn.active {
    border-bottom-color: #3b82f6;
    color: #3b82f6;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.scores-list {
    margin-top: 1rem;
}

.scores-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.scores-list li {
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.scores-list li strong {
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.scores-list li ul {
    margin-top: 0.5rem;
    padding-left: 1rem;
}

.scores-list li ul li {
    background: none;
    border: none;
    padding: 0.25rem 0;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.files-list {
    margin-top: 1rem;
}

.files-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.files-list li {
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.files-list button {
    padding: 0.5rem 1rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.files-list button:hover {
    background: #1d4ed8;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.task-id-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 1rem;
    border-left: 4px solid #3b82f6;
}

.task-id-value {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.info-grid-compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
}

/* 任务历史样式 */
.task-history-container {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tasks-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.task-item {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    background: #f9fafb;
    transition: all 0.2s ease;
}

.task-item:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.task-id {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #6b7280;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.task-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-status.pending {
    background: #fef3c7;
    color: #92400e;
}

.task-status.running {
    background: #dbeafe;
    color: #1e40af;
}

.task-status.completed {
    background: #d1fae5;
    color: #065f46;
}

.task-status.failed {
    background: #fee2e2;
    color: #991b1b;
}

.task-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.task-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

.task-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-btn.view {
    background: #3b82f6;
    color: white;
}

.task-btn.view:hover {
    background: #2563eb;
}

.task-btn.process {
    background: #10b981;
    color: white;
}

.task-btn.process:hover {
    background: #059669;
}

.task-btn.process:disabled {
    background: #f59e0b !important; /* 橙色表示处理中 */
    cursor: not-allowed !important;
    opacity: 0.8;
}

.task-btn.process:disabled:hover {
    background: #f59e0b !important; /* 保持橙色，不变化 */
}

.task-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}


.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.btn-secondary {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.btn-secondary:hover {
    background: #4b5563;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

.detail-row {
    display: flex;
    margin-bottom: 1rem;
    align-items: flex-start;
}

.detail-row label {
    font-weight: 600;
    color: #374151;
    min-width: 100px;
    margin-right: 1rem;
}

.detail-row span {
    flex: 1;
}

.parameters-display {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 1rem;
    font-size: 0.875rem;
    overflow-x: auto;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

/* 模型提取按钮样式 */
#extract-model-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

#extract-model-btn:active {
    transform: translateY(0);
}

#extract-model-btn:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}


/* 批量上传样式 */
.batch-upload-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.csv-upload-section {
    text-align: center;
    padding: 2rem;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background-color: #f9fafb;
}

.upload-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.upload-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.csv-format-hint {
    margin-top: 1rem;
    font-size: 0.8rem;
    color: #6b7280;
    line-height: 1.4;
}

/* 批量分子容器样式 */
.batch-molecules-container {
    display: flex;
    gap: 1rem;
    min-height: 400px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.molecules-panel {
    flex: 1;
    background: white;
    display: flex;
    flex-direction: column;
}

.structure-panel {
    width: 350px;
    background: #f8fafc;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #e5e7eb;
}

.panel-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h5 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
}

.search-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-container input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.8rem;
    width: 150px;
}

.search-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.search-btn:hover {
    background: #e5e7eb;
}

.select-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.select-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
}

.select-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

/* 分子列表样式 */
.molecules-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
    max-height: 300px; /* 限制最大高度为300px */
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #f9fafb;
}

/* 自定义滚动条样式 */
.molecules-list::-webkit-scrollbar {
    width: 8px;
}

.molecules-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.molecules-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.molecules-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.molecule-item {
    display: flex;
    align-items: center;
    padding: 0.4rem;
    margin-bottom: 0.2rem;
    border: 1px solid #e5e7eb;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.molecule-item:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.molecule-item.selected {
    background: #eff6ff;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
}

.molecule-item-content {
    flex: 1;
}

.molecule-name {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.molecule-smiles {
    font-size: 0.75rem;
    color: #6b7280;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

/* 2D结构显示样式 */
#structure2d {
    margin: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
}

.molecule-info {
    padding: 1rem;
    font-size: 0.8rem;
    color: #6b7280;
    line-height: 1.4;
}

.molecule-info .info-item {
    margin-bottom: 0.5rem;
}

.molecule-info .info-label {
    font-weight: 500;
    color: #374151;
}

/* 分页控件样式 */
.pagination-container {
    margin-top: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pagination-info {
    color: #6b7280;
    font-size: 0.875rem;
    margin-right: 1rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    cursor: pointer;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    min-width: 40px;
    text-align: center;
    font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.pagination-btn:disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.pagination-btn.active:hover {
    background: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

.pagination-ellipsis {
    padding: 0.5rem 0.25rem;
    color: #9ca3af;
    font-size: 0.875rem;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.page-size-selector select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background: white;
    font-size: 0.875rem;
}

.task-history-container .btn-secondary {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.task-history-container .btn-secondary:hover {
    background: #4b5563;
}

/* 绿色刷新按钮样式 */
.btn-refresh-green {
    background: #4caf50;
    color: white;
    border: 2px solid #4caf50;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-refresh-green:hover {
    background: #45a049;
    border-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.btn-icon {
    font-size: 1rem;
}

/* 多文件选择样式 */
.file-selection-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.file-selector-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-select[multiple] {
    min-height: 120px;
    padding: 0.5rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.file-select[multiple]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.file-select[multiple] option {
    padding: 0.5rem;
    border-radius: 4px;
    margin: 2px 0;
}

.file-select[multiple] option:hover {
    background-color: #f3f4f6;
}

.file-select[multiple] option:checked {
    background-color: #3b82f6;
    color: white;
}

.file-selector-hint {
    font-size: 0.75rem;
    color: #6b7280;
    text-align: center;
    padding: 0.25rem;
    background: #f9fafb;
    border-radius: 4px;
}

.selected-files-display {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
}

.selected-files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.selected-files-header h5 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
}

.clear-selection-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-selection-btn:hover {
    background: #dc2626;
}

.selected-files-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.selected-file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.selected-file-item .file-name {
    font-size: 0.875rem;
    color: #374151;
}

.remove-file-btn {
    background: #ef4444;
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.remove-file-btn:hover {
    background: #dc2626;
}

/* 文件选择相关样式 */
.section-subtitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem; /* 减小子标题字体 */
    font-weight: 500; /* 减少字重 */
    color: #6b7280; /* 使用更浅的颜色 */
    margin-bottom: 0.75rem;
}

.subtitle-icon {
    font-size: 1.1rem;
}

/* 已上传文件网格样式 */
.uploaded-files-section {
    margin-bottom: 1.5rem;
}

.uploaded-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.5rem;
    min-height: 60px;
}

.file-card {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    position: relative;
}

.file-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.file-card.selected {
    border-color: #10b981;
    background: #f0fdf4;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}

.file-card-header {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.file-type-icon {
    font-size: 1rem;
}

.file-name {
    font-weight: 500;
    color: #374151;
    font-size: 0.8rem;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-card.selected .file-name {
    color: #065f46;
}

.file-info {
    font-size: 0.7rem;
    color: #6b7280;
}

.file-card.selected .file-info {
    color: #047857;
}

.selection-indicator {
    position: absolute;
    top: 0.375rem;
    right: 0.375rem;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #10b981;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
}

.file-card.selected .selection-indicator {
    opacity: 1;
    transform: scale(1);
}

.no-files-message {
    grid-column: 1 / -1;
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 1.5rem;
    background: #f9fafb;
    border: 2px dashed #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
}

/* 分隔线样式 */
.section-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.5rem 0;
    position: relative;
}

.section-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.divider-text {
    background: white;
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

/* 本地上传区域样式 */
.local-upload-section {
    margin-bottom: 1.5rem;
}

.upload-drop-zone {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fafafa;
}

.upload-drop-zone:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.upload-drop-zone.dragover {
    border-color: #10b981;
    background: #f0fdf4;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #6b7280;
}

.upload-text {
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.5rem;
}

.upload-hint {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 已选文件显示样式保持不变 */
.selected-files-display {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.selected-files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.selected-files-header h5 {
    margin: 0;
    font-size: 0.9rem;
    color: #374151;
}

.clear-selection-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background 0.2s ease;
}

.clear-selection-btn:hover {
    background: #dc2626;
}

.selected-files-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.selected-file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.selected-file-item .file-name {
    font-size: 0.875rem;
    color: #374151;
}

.remove-file-btn {
    background: #ef4444;
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.remove-file-btn:hover {
    background: #dc2626;
}

/* 对接口袋指南样式 */
.docking-pocket-guide {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
}

.guide-title {
    color: #1e40af;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.guide-section {
    display: grid;
    gap: 1.25rem;
}


/* 分页容器样式 */
.pagination-container {
    background: white;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info {
    color: #6b7280;
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #374151;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.pagination-btn:disabled {
    background: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    border-color: #e5e7eb;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #374151;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.page-number:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.page-number.active {
    background: #2196f3;
    border-color: #2196f3;
    color: white;
}

.page-number.ellipsis {
    background: transparent;
    border: none;
    cursor: default;
    color: #9ca3af;
}

.page-number.ellipsis:hover {
    background: transparent;
    border: none;
    color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .page-numbers {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* 平台教程按钮样式（更醒目，并与标题同排） */
.page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.tutorial-btn {
    background: #f86e6e;
    color: #3d2f00;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.2s ease;
    border: 1px solid #ed9696;
    margin-left: 0.75rem;
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
}

.tutorial-btn:hover {
    background: #ed9696;
    color: #3d2f00;
    text-decoration: none;
    transform: translateY(-1px);
}