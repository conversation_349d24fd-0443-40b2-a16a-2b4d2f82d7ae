:root {
    --primary: #0d3b66;
    --secondary: #5c9ead;
    --accent: #1d7874;
    --light: #f4f7f6;
    --text: #333333;
    --border: #dde2e5;
    --shadow: rgba(13, 59, 102, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text);
    background-color: #fafafa;
    padding: 20px;
}

.agreement-container {
    max-width: 900px;
    margin: 30px auto;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 25px var(--shadow);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    padding: 30px;
    text-align: center;
    position: relative;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: 1px;
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    gap: 10px;
    line-height: 1.2;
}

.logo-icon {
    width: 36px;
    height: 36px;
    background: radial-gradient(circle, #fff 30%, transparent 70%);
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.logo-icon::after {
    content: "Ψ";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    font-weight: bold;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 500px;
    margin: 0 auto;
}

.content {
    padding: 40px;
}

.section {
    margin-bottom: 35px;
    border-left: 4px solid var(--accent);
    padding-left: 15px;
    transition: all 0.3s ease;
}

.section:hover {
    border-left: 4px solid var(--primary);
}

.section-title {
    font-size: 1.4rem;
    color: var(--primary);
    margin-bottom: 15px;
    font-weight: 600;
}

.subsection {
    margin-left: 20px;
    margin-bottom: 15px;
}

.highlight-box {
    background-color: var(--light);
    border-radius: 8px;
    padding: 20px;
    margin: 25px 0;
    border: 1px solid var(--border);
}

.highlight-title {
    font-weight: 600;
    color: var(--accent);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.highlight-title i {
    font-size: 1.2rem;
}

.checkbox-container {
    background-color: rgba(93, 158, 173, 0.1);
    border-radius: 8px;
    padding: 20px;
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin: 10px 0 20px;
}


.footer {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 0.9rem;
    border-top: 1px solid var(--border);
    margin-top: 20px;
}

/* 语言切换按钮样式 */
.language-switcher {
    position: fixed;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    z-index: 1000;
}

.language-btn {
    padding: 0.5rem 0.75rem;
    border: 2px solid #e1e5e9;
    background: white;
    color: #666;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.language-btn:hover {
    border-color: var(--primary);
    color: var(--primary);
}

.language-btn.active {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    border-color: var(--primary);
    color: white;
    font-weight: 600;
}

@media (max-width: 768px) {
    .content {
        padding: 30px 20px;
    }

    .header {
        padding: 25px 20px;
    }

    .agreement-container {
        margin: 10px;
    }

    .language-switcher {
        top: 0.5rem;
        right: 0.5rem;
    }

    .logo {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: center;
        gap: 15px;
        text-align: center;
    }

    .logo-icon {
        margin-top: 0;
    }
}