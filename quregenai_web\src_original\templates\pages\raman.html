{% extends "layout_with_nav.html" %}

{% block title %}Raman光谱检测 - QureGenAI 药物设计平台{% endblock %}

{% block page_css %}
<link rel="stylesheet" href="/src/styles/pages/raman.css">
{% endblock %}

{% block content %}

            <div class="page-header">
                <h1 class="page-title">
                    <span class="page-icon">⚡</span>
                    <span data-i18n="page-title">Raman光谱检测</span>
                </h1>
                <p class="page-subtitle" data-i18n="page-subtitle">
                    Raman光谱检测是一种基于分子振动光谱的分析技术，通过激光激发样品产生拉曼散射，获得分子结构信息。
                </p>
                
                <div class="raman-intro">
                    <h3 data-i18n="tech-advantages">🌟 技术优势</h3>
                    <p><strong data-i18n="cost-effective">低成本高效：</strong><span data-i18n="cost-effective-desc">相比传统检测方法，Raman光谱检测具有成本低、速度快、无损检测等优势。</span></p>
                    <p><strong data-i18n="wide-application">广泛应用：</strong><span data-i18n="wide-application-desc">适用于中草药成分含量检测、农作物农药残留检测、药物成分分析、食品安全检测等领域。</span></p>
                    <p><strong data-i18n="real-time-analysis">实时分析：</strong><span data-i18n="real-time-analysis-desc">支持现场快速检测，无需复杂的前处理步骤，为质量控制提供即时反馈。</span></p>
                </div>
            </div>

            <div class="section">
                <div class="section-header">
                    <h2 data-i18n="task-history">Raman光谱检测历史</h2>
                    <button id="refresh-tasks-btn" class="btn btn-refresh-blue">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="23 4 23 10 17 10"></polyline>
                            <polyline points="1 20 1 14 7 14"></polyline>
                            <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                        </svg>
                        <span data-i18n="refresh-tasks">刷新</span>
                    </button>
                </div>
                
                <div class="task-history-container">
                    <div id="tasks-list" class="tasks-list">
                        <!-- 任务列表将在这里动态生成 -->
                    </div>
                    
                    <div id="tasks-pagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="pagination-info-text"></span>
                        </div>
                        <div class="pagination-controls">
                            <button id="prev-page-btn" class="pagination-btn" onclick="loadTaskHistoryPage(currentPage - 1)">
                                <span data-i18n="prev-page">上一页</span>
                            </button>
                            <div id="page-numbers" class="page-numbers">
                                <!-- 页码按钮将在这里动态生成 -->
                            </div>
                            <button id="next-page-btn" class="pagination-btn" onclick="loadTaskHistoryPage(currentPage + 1)">
                                <span data-i18n="next-page">下一页</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
{% endblock %}

{% block page_js %}
<script>
        // 多语言文本配置
        const translations = {
            zh: {
                'page-title': 'Raman光谱检测',
                'page-subtitle': 'Raman光谱检测是一种基于分子振动光谱的分析技术，通过激光激发样品产生拉曼散射，获得分子结构信息。',
                'task-history': 'Raman光谱检测历史',
                'refresh-tasks': '刷新',
                'prev-page': '上一页',
                'next-page': '下一页',
                'pending': '等待中',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败',
                'tech-advantages': '🌟 技术优势',
                'cost-effective': '低成本高效：',
                'cost-effective-desc': '相比传统检测方法，Raman光谱检测具有成本低、速度快、无损检测等优势。',
                'wide-application': '广泛应用：',
                'wide-application-desc': '适用于中草药成分含量检测、农作物农药残留检测、药物成分分析、食品安全检测等领域。',
                'real-time-analysis': '实时分析：',
                'real-time-analysis-desc': '支持现场快速检测，无需复杂的前处理步骤，为质量控制提供即时反馈。',
            },
            en: {
                'page-title': 'Raman Spectroscopy Detection',
                'page-subtitle': 'Raman spectroscopy detection is an analytical technique based on molecular vibrational spectroscopy, which obtains molecular structural information through laser excitation of samples to produce Raman scattering.',
                'task-history': 'Raman Spectroscopy Detection History',
                'refresh-tasks': 'Refresh',
                'prev-page': 'Previous',
                'next-page': 'Next',
                'pending': 'Pending',
                'running': 'Running',
                'completed': 'Completed',
                'failed': 'Failed',
                'tech-advantages': '🌟 Technical Advantages',
                'cost-effective': 'Cost-effective & Efficient: ',
                'cost-effective-desc': 'Compared to traditional detection methods, Raman spectroscopy detection offers advantages of low cost, high speed, and non-destructive testing.',
                'wide-application': 'Wide Applications: ',
                'wide-application-desc': 'Applicable to traditional Chinese medicine component content detection, crop pesticide residue detection, drug component analysis, food safety testing and other fields.',
                'real-time-analysis': 'Real-time Analysis: ',
                'real-time-analysis-desc': 'Supports on-site rapid detection without complex pre-processing steps, providing immediate feedback for quality control.',
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 任务历史相关变量
        let currentPage = 1;
        let totalPages = 1;
        let tasksPerPage = 10;
        let totalTasks = 0;

        // 切换语言函数 - 覆盖全局函数以添加页面特定逻辑
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });

            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';

            // 更新文档标题
            document.title = lang === 'zh' ? 'Raman光谱检测 - QureGenAI 药物设计平台' : 'Raman Spectroscopy Detection - QureGenAI Drug Design Platform';

            // 更新页面文本
            updatePageText(lang);

            // 更新登录时间显示
            const loginTime = localStorage.getItem('loginTime');
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];
            
            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t[key]) {
                    element.textContent = t[key];
                }
            });
        }

        // 修改 apiRequest 函数，添加更详细的错误处理
        function apiRequest(url, options = {}) {
            options.credentials = 'include';
            
            return fetch(url, options)
                .then(response => {
                    if (response.status === 401) {
                        // 认证失败，跳转登录
                        window.location.href = 'login';
                        throw new Error('认证失败，请重新登录');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.redirect === '/login') {
                        // 服务器要求重新登录
                        window.location.href = 'login';
                        throw new Error(data.message || '需要重新登录');
                    }
                    return data;
                });
        }

        // 检查后端认证状态
        async function checkBackendAuth() {
            try {
                const response = await fetch('/api/auth/status', {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.logged_in) {
                        // 后端认证通过，显示用户信息并加载任务
                        displayUserInfo();
                        loadTaskHistory();
                    } else {
                        // 后端认证失败，跳转登录
                        window.location.href = 'login';
                    }
                } else {
                    // 请求失败，跳转登录
                    window.location.href = 'login';
                }
            } catch (error) {
                console.error('检查认证状态失败:', error);
                // 网络错误，跳转登录
                window.location.href = 'login';
            }
        }

        // 显示用户信息
        function displayUserInfo() {
            // 从后端获取用户信息
            fetch('/api/auth/status', {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.logged_in && data.user) {
                    const username = data.user.mobile || data.user.email || '未知用户';
                    document.getElementById('username').textContent = username;
                    document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
            });
        }


        // 修改 loadTaskHistory 函数，使用 apiRequest 确保认证
        async function loadTaskHistory(page = 1) {
            try {

                // 先检查前端登录状态
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                if (isLoggedIn !== 'true') {
                    console.log('用户未登录，重定向到登录页面');
                    window.location.href = 'login';
                    return;
                }

                showTasksLoading();
                console.log(`加载第${page}页任务历史，每页${tasksPerPage}条`);
                
                // 调用Raman任务API

                const response = await fetch(`/api/raman-ai/tasks?page=${page}&per_page=${tasksPerPage}`, {
                    method: 'GET'
                })

                if (response.status === 401) {
                    // 认证失败，清除本地存储并重定向到登录页面
                    console.log('后端认证失败，清除本地存储并重定向到登录页面');
                    clearLocalStorage();
                    window.location.href = 'login';
                    return;
                }
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                console.log(result);
                if (result.success) {
                    // 更新分页信息
                    if (result.pagination) {
                        currentPage = result.pagination.page;
                        totalPages = result.pagination.total_pages;
                        totalTasks = result.pagination.total;
                    }
                    
                    // 显示任务列表
                    displayTasks(result.tasks || []);
                    updatePagination();
                } else {
                    console.error('获取任务列表失败:', result.message);
                    showNotification(result.message || '获取任务列表失败', 'error');
                }
            } catch (error) {
                console.error('加载任务历史失败:', error);
                if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                    // 认证错误，清除本地存储并重定向
                    clearLocalStorage();
                    window.location.href = 'login';
                } else {
                    showNotification('加载任务历史失败，请稍后重试', 'error');
                }
            } finally {
                hideTasksLoading();
            }
        }

        // 添加显示和隐藏加载状态的函数
        function showTasksLoading() {
            const loadingElement = document.getElementById('tasks-loading');
            if (loadingElement) {
                loadingElement.style.display = 'block';
            }
        }

        function hideTasksLoading() {
            const loadingElement = document.getElementById('tasks-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        }

        // 显示无任务状态
        function showNoTasks(message = '暂无Raman光谱检测任务') {
            const tasksList = document.getElementById('tasks-list');
            tasksList.innerHTML = `
                <div class="no-tasks">
                    <p>${message}</p>
                </div>
            `;
            
            // 隐藏分页
            document.getElementById('tasks-pagination').style.display = 'none';
        }

        // 修改 displayTasks 函数，为任务项添加点击跳转功能
        function displayTasks(tasks) {
            const container = document.getElementById('tasks-list');
            
            if (!container) {
                console.error('找不到任务列表容器元素 tasks-list');
                return;
            }
            
            if (!tasks || tasks.length === 0) {
                container.innerHTML = `
                    <div class="no-tasks-message">
                        <p>暂无Raman光谱检测任务</p>
                    </div>
                `;
                return;
            }
            
            const tasksHTML = tasks.map(task => `
                <div class="task-item" onclick="viewTaskDetail('${task.detection_id}')">
                    <div class="task-header">
                        <div class="task-id-container">
                            <span class="task-id">检测ID: ${task.detection_id}</span>
                            <button class="copy-btn" onclick="copyToClipboard('${task.detection_id}', event)" title="复制检测ID">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                            </button>
                        </div>
                        <span class="task-status ${task.status}">${getStatusText(task.status)}</span>
                    </div>
                    <div class="task-details">
                        <div class="task-detail-item">
                            <span class="task-detail-label">机器ID:</span>
                            <span class="task-detail-value">${task.machine_id || '未知'}</span>
                        </div>
                        <div class="task-detail-item">
                            <span class="task-detail-label">检测时间:</span>
                            <span class="task-detail-value">${task.detection_time || '未知'}</span>
                        </div>
                        <div class="task-detail-item">
                            <span class="task-detail-label">样本名称:</span>
                            <span class="task-detail-value">${task.sample_name || '未知'}</span>
                        </div>
                        <div class="task-detail-item">
                            <span class="task-detail-label">样本CAS:</span>
                            <span class="task-detail-value">${task.sample_cas || '未知'}</span>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = tasksHTML;
        }

        // 添加任务详情跳转函数
        function viewTaskDetail(detectionId) {
            console.log('查看任务详情:', detectionId);
            // 跳转到任务详情页面
            window.location.href = `raman_detail?task_id=${detectionId}`;
        }


        // 添加复制到剪贴板的函数
        function copyToClipboard(text, event) {
            event.stopPropagation(); // 阻止事件冒泡
            
            // 使用现代浏览器的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopySuccess(event.target);
                }).catch(err => {
                    console.error('复制失败:', err);
                    // 降级到传统方法
                    fallbackCopyToClipboard(text, event);
                });
            } else {
                // 降级到传统方法
                fallbackCopyToClipboard(text, event);
            }
        }

        // 降级复制方法
        function fallbackCopyToClipboard(text, event) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showCopySuccess(event.target);
            } catch (err) {
                console.error('复制失败:', err);
                showCopyError(event.target);
            }
            
            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess(button) {
            const originalHTML = button.innerHTML;
            button.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
            `;
            button.classList.add('copy-success');
            
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('copy-success');
            }, 1500);
        }

        // 显示复制失败提示
        function showCopyError(button) {
            const originalHTML = button.innerHTML;
            button.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            `;
            button.classList.add('copy-error');
            
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('copy-error');
            }, 1500);
        }



        // 获取状态文本
        function getStatusText(status) {
            const t = translations[currentLanguage];
            const statusMap = {
                'completed': t['completed'] || '已完成',
                'running': t['running'] || '运行中',
                'failed': t['failed'] || '失败',
                'pending': t['pending'] || '等待中'
            };
            return statusMap[status] || status;
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // 修改 viewTaskDetails 函数，跳转到任务详情页面
        function viewTaskDetails(detectionId) {
            console.log('查看任务详情:', detectionId);
            // 跳转到任务详情页面
            window.location.href = `raman_detail?task_id=${detectionId}`;
        }

        // 修复 updatePagination 函数，使用正确的变量
        function updatePagination() {
            // 检查是否有分页信息
            if (totalPages <= 1) {
                document.getElementById('tasks-pagination').style.display = 'none';
                return;
            }
            
            document.getElementById('tasks-pagination').style.display = 'block';
            
            // 更新分页信息
            const startItem = (currentPage - 1) * tasksPerPage + 1;
            const endItem = Math.min(currentPage * tasksPerPage, totalTasks);
            const infoText = `显示 ${startItem}-${endItem} 条，共 ${totalTasks} 条任务`;
            document.getElementById('pagination-info-text').textContent = infoText;
            
            // 更新按钮状态
            document.getElementById('prev-page-btn').disabled = currentPage <= 1;
            document.getElementById('next-page-btn').disabled = currentPage >= totalPages;
            
            // 生成页码按钮
            generatePageNumbers();
        }

        // 修改 generatePageNumbers 函数，参考 autodock 的实现
        function generatePageNumbers() {
            const pageNumbers = document.getElementById('page-numbers');
            pageNumbers.innerHTML = '';
            
            const maxVisiblePages = 7; // 最多显示7个页码按钮
            let startPage = 1;
            let endPage = totalPages;
            
            if (totalPages > maxVisiblePages) {
                const halfVisible = Math.floor(maxVisiblePages / 2);
                startPage = Math.max(1, currentPage - halfVisible);
                endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                
                // 调整开始页码，确保显示完整的页码范围
                if (endPage - startPage + 1 < maxVisiblePages) {
                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }
            }
            
            let html = '';
            
            // 第一页和省略号
            if (startPage > 1) {
                html += `<button class="page-number-btn" onclick="loadTaskHistoryPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="page-number-btn ellipsis">...</span>`;
                }
            }
            
            // 页码范围
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                html += `<button class="page-number-btn ${activeClass}" onclick="loadTaskHistoryPage(${i})">${i}</button>`;
            }
            
            // 省略号和最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="page-number-btn ellipsis">...</span>`;
                }
                html += `<button class="page-number-btn" onclick="loadTaskHistoryPage(${totalPages})">${totalPages}</button>`;
            }
            
            pageNumbers.innerHTML = html;
        }

        // 修改 loadTaskHistoryPage 函数
        function loadTaskHistoryPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                loadTaskHistory(page);
            }
        }

        // 退出登录
        function logout() {
            fetch('/api/logout', { 
                method: 'POST',
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('退出登录成功');
                })
                .catch(error => {
                    console.error('退出登录时发生错误:', error);
                })
                .finally(() => {
                    clearLocalStorage();
                    window.location.href = 'login';
                });
        }

        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('userId');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('lastAuthCheck');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 先检查登录状态
            checkLoginStatus();
            // 初始化语言切换功能
            switchLanguage(currentLanguage);
            // 加载任务历史
            loadTaskHistory();

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });
        });

        // 检查登录状态函数
        function checkLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn !== 'true') {
                window.location.href = 'login';
                return;
            }

            // 显示用户信息
            const username = localStorage.getItem('username');
            const loginTime = localStorage.getItem('loginTime');
            
            const t = translations[currentLanguage];
            if (document.getElementById('username')) {
                document.getElementById('username').textContent = username || t['unknown-user'];
            }
            if (document.getElementById('userAvatar')) {
                document.getElementById('userAvatar').textContent = (username || 'U').substring(0, 3).toUpperCase();
            }
        }

        // 退出登录函数
        function logout() {
            fetch('/api/logout', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Server session ended');
                    } else {
                        console.error('Server logout failed:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error during server logout:', error);
                })
                .finally(() => {
                    // Always clear client-side storage and redirect
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login';
                });
        }


        // 添加通知显示函数
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            // 添加到页面
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

    </script>
{% endblock %}