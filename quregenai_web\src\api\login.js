import api from './index'

// 用户登录接口
export function userLogin(data) {
  return api.post('/login/userlogin', data)
}

// 用户注册接口
export function userRegister(data) {
  return api.post('/login/user_register', data)
}

// 判断用户是否已登录
export function isLogin() {
  return api.get('/login/islogin')
}

// 登录状态心跳刷新
export function loginHeartbeat() {
  return api.get('/login/loginHeartbeat')
}

// 注册时获取验证码
export function getVerificationCode(data) {
  return api.post('/login/vcode', data)
}

// 发送验证码（支持手机和邮箱）
export function sendVerificationCode(data) {
  return api.post('/api/send_verification_code', data)
}

// 用户注册（支持手机和邮箱）
export function registerUser(data) {
  return api.post('/api/register', data)
}

// 找回密码时获取验证码
export function getFindPwdCode(mobile) {
  return api.get(`/login/findpwd?mobile=${mobile}`)
}

// 更改密码
export function updatePassword(data) {
  return api.post('/login/update_pwd', data)
}

// 用户登出
export function logout() {
  return api.post('/login/logout')
}

// 获取事务验证码
export function getAffairCode(mobile) {
  return api.get(`/login/post_affair_code?mobile=${mobile}`)
} 