/**
 * Authentication Styles
 * 认证页面样式
 * 
 * 包含: 登录、注册、密码重置页面样式
 * 依赖: base.css
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

/* ===== CSS变量定义 ===== */
:root {
    --auth-primary-color: #667eea;
    --auth-secondary-color: #764ba2;
    --auth-success-color: #27ae60;
    --auth-error-color: #e74c3c;
    --auth-warning-color: #f39c12;
    --auth-bg-color: #f5f7fa;
    --auth-card-bg: #ffffff;
    --auth-border-color: #e1e5e9;
    --auth-text-color: #333;
    --auth-text-muted: #666;
    --auth-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
    --auth-transition: all 0.3s ease;
}

/* ===== 简单布局容器 ===== */
.layout-simple {
    background: linear-gradient(135deg, var(--auth-primary-color) 0%, var(--auth-secondary-color) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.simple-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

/* ===== 认证卡片 ===== */
.auth-card {
    background: var(--auth-card-bg);
    border-radius: 1rem;
    padding: 2.5rem;
    box-shadow: var(--auth-shadow);
    text-align: center;
}

.auth-card__header {
    margin-bottom: 2rem;
}

.auth-card__title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--auth-text-color);
    margin-bottom: 0.5rem;
}

.auth-card__subtitle {
    color: var(--auth-text-muted);
    font-size: 0.9rem;
}

.auth-card__logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--auth-primary-color) 0%, var(--auth-secondary-color) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    font-weight: 700;
}

/* ===== 表单样式 ===== */
.auth-form {
    text-align: left;
}

.auth-form__group {
    margin-bottom: 1.5rem;
}

.auth-form__label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--auth-text-color);
    font-size: 0.9rem;
}

.auth-form__input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--auth-border-color);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: var(--auth-transition);
    background: var(--auth-card-bg);
}

.auth-form__input:focus {
    outline: none;
    border-color: var(--auth-primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.auth-form__input.is-valid {
    border-color: var(--auth-success-color);
}

.auth-form__input.is-invalid {
    border-color: var(--auth-error-color);
}

.auth-form__input::placeholder {
    color: var(--auth-text-muted);
}

/* ===== 按钮样式 ===== */
.auth-btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, var(--auth-primary-color) 0%, var(--auth-secondary-color) 100%);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--auth-transition);
    margin-bottom: 1rem;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-btn:active {
    transform: translateY(0);
}

.auth-btn.is-loading {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.auth-btn--secondary {
    background: transparent;
    color: var(--auth-primary-color);
    border: 2px solid var(--auth-primary-color);
}

.auth-btn--secondary:hover {
    background: var(--auth-primary-color);
    color: white;
}

/* ===== 消息提示 ===== */
.auth-message {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.auth-message--success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--auth-success-color);
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.auth-message--error {
    background: rgba(231, 76, 60, 0.1);
    color: var(--auth-error-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.auth-message--warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--auth-warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.auth-message--info {
    background: rgba(102, 126, 234, 0.1);
    color: var(--auth-primary-color);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

/* ===== 链接样式 ===== */
.auth-links {
    text-align: center;
    margin-top: 1.5rem;
}

.auth-link {
    color: var(--auth-primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--auth-transition);
}

.auth-link:hover {
    color: var(--auth-secondary-color);
    text-decoration: underline;
}

.auth-divider {
    margin: 1.5rem 0;
    text-align: center;
    position: relative;
    color: var(--auth-text-muted);
    font-size: 0.85rem;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--auth-border-color);
    z-index: 1;
}

.auth-divider span {
    background: var(--auth-card-bg);
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

/* ===== 复选框样式 ===== */
.auth-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.auth-checkbox__input {
    width: auto;
    margin-right: 0.5rem;
}

.auth-checkbox__label {
    margin-bottom: 0;
    font-size: 0.85rem;
    color: var(--auth-text-muted);
}

/* ===== 密码强度指示器 ===== */
.password-strength {
    margin-top: 0.5rem;
    height: 4px;
    background: var(--auth-border-color);
    border-radius: 2px;
    overflow: hidden;
}

.password-strength__bar {
    height: 100%;
    width: 0;
    transition: var(--auth-transition);
    border-radius: 2px;
}

.password-strength--weak .password-strength__bar {
    width: 33%;
    background: var(--auth-error-color);
}

.password-strength--medium .password-strength__bar {
    width: 66%;
    background: var(--auth-warning-color);
}

.password-strength--strong .password-strength__bar {
    width: 100%;
    background: var(--auth-success-color);
}

/* ===== 响应式设计 ===== */
@media (max-width: 480px) {
    .layout-simple {
        padding: 1rem;
    }

    .auth-card {
        padding: 2rem 1.5rem;
    }

    .auth-card__title {
        font-size: 1.5rem;
    }

    .simple-container {
        max-width: 100%;
    }
}

/* ===== 动画效果 ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== 加载状态 ===== */
.auth-btn.is-loading::after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
