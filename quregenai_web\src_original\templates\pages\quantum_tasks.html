{% extends "layout_with_nav.html" %}

{% block title %}TyxonQ - 量子计算任务列表{% endblock %}

{% block inline_styles %}
<link rel="stylesheet" href="/src/styles/pages/quantum_tasks.css">
{% endblock %}

{% block content %}
            <div class="container">
                <div class="page-header">
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <h1 data-i18n="tasks.title">⚛️ TyxonQ - 量子计算任务列表</h1>
                        <div class="header-actions" style="display: flex; align-items: center; gap: 1rem;">
                            <a href="https://github.com/QureGenAI-Biotech/TyxonQ" target="_blank" class="tutorial-btn">
                                <span>📚</span>
                                <span data-i18n="tutorial-btn">平台教程</span>
                            </a>
                            <button onclick="refreshTasks()" class="btn btn-primary" data-i18n="tasks.refresh">
                                <span class="icon">🔄</span>
                                刷新任务
                            </button>
                        </div>
                    </div>
                </div>
                <div class="stats-bar" id="stats-bar" style="display: none;">
                    <!-- 统计信息将在这里显示 -->
                </div>
                <div id="loading" class="loading" data-i18n="common.loading">
                    正在加载任务列表...
                </div>

                <div id="error" class="error" style="display: none;">
                </div>

                <div id="tasks-container" class="tasks-list" style="display: none;">
                </div>

                <div id="no-tasks" class="no-tasks" style="display: none;" data-i18n="tasks.no_tasks">
                    暂无任务数据
                </div>

                <div id="pagination" class="pagination"></div>

                <div class="timestamp" id="last-updated"></div>
            </div>
{% endblock %}

{% block page_js %}
{{ super() }}
<script>
        let currentPage = 1;
        const pageSize = 20;
        let allTasks = [];
        let totalTasks = 0;



        async function loadTasks(pageNumber = 1) {
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('error');
            const containerEl = document.getElementById('tasks-container');
            const noTasksEl = document.getElementById('no-tasks');
            const timestampEl = document.getElementById('last-updated');

            // 显示加载状态
            loadingEl.style.display = 'block';
            errorEl.style.display = 'none';
            containerEl.style.display = 'none';
            noTasksEl.style.display = 'none';

            try {
                // 构建查询参数
                const params = new URLSearchParams({
                    task_type: 'quantum_api',
                    page: pageNumber,
                    page_size: pageSize
                });

                // 使用项目标准API路径，将参数添加到URL中
                const apiUrl = `/api/tasks?${params.toString()}`;

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (!data.success) {
                    throw new Error('API返回失败状态');
                }

                allTasks = data.tasks || [];
                totalTasks = data.total_count || allTasks.length;
                currentPage = pageNumber;

                loadingEl.style.display = 'none';

                if (allTasks.length === 0) {
                    noTasksEl.style.display = 'block';
                } else {
                    renderTasks();
                }

                // 更新时间戳
                const lastUpdatedText = translations[currentLanguage]['tasks.last_updated'] || '最后更新';
                const totalTasksText = translations[currentLanguage]['tasks.total_tasks'] || '总任务数';
                const currentTime = new Date().toLocaleString(currentLanguage === 'en' ? 'en-US' : 'zh-CN');
                timestampEl.textContent = `${lastUpdatedText}: ${currentTime} | ${totalTasksText}: ${totalTasks}`;

            } catch (error) {
                loadingEl.style.display = 'none';
                errorEl.style.display = 'block';
                const errorText = translations[currentLanguage]['tasks.load_error'] || '加载任务列表失败';
                errorEl.textContent = `${errorText}: ${error.message}`;
                console.error('Error loading tasks:', error);
            }
        }



        function updateStats(data) {
            const statsBar = document.getElementById('stats-bar');
            const total = data.total || allTasks.length;

            let html = `
                <div class="stat-card">
                    <div class="stat-value">${total.toLocaleString()}</div>
                    <div class="stat-label">${translations[currentLanguage]['tasks.total_tasks'] || '总任务数'}</div>
                </div>
            `;

            statsBar.innerHTML = html;
            statsBar.style.display = 'grid';
        }

        function updatePaginationStats() {
            const statsBar = document.getElementById('stats-bar');
            const currentPageStart = (currentPage - 1) * pageSize + 1;
            const currentPageEnd = Math.min(currentPage * pageSize, totalTasks);
            const totalPages = Math.ceil(totalTasks / pageSize);

            // 使用国际化函数获取文本
            const totalTasksText = translations[currentLanguage]['tasks.total_tasks'] || '总任务数';
            const currentDisplayText = translations[currentLanguage]['tasks.current_display'] || '当前显示';
            const currentPageTotalText = translations[currentLanguage]['tasks.current_page_total'] || '当前页/总页数';
            const perPageText = translations[currentLanguage]['tasks.per_page'] || '每页显示';

            let html = `
                <div class="stat-card">
                    <div class="stat-value">${totalTasks.toLocaleString()}</div>
                    <div class="stat-label">${totalTasksText}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${currentPageStart}-${currentPageEnd}</div>
                    <div class="stat-label">${currentDisplayText}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${currentPage}/${totalPages}</div>
                    <div class="stat-label">${currentPageTotalText}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${pageSize}</div>
                    <div class="stat-label">${perPageText}</div>
                </div>
            `;

            statsBar.innerHTML = html;
            statsBar.style.display = 'grid';
        }

        function renderTasks() {
            const containerEl = document.getElementById('tasks-container');
            const noTasksEl = document.getElementById('no-tasks');

            if (allTasks.length === 0) {
                containerEl.style.display = 'none';
                noTasksEl.style.display = 'block';
                document.getElementById('pagination').innerHTML = '';
                return;
            }

            // 直接显示从API获取的任务，不需要前端分页
            containerEl.innerHTML = allTasks.map(task => createTaskItem(task)).join('');
            containerEl.style.display = 'block';
            noTasksEl.style.display = 'none';

            renderPagination();
            updatePaginationStats();
        }

        function createTaskItem(task) {
            const statusClass = `status-${task.status || 'unknown'}`;
            const statusText = getStatusText(task.status);

            const formatTimestamp = (timestamp) => {
                if (!timestamp || timestamp === 'NaT') return translations[currentLanguage]['tasks.unknown'] || '未知';
                // 直接使用字符串时间戳
                const date = new Date(timestamp);
                return date.toLocaleString(currentLanguage === 'en' ? 'en-US' : 'zh-CN');
            };

            const formatDuration = (duration) => {
                if (!duration) return translations[currentLanguage]['tasks.unknown'] || '未知';
                const ms = duration / 1000; // 转换为毫秒
                if (ms < 1000) return `${ms.toFixed(0)}ms`;
                if (ms < 60000) return `${(ms/1000).toFixed(1)}s`;
                return `${(ms/60000).toFixed(1)}min`;
            };

            // 解析参数和结果
            let parsedParams = {};
            let parsedResult = {};
            let resultText = '';

            try {
                if (task.parameters) {
                    parsedParams = JSON.parse(task.parameters);
                }
                if (task.result) {
                    parsedResult = JSON.parse(task.result);
                    if (parsedResult.result && typeof parsedResult.result === 'object') {
                        const results = Object.entries(parsedResult.result).map(([state, count]) => `|${state}⟩:${count}`);
                        resultText = results.slice(0, 3).join(' '); // 只显示前3个结果
                        if (results.length > 3) resultText += '...';
                    }
                }
            } catch (e) {
                console.warn(translations[currentLanguage]['tasks.parse_error'] || '解析任务参数或结果失败:', e);
            }

            // 获取完成时间
            const completedTime = task.completed_at && task.completed_at !== 'NaT' ? formatTimestamp(task.completed_at) : (translations[currentLanguage]['tasks.not_completed'] || '未完成');

            // 处理设备名称，去掉问号及其后面的部分
            const deviceName = (parsedParams.device || parsedResult.device || (translations[currentLanguage]['tasks.unknown_device'] || '未知设备')).split('?')[0];

            return `
                <div class="task-item" onclick="viewTask('${task.task_id}')">
                    <div class="task-main">
                        <div class="task-header">
                            <div class="task-header-left">
                                <span class="task-id"><a href='/qau-cloud/quantum_task_detail?id=${task.task_id}'>${task.task_id}</a></span>
                            </div>
                            <div class="task-header-right">
                                <span class="task-device">${deviceName}</span>
                                <span class="task-status ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                        <div class="task-details">
                            <span class="task-detail-item">
                                <strong>${translations[currentLanguage]['tasks.qubits'] || '量子比特'}:</strong> ${parsedResult.qubits || 0}
                            </span>
                            <span class="task-detail-item">
                                <strong>${translations[currentLanguage]['tasks.depth'] || '深度'}:</strong> ${parsedResult.depth || 0}
                            </span>
                            <span class="task-detail-item">
                                <strong>${translations[currentLanguage]['tasks.shots'] || '测试'}:</strong> ${parsedParams.shots || parsedResult.shots || 0}${translations[currentLanguage]['tasks.shots_unit'] || '次'}
                            </span>
                            <span class="task-detail-item">
                                <strong>${translations[currentLanguage]['tasks.queue'] || '队列'}:</strong> ${parsedResult.queue || (translations[currentLanguage]['tasks.unknown'] || '未知')}
                            </span>
                            <span class="task-detail-item">
                                <strong>${translations[currentLanguage]['tasks.cost'] || '费用'}:</strong> ${task.cost_qau ? parseFloat(task.cost_qau).toLocaleString() : '0'} QAU
                            </span>
                            <span class="task-detail-item">
                                <strong>${translations[currentLanguage]['tasks.duration'] || '时长'}:</strong> ${formatDuration(parsedResult.runDur)}
                            </span>
                        </div>
                        <div class="task-timing">
                            <span class="task-timing-item"><strong>${translations[currentLanguage]['tasks.created_time'] || '创建时间'}:</strong> ${formatTimestamp(task.created_at)}</span>
                            <span class="task-timing-item"><strong>${translations[currentLanguage]['tasks.completed_time'] || '完成时间'}:</strong> ${completedTime}</span>
                            ${resultText ? `<span class="task-timing-item task-result-mini"><strong>${translations[currentLanguage]['tasks.result'] || '结果'}:</strong> ${resultText}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        function renderPagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(totalTasks / pageSize);

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let html = '';

            // 上一页
            const prevText = translations[currentLanguage]['tasks.prev_page'] || '上一页';
            html += `<button ${currentPage === 1 ? 'disabled' : ''} onclick="goToPage(${currentPage - 1})">← ${prevText}</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                html += `<button onclick="goToPage(1)">1</button>`;
                if (startPage > 2) html += `<span>...</span>`;
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    html += `<button class="active">${i}</button>`;
                } else {
                    html += `<button onclick="goToPage(${i})">${i}</button>`;
                }
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) html += `<span>...</span>`;
                html += `<button onclick="goToPage(${totalPages})">${totalPages}</button>`;
            }

            // 下一页
            const nextText = translations[currentLanguage]['tasks.next_page'] || '下一页';
            html += `<button ${currentPage === totalPages ? 'disabled' : ''} onclick="goToPage(${currentPage + 1})">${nextText} →</button>`;

            pagination.innerHTML = html;
        }

        function goToPage(page) {
            if (page < 1 || page > Math.ceil(totalTasks / pageSize)) {
                return; // 防止无效页码
            }

            // 更新URL参数
            const url = new URL(window.location);
            url.searchParams.set('np', page);
            window.history.pushState({}, '', url);

            loadTasks(page);
        }



        function refreshTasks() {
            loadTasks(currentPage);
        }

        function getStatusText(status) {
            // 直接使用translations对象
            const statusKey = `tasks.status.${status}`;
            if (translations[currentLanguage] && translations[currentLanguage][statusKey]) {
                return translations[currentLanguage][statusKey];
            }

            // 回退到默认文本
            const statusMap = {
                'completed': '已完成',
                'pending': '等待中',
                'pending1': '等待中',
                'running': '运行中',
                'failed': '失败',
                'cancelled': '已取消',
                'scheduled': '已调度'
            };
            return statusMap[status] || status || (translations[currentLanguage]['tasks.unknown'] || '未知');
        }

        function viewTask(taskId) {
            // 跳转到任务详情页面
            window.location.href = `/qau-cloud/quantum_task_detail?id=${taskId}`;
        }
        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('userId');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('lastAuthCheck');
            localStorage.removeItem('email');
        }
         // 退出登录
        function logout() {
            // 直接使用fetch，避免apiRequest的认证检查逻辑
            fetch('/api/logout', { 
                method: 'POST',
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('退出登录成功');
                })
                .catch(error => {
                    console.error('退出登录时发生错误:', error);
                })
                .finally(() => {
                    // 总是清除客户端存储并跳转
                    clearLocalStorage();
                    console.log('跳转？')
                    window.location.href = 'login';
                });
        }

        // 多语言文本配置
        const translations = {
            zh: {
                'tasks.title': '⚛️ TyxonQ - 量子计算任务列表',
                'tutorial-btn': '平台教程',
                'common.refresh': '🔄 刷新',
                'common.loading': '正在加载任务列表...',
                'tasks.no_tasks': '暂无任务数据',
                'tasks.total_tasks': '总任务数',
                'tasks.current_display': '当前显示',
                'tasks.current_page_total': '当前页/总页数',
                'tasks.per_page': '每页显示',
                'tasks.prev_page': '上一页',
                'tasks.next_page': '下一页',
                'tasks.last_updated': '最后更新',
                'tasks.status.completed': '已完成',
                'tasks.status.pending': '等待中',
                'tasks.status.pending1': '等待中',
                'tasks.status.running': '运行中',
                'tasks.status.failed': '失败',
                'tasks.status.cancelled': '已取消',
                'tasks.status.scheduled': '已调度',
                'tasks.load_error': '加载任务列表失败',
                'tasks.unknown': '未知',
                'tasks.unknown_user': '未知用户',
                'tasks.unknown_device': '未知设备',
                'tasks.not_completed': '未完成',
                'tasks.qubits': '量子比特',
                'tasks.depth': '深度',
                'tasks.shots': '测试',
                'tasks.shots_unit': '次',
                'tasks.queue': '队列',
                'tasks.cost': '费用',
                'tasks.duration': '时长',
                'tasks.created_time': '创建时间',
                'tasks.completed_time': '完成时间',
                'tasks.result': '结果',
                'tasks.parse_error': '解析任务参数或结果失败',
            },
            en: {
                'tasks.title': '⚛️ TyxonQ - Quantum Computing Task List',
                'common.refresh': '🔄 Refresh',
                'common.loading': 'Loading task list...',
                'tasks.no_tasks': 'No tasks available',
                'tasks.total_tasks': 'Total Tasks',
                'tasks.current_display': 'Current Display',
                'tasks.current_page_total': 'Current Page/Total Pages',
                'tasks.per_page': 'Per Page',
                'tasks.prev_page': 'Previous',
                'tasks.next_page': 'Next',
                'tasks.last_updated': 'Last Updated',
                'tasks.status.completed': 'Completed',
                'tasks.status.pending': 'Pending',
                'tasks.status.pending1': 'Pending',
                'tasks.status.running': 'Running',
                'tasks.status.failed': 'Failed',
                'tasks.status.cancelled': 'Cancelled',
                'tasks.status.scheduled': 'Scheduled',
                'tasks.load_error': 'Failed to load task list',
                'tasks.unknown': 'Unknown',
                'tasks.unknown_user': 'Unknown User',
                'tasks.unknown_device': 'Unknown Device',
                'tasks.not_completed': 'Not Completed',
                'tasks.qubits': 'Qubits',
                'tasks.depth': 'Depth',
                'tasks.shots': 'Shots',
                'tasks.shots_unit': ' times',
                'tasks.queue': 'Queue',
                'tasks.cost': 'Cost',
                'tasks.duration': 'Duration',
                'tasks.created_time': 'Created Time',
                'tasks.completed_time': 'Completed Time',
                'tasks.result': 'Result',
                'tasks.parse_error': 'Failed to parse task parameters or results',
            }
        };

        // 简单的国际化实现
        let currentLanguage = localStorage.getItem('language') || 'en';

        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });

            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';

            updatePageText();

            // 重新渲染任务列表以更新动态生成的文本
            if (allTasks.length > 0) {
                renderTasks();
            }

            // 更新时间戳文本
            const timestampEl = document.getElementById('last-updated');
            if (timestampEl.textContent) {
                const lastUpdatedText = translations[currentLanguage]['tasks.last_updated'] || '最后更新';
                const totalTasksText = translations[currentLanguage]['tasks.total_tasks'] || '总任务数';
                const currentTime = new Date().toLocaleString(currentLanguage === 'en' ? 'en-US' : 'zh-CN');
                timestampEl.textContent = `${lastUpdatedText}: ${currentTime} | ${totalTasksText}: ${totalTasks}`;
            }
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        function updatePageText() {
            const elements = document.querySelectorAll('[data-i18n]');
            elements.forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (translations[currentLanguage] && translations[currentLanguage][key]) {
                    element.textContent = translations[currentLanguage][key];
                }
            });
        }

        // 从本地存储显示用户信息
        function displayUserInfoFromLocal() {
            const username = localStorage.getItem('username') || (translations[currentLanguage]['tasks.unknown_user'] || '未知用户');
            const loginTime = localStorage.getItem('loginTime');

            document.getElementById('username').textContent = username;
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置页面语言
            document.documentElement.lang = currentLanguage === 'zh' ? 'zh-CN' : 'en';

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === currentLanguage);
            });

            updatePageText();
            displayUserInfoFromLocal();

            // 从URL参数获取页码
            const urlParams = new URLSearchParams(window.location.search);
            const pageFromUrl = parseInt(urlParams.get('np')) || 1;
            loadTasks(pageFromUrl);

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });
        });
    </script>

    <!-- 国际化脚本 -->
    <script>
        // 监听语言变化事件，重新渲染页面内容
        window.addEventListener('languageChanged', function(e) {
            // 重新渲染任务列表以更新动态生成的文本
            if (allTasks.length > 0) {
                renderTasks();
            }

            // 更新时间戳文本
            const timestampEl = document.getElementById('last-updated');
            if (timestampEl.textContent) {
                const lastUpdatedText = translations[currentLanguage]['tasks.last_updated'] || '最后更新';
                const totalTasksText = translations[currentLanguage]['tasks.total_tasks'] || '总任务数';
                const currentTime = new Date().toLocaleString(e.detail.language === 'en' ? 'en-US' : 'zh-CN');
                timestampEl.textContent = `${lastUpdatedText}: ${currentTime} | ${totalTasksText}: ${totalTasks}`;
            }
        });
</script>
{% endblock %}
