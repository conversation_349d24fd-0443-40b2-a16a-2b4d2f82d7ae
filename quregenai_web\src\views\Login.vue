<template>
  <div class="login-page">
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'zh' }"
        @click="switchLanguage('zh')"
      >
        🇨🇳 中文
      </button>
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'en' }"
        @click="switchLanguage('en')"
      >
        🇺🇸 EN
      </button>
    </div>

    <div class="login-container">
      <div class="login-header">
        <h1>{{ t('login.title') }}</h1>
        <p>{{ t('login.subtitle') }}</p>
      </div>
      
      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <label for="contactInfo">{{ t('common.mobile-label') }}</label>
          <input 
            type="text" 
            id="contactInfo" 
            v-model="contactInfo" 
            :placeholder="t('common.mobile-placeholder')"
            required
          >
        </div>
        
        <div class="form-group">
          <label for="password">{{ t('common.password-label') }}</label>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            :placeholder="t('common.password-placeholder')"
            required
          >
          <div class="forgot-password">
            <router-link to="/reset-password">{{ t('login.reset-password') }}</router-link>
          </div>
        </div>
        
        <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
        
        <button type="submit" class="login-btn">{{ t('common.login-btn') }}</button>
        
        <div class="auth-divider">
          <span>{{ t('login.no-account') }}</span>
        </div>
        
        <button type="button" class="register-btn" @click="goToRegister">{{ t('login.register-btn') }}</button>
      </form>
      
      <div class="features">
        <h3>{{ t('login.features-title') }}</h3>
        <div class="feature-list">
          <span class="feature-item">{{ t('login.feature-hts') }}</span>
          <span class="feature-item">{{ t('login.feature-pocket') }}</span>
          <span class="feature-item">{{ t('login.feature-folding') }}</span>
          <span class="feature-item">{{ t('login.feature-quantum') }}</span>
          <span class="feature-item">{{ t('login.feature-ai-mcp') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { userLogin } from '../api/login'
import { checkAuthStatus } from '../utils/auth'
import { currentLanguage, switchLanguage, t } from '../utils/i18n'

export default {
  name: 'LoginView',
  data() {
    return {
      contactInfo: '',
      password: '',
      errorMessage: '',
    }
  },
  computed: {
    currentLanguage() {
      return currentLanguage.value
    }
  },
  mounted() {
    // 清除本地存储（如果有残留的登录状态）
    this.clearStaleLoginState();
    // 检查服务端认证状态
    this.checkAuthStatus();
    // 监听语言变化
    window.addEventListener('storage', this.handleLanguageChange);
  },
  beforeUnmount() {
    window.removeEventListener('storage', this.handleLanguageChange);
  },
  methods: {
    t,
    switchLanguage,
    handleLanguageChange(e) {
      if (e.key === 'language') {
        this.$forceUpdate();
      }
    },
    clearStaleLoginState() {
      // 只清除客户端存储，不发送请求
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('username');
      localStorage.removeItem('userId');
      localStorage.removeItem('loginTime');
    },
    validateContact(contact) {
      const mobileRegex = /^1[3-9]\d{9}$/;
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return mobileRegex.test(contact) || emailRegex.test(contact);
    },
    checkAuthStatus() {
      // 使用统一的认证状态检查函数
      checkAuthStatus()
        .then(result => {
          if (result.isLoggedIn) {
            // 如果已登录，重定向到首页
            this.$router.push('/');
          }
          // 如果未登录，继续显示登录表单
        })
        .catch(error => {
          console.error('Error checking auth status:', error);
          // 默认继续显示登录表单
        });
    },
    handleLogin() {
      // 重置错误消息
      this.errorMessage = '';
      
      // 验证手机号码或邮箱格式
      if (!this.validateContact(this.contactInfo)) {
        this.errorMessage = this.t('login.mobile-invalid');
        setTimeout(() => {
          this.errorMessage = '';
        }, 3000);
        return;
      }
      
      // 发送登录请求 - 使用新的API格式
      fetch('/api/weblogin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          contactInfo: this.contactInfo, 
          password: this.password 
        }),
        credentials: 'include'
      })
      .then(response => response.json())
      .then(data => {
        if (data.success && data.loginStatus) {
          localStorage.setItem('isLoggedIn', 'true');
          localStorage.setItem('username', data.user.mobile);
          localStorage.setItem('userId', data.user.user_id);
          localStorage.setItem('loginTime', new Date().toISOString());
          this.$router.push('/');
        } else {
          this.errorMessage = data.message || this.t('login.failed');
          setTimeout(() => {
            this.errorMessage = '';
          }, 3000);
        }
      })
      .catch(error => {
        console.error('Login error:', error);
        this.errorMessage = this.t('common.network-error');
        setTimeout(() => {
          this.errorMessage = '';
        }, 3000);
      });
    },
    goToRegister() {
      this.$router.push('/register');
    }
  }
}
</script>

<style scoped>
@import '../assets/styles/login.css';
</style> 