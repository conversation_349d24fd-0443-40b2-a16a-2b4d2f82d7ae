/**
 * Authentication Module
 * 用户认证模块
 * 
 * 功能: 登录、注册、密码重置等认证相关功能
 * 依赖: common.js
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

(function() {
    'use strict';

    // ===== 私有变量 =====
    let isSubmitting = false;

    // ===== 表单验证 =====
    const Validator = {
        // 验证邮箱
        email: function(email) {
            const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return pattern.test(email);
        },

        // 验证密码强度
        password: function(password) {
            // 至少8位，包含字母和数字
            const pattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
            return pattern.test(password);
        },

        // 验证用户名
        username: function(username) {
            // 3-20位，字母数字下划线
            const pattern = /^[a-zA-Z0-9_]{3,20}$/;
            return pattern.test(username);
        },

        // 验证手机号
        phone: function(phone) {
            const pattern = /^1[3-9]\d{9}$/;
            return pattern.test(phone);
        }
    };

    // ===== 表单处理 =====
    function handleLogin(form) {
        if (isSubmitting) return;

        const formData = new FormData(form);
        const email = formData.get('email');
        const password = formData.get('password');

        // 基础验证
        if (!email || !password) {
            showMessage('请填写完整的登录信息', 'error');
            return;
        }

        if (!Validator.email(email)) {
            showMessage('请输入有效的邮箱地址', 'error');
            return;
        }

        isSubmitting = true;
        showLoading(form, true);

        // 发送登录请求
        fetch('/api/login', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 保存登录状态
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('username', data.username);
                localStorage.setItem('userId', data.userId);
                localStorage.setItem('loginTime', new Date().toISOString());

                showMessage('登录成功，正在跳转...', 'success');
                
                // 跳转到主页
                setTimeout(() => {
                    window.location.href = data.redirect || 'home';
                }, 1000);
            } else {
                showMessage(data.message || '登录失败', 'error');
            }
        })
        .catch(error => {
            console.error('登录错误:', error);
            showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
            isSubmitting = false;
            showLoading(form, false);
        });
    }

    function handleRegister(form) {
        if (isSubmitting) return;

        const formData = new FormData(form);
        const username = formData.get('username');
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        // 基础验证
        if (!username || !email || !password || !confirmPassword) {
            showMessage('请填写完整的注册信息', 'error');
            return;
        }

        if (!Validator.username(username)) {
            showMessage('用户名必须是3-20位字母、数字或下划线', 'error');
            return;
        }

        if (!Validator.email(email)) {
            showMessage('请输入有效的邮箱地址', 'error');
            return;
        }

        if (!Validator.password(password)) {
            showMessage('密码必须至少8位，包含字母和数字', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showMessage('两次输入的密码不一致', 'error');
            return;
        }

        isSubmitting = true;
        showLoading(form, true);

        // 发送注册请求
        fetch('/api/register', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('注册成功，请登录', 'success');
                
                // 跳转到登录页
                setTimeout(() => {
                    window.location.href = 'login';
                }, 1500);
            } else {
                showMessage(data.message || '注册失败', 'error');
            }
        })
        .catch(error => {
            console.error('注册错误:', error);
            showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
            isSubmitting = false;
            showLoading(form, false);
        });
    }

    function handleResetPassword(form) {
        if (isSubmitting) return;

        const formData = new FormData(form);
        const email = formData.get('email');

        if (!email) {
            showMessage('请输入邮箱地址', 'error');
            return;
        }

        if (!Validator.email(email)) {
            showMessage('请输入有效的邮箱地址', 'error');
            return;
        }

        isSubmitting = true;
        showLoading(form, true);

        // 发送重置密码请求
        fetch('/api/reset-password', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('重置链接已发送到您的邮箱', 'success');
            } else {
                showMessage(data.message || '发送失败', 'error');
            }
        })
        .catch(error => {
            console.error('重置密码错误:', error);
            showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
            isSubmitting = false;
            showLoading(form, false);
        });
    }

    // ===== UI 辅助函数 =====
    function showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessage = document.querySelector('.auth-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建新消息
        const messageEl = document.createElement('div');
        messageEl.className = `auth-message auth-message--${type}`;
        messageEl.textContent = message;

        // 插入到表单前面
        const form = document.querySelector('form');
        if (form) {
            form.parentNode.insertBefore(messageEl, form);
        }

        // 自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    function showLoading(form, show) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (!submitBtn) return;

        if (show) {
            submitBtn.disabled = true;
            submitBtn.textContent = '处理中...';
            submitBtn.classList.add('is-loading');
        } else {
            submitBtn.disabled = false;
            submitBtn.classList.remove('is-loading');
            
            // 恢复原始文本
            const originalText = submitBtn.dataset.originalText || '提交';
            submitBtn.textContent = originalText;
        }
    }

    // ===== 事件绑定 =====
    function bindEvents() {
        // 表单提交处理
        document.addEventListener('submit', function(e) {
            const form = e.target;
            
            if (form.matches('.js-login-form')) {
                e.preventDefault();
                handleLogin(form);
            } else if (form.matches('.js-register-form')) {
                e.preventDefault();
                handleRegister(form);
            } else if (form.matches('.js-reset-form')) {
                e.preventDefault();
                handleResetPassword(form);
            }
        });

        // 实时验证
        document.addEventListener('input', function(e) {
            const input = e.target;
            if (input.matches('input[type="email"]')) {
                validateField(input, Validator.email);
            } else if (input.matches('input[type="password"]')) {
                validateField(input, Validator.password);
            } else if (input.matches('input[name="username"]')) {
                validateField(input, Validator.username);
            }
        });

        // 密码确认验证
        document.addEventListener('input', function(e) {
            const input = e.target;
            if (input.matches('input[name="confirmPassword"]')) {
                const password = document.querySelector('input[name="password"]');
                if (password) {
                    const isValid = input.value === password.value;
                    toggleFieldValidation(input, isValid);
                }
            }
        });
    }

    function validateField(input, validator) {
        const isValid = validator(input.value);
        toggleFieldValidation(input, isValid);
        return isValid;
    }

    function toggleFieldValidation(input, isValid) {
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        }
    }

    // ===== 初始化 =====
    function init() {
        // 保存提交按钮的原始文本
        document.querySelectorAll('button[type="submit"]').forEach(btn => {
            btn.dataset.originalText = btn.textContent;
        });

        // 绑定事件
        bindEvents();

        // 如果已经登录，跳转到主页
        if (localStorage.getItem('isLoggedIn') === 'true') {
            window.location.href = 'home';
        }
    }

    // ===== 页面加载完成后初始化 =====
    document.addEventListener('DOMContentLoaded', init);

    // ===== 全局暴露 =====
    window.Auth = {
        Validator,
        showMessage,
        showLoading
    };

})();
