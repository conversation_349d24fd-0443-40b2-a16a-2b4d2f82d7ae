<template>
  <header class="header">
    <router-link to="/" style="text-decoration: none;">
      <div class="logo">{{ t('common.platform-name') }}</div>
    </router-link>
    <div class="user-info">
      <!-- 语言切换按钮 -->
      <div class="language-switcher">
        <button
          class="language-btn"
          :class="{ active: currentLanguage === 'zh' }"
          @click="switchLanguage('zh')"
        >
          🇨🇳 中文
        </button>
        <button
          class="language-btn"
          :class="{ active: currentLanguage === 'en' }"
          @click="switchLanguage('en')"
        >
          🇺🇸 EN
        </button>
      </div>
      <template v-if="isLoggedIn">
        <div class="user-avatar">{{ avatarText }}</div>
        <div class="user-details">
          <div class="username">{{ username }}</div>
          <div class="login-time">{{ loginTimeDisplay }}</div>
        </div>
        <button class="logout-btn" @click="logout">{{ t('common.logout-btn') }}</button>
      </template>
    </div>
  </header>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { logout as apiLogout, isLogin } from '../../api/login'
import { t, switchLanguage as switchLang, currentLanguage } from '../../utils/i18n'

export default {
  name: 'Navbar',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const isLoggedIn = ref(false)
    const username = ref('')
    const loginTime = ref('')
    const loginTimeDisplay = ref(t('home.just-logged-in'))

    // 计算用户头像显示的文字
    const avatarText = computed(() => {
      return username.value.charAt(0).toUpperCase()
    })

    onMounted(() => {
      checkAuthStatus()
    })

    // 监听路由变化，每次路由变化时检查登录状态
    watch(() => route.path, () => {
      checkAuthStatus()
    })

    // 切换语言函数
    const switchLanguage = (lang) => {
      switchLang(lang)
      
      // 更新登录时间显示
      if (loginTime.value) {
        updateLoginTimeDisplay()
      }
    }

    // 使用API验证登录状态
    const checkAuthStatus = () => {
      isLogin()
        .then(data => {
          if (data.loginStatus) {
            // 如果服务器认为已登录，更新本地状态
            isLoggedIn.value = true
            username.value = data.userInfo.mobile

            // 更新localStorage
            localStorage.setItem('isLoggedIn', 'true')
            localStorage.setItem('username', data.userInfo.mobile)
            localStorage.setItem('user_id', data.userInfo.user_id)
            localStorage.setItem('loginTime', data.userInfo.lastLoginTime)
            loginTime.value = data.userInfo.lastLoginTime

            if (loginTime.value) {
              updateLoginTimeDisplay()
            }
          } else {
            // 如果服务器认为未登录，清除本地状态
            clearLocalStorage()
          }
        })
        .catch(error => {
          console.error('检查登录状态出错:', error)
          // 出错时尝试从本地存储获取状态
          checkLocalLoginStatus()
        })
    }

    // 从本地存储获取登录状态（作为备用方案）
    const checkLocalLoginStatus = () => {
      const loggedIn = localStorage.getItem('isLoggedIn')
      if (loggedIn === 'true') {
        isLoggedIn.value = true
        username.value = localStorage.getItem('username') || '未知用户'
        loginTime.value = localStorage.getItem('loginTime')

        if (loginTime.value) {
          updateLoginTimeDisplay()
        }
      } else {
        isLoggedIn.value = false
        username.value = ''
      }
    }

    // 更新登录时间显示
    const updateLoginTimeDisplay = () => {
      const loginDate = new Date(loginTime.value)
      const now = new Date()
      const diffMinutes = Math.floor((now - loginDate) / (1000 * 60))

      let timeText
      if (diffMinutes < 1) {
        timeText = t('home.just-logged-in')
      } else if (diffMinutes < 60) {
        timeText = currentLanguage.value === 'zh' ? `${diffMinutes}${t('home.minutes-ago')}` : `${diffMinutes} ${t('home.minutes-ago')}`
      } else {
        const diffHours = Math.floor(diffMinutes / 60)
        if (diffHours < 24) {
          timeText = currentLanguage.value === 'zh' ? `${diffHours}${t('home.hours-ago')}` : `${diffHours} ${t('home.hours-ago')}`
        } else {
          const diffDays = Math.floor(diffHours / 24)
          timeText = currentLanguage.value === 'zh' ? `${diffDays}${t('home.days-ago')}` : `${diffDays} ${t('home.days-ago')}`
        }
      }

      loginTimeDisplay.value = timeText
    }

    // 清除本地存储
    const clearLocalStorage = () => {
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('username')
      localStorage.removeItem('user_id')
      localStorage.removeItem('loginTime')
      localStorage.removeItem('lastAuthCheck')
      isLoggedIn.value = false
      username.value = ''
    }

    const logout = () => {
      // 调用登出API
      apiLogout()
        .then(() => {
          console.log('退出登录成功')
        })
        .catch(error => {
          console.error('退出登录时发生错误:', error)
        })
        .finally(() => {
          // 总是清除客户端存储并跳转
          clearLocalStorage()
          router.push('/login')
        })
    }

    return {
      isLoggedIn,
      username,
      avatarText,
      loginTimeDisplay,
      currentLanguage,
      t,
      switchLanguage,
      logout
    }
  }
}
</script>

<style scoped>
.header {
  background: white;
  height: 60px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 语言切换按钮样式 */
.language-switcher {
  display: flex;
  gap: 0.5rem;
  margin-right: 1rem;
}

.language-btn {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  color: #666;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.75rem;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.language-btn:hover {
  background: #e9ecef;
  border-color: #667eea;
  color: #667eea;
}

.language-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.login-time {
  font-size: 0.75rem;
  color: #666;
}

.logout-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.logout-btn:hover {
  background: #c0392b;
}

@media (max-width: 768px) {
  .language-switcher {
    margin-right: 0.5rem;
  }

  .language-btn {
    padding: 0.2rem 0.6rem;
    font-size: 0.7rem;
  }
}
</style>