# 国际化使用指南

## 新的分层结构

现在翻译文本按页面/模块组织，避免了key冲突问题：

```javascript
// 旧的扁平结构
t('login-btn')  // 可能在多个页面都有登录按钮

// 新的分层结构
t('common.login-btn')     // 通用组件中的登录按钮
t('login.register-btn')   // 登录页面中的注册按钮
t('register.back-to-login') // 注册页面中的返回登录
```

## 组织结构

### 1. common - 通用组件
存放在多个页面都会用到的文本，如：
- 平台名称
- 通用按钮（登录、注册、密码等）
- 通用错误信息

### 2. home - 首页
首页特有的文本内容

### 3. modules - 功能模块
各个功能模块的描述和详情

### 4. login - 登录页面
登录页面特有的文本

### 5. register - 注册页面
注册页面特有的文本

## 使用方法

### 在Vue组件中使用

```vue
<template>
  <div>
    <!-- 使用通用文本 -->
    <h1>{{ t('common.platform-name') }}</h1>
    
    <!-- 使用页面特定文本 -->
    <h2>{{ t('home.welcome-title') }}</h2>
    <p>{{ t('home.welcome-text') }}</p>
    
    <!-- 使用模块文本 -->
    <div>{{ t('modules.autodock-desc') }}</div>
    
    <!-- 登录相关 -->
    <button>{{ t('login.register-btn') }}</button>
  </div>
</template>

<script setup>
import { t } from '@/utils/i18n'
</script>
```

### 在JavaScript中使用

```javascript
import { t } from '@/utils/i18n'

// 显示错误信息
console.error(t('common.network-error'))

// 设置页面标题
document.title = t('register.page-title')

// 表单验证
if (!isValid) {
  alert(t('register.password-invalid'))
}
```

## 添加新的翻译

### 1. 确定分类
首先确定新文本属于哪个分类：
- 如果是多个页面共用的，放在 `common`
- 如果是特定页面的，放在对应页面分类
- 如果是新页面，创建新的分类

### 2. 添加翻译
在 `src/utils/i18n.js` 中同时添加中英文翻译：

```javascript
// 中文
zh: {
  newPage: {
    'title': '新页面标题',
    'description': '新页面描述'
  }
}

// 英文
en: {
  newPage: {
    'title': 'New Page Title',
    'description': 'New Page Description'
  }
}
```

### 3. 使用新翻译
```javascript
t('newPage.title')
t('newPage.description')
```

## 优势

1. **避免key冲突**：不同页面可以有相同的key名称
2. **更好的组织**：按功能模块分类，便于维护
3. **清晰的命名空间**：从key就能知道文本属于哪个页面
4. **易于扩展**：添加新页面时只需要添加新的分类
5. **团队协作**：不同开发者可以独立维护不同模块的翻译

## 迁移指南

如果你有使用旧的扁平结构的代码，需要更新：

```javascript
// 旧的方式
t('platform-name')     → t('common.platform-name')
t('login-btn')         → t('common.login-btn')
t('welcome-title')     → t('home.welcome-title')
t('login-title')       → t('login.title')
t('register-title')    → t('register.title')
```

建议使用全局搜索替换来批量更新现有代码。