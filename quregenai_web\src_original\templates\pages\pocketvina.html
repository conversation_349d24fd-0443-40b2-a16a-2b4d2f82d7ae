{% extends "layout_with_nav.html" %}

{% block title %}PocketVina - QureGenAI 药物设计平台{% endblock %}

{% block page_css %}
<link rel="stylesheet" href="/src/styles/pages/pocketvina.css">
{% endblock %}

{% block content %}

            <div class="page-title">
                <div class="pocketvina-icon">🔬</div>
                <div>
                    <h1 data-i18n="pocketvina-title">PocketVina 分子对接</h1>
                </div>
            </div>
            <p class="page-subtitle" data-i18n="pocketvina-subtitle">
                基于口袋预测的分子对接工具，支持多个分子和蛋白质文件的高通量筛选分析
            </p>

            <div class="form-container">
                <form id="pocketvinaForm">
                    <div class="form-section">
                        <h3>
                            <span>📝</span>
                            <span data-i18n="task-settings-title">任务设置</span>
                        </h3>

                        <div class="form-group">
                            <label for="jobName" data-i18n="job-name-label">任务名称（可选）</label>
                            <input type="text" id="jobName" name="jobName" class="form-control" 
                                   placeholder="自定义任务名称" data-i18n-placeholder="job-name-placeholder">
                            <div class="help-text" data-i18n="job-name-help">
                                为您的对接任务指定一个便于识别的名称，留空将自动生成
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>
                            <span>🧬</span>
                            <span data-i18n="smiles-input-title">分子输入</span>
                        </h3>

                        <div class="form-group">
                            <label for="smilesInput" data-i18n="smiles-input-label">SMILES字符串</label>
                            <textarea id="smilesInput" name="smilesInput" class="form-control" 
                                      placeholder="输入一个或多个SMILES，用换行符分隔&#10;例如：&#10;O=C(O)c1ccccc1O&#10;Cn1cnc2c1c(=O)n(C)c(=O)n2C" 
                                      data-i18n-placeholder="smiles-input-placeholder"></textarea>
                            <div class="help-text" data-i18n="smiles-input-help">
                                输入一个或多个分子的SMILES表示，多个分子用换行符分隔
                            </div>
                        </div>

                        <div class="smiles-examples">
                            <h4 data-i18n="example-molecules">示例分子：</h4>
                            <div class="example-smiles">
                                <div class="example-smiles-item" onclick="useExampleSmiles('O=C(O)c1ccccc1O')" title="水杨酸">水杨酸</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('Cn1cnc2c1c(=O)n(C)c(=O)n2C')" title="咖啡因">咖啡因</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('CCO')" title="乙醇">乙醇</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('CC(=O)O')" title="乙酸">乙酸</div>
                                <div class="example-smiles-item" onclick="useExampleSmiles('c1ccccc1')" title="苯">苯</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>
                            <span>📁</span>
                            <span data-i18n="protein-files-title">蛋白质文件</span>
                        </h3>

                        <div class="file-upload-section">
                            <div class="file-drop-zone" id="fileDropZone">
                                <div class="file-drop-zone-icon">📁</div>
                                <div class="file-drop-zone-text" data-i18n="file-drop-zone-text">拖拽文件到此处或点击选择</div>
                                <div class="file-drop-zone-hint" data-i18n="file-drop-zone-hint">支持 .pdb, .pdbqt 格式</div>
                                <input type="file" id="fileInput" class="file-input" multiple accept=".pdb,.pdbqt">
                            </div>

                            <div class="uploaded-files">
                                <h4 data-i18n="uploaded-files-title">已上传文件</h4>
                                <ul class="file-list" id="fileList">
                                    <li class="no-files" data-i18n="no-files-uploaded">暂无文件上传</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn" id="submitBtn">
                        <span data-i18n="submit-docking">提交分子对接</span>
                    </button>
                </form>
            </div>

            <!-- PocketVina任务历史区域 -->
            <section class="section" id="pocketvina-history-section" style="margin-top: 2rem;">
                <h2 class="section-title" data-i18n="task-history-title">
                    📋 PocketVina任务历史
                </h2>
                
                <div class="tasks-container">
                    <div class="tasks-header">
                        <button class="refresh-btn" onclick="loadPocketVinaTaskHistory()" data-i18n="refresh-btn">
                            刷新
                        </button>
                    </div>
                    
                    <div id="pocketvina-tasks-list" class="tasks-list">
                        <!-- 任务列表将在这里动态加载 -->
                        <div class="loading-placeholder">
                            <div class="loading-spinner"></div>
                            <p data-i18n="loading-task-history">加载任务历史中...</p>
                        </div>
                    </div>

                    <!-- 添加分页控件 -->
                    <div id="pocketvina-tasks-pagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="pocketvina-pagination-info-text"></span>
                        </div>
                        <div class="pagination-controls">
                            <button id="pocketvina-prev-page-btn" class="pagination-btn" onclick="loadPocketVinaTaskHistoryPage(currentPocketVinaPage - 1)">
                                <span data-i18n="prev-page">上一页</span>
                            </button>
                            <div id="pocketvina-page-numbers" class="page-numbers">
                                <!-- 页码按钮将在这里动态生成 -->
                            </div>
                            <button id="pocketvina-next-page-btn" class="pagination-btn" onclick="loadPocketVinaTaskHistoryPage(currentPocketVinaPage + 1)">
                                <span data-i18n="next-page">下一页</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>


    <!-- 通知消息 -->
    <div id="notification" class="notification"></div>
{% endblock %}

{% block page_js %}
<script>
        // 多语言文本配置
        const translations = {
            zh: {
                'pocketvina-title': 'PocketVina 分子对接',
                'pocketvina-subtitle': '基于口袋预测的分子对接工具，支持多个分子和蛋白质文件的高通量筛选分析',
                'task-settings-title': '任务设置',
                'job-name-label': '任务名称（可选）',
                'job-name-placeholder': '自定义任务名称',
                'job-name-help': '为您的对接任务指定一个便于识别的名称，留空将自动生成',
                'smiles-input-title': '分子输入',
                'smiles-input-label': 'SMILES字符串',
                'smiles-input-placeholder': '输入一个或多个SMILES，用换行符分隔\n例如：\nO=C(O)c1ccccc1O\nCn1cnc2c1c(=O)n(C)c(=O)n2C',
                'smiles-input-help': '输入一个或多个分子的SMILES表示，多个分子用换行符分隔',
                'example-molecules': '示例分子：',
                'protein-files-title': '蛋白质文件',
                'file-drop-zone-text': '拖拽文件到此处或点击选择',
                'file-drop-zone-hint': '支持 .pdb, .pdbqt 格式',
                'uploaded-files-title': '已上传文件',
                'no-files-uploaded': '暂无文件上传',
                'submit-docking': '提交分子对接',
                'task-history-title': 'PocketVina任务历史',
                'my-pocketvina-tasks': '我的PocketVina任务',
                'refresh-btn': '刷新',
                'loading-task-history': '加载任务历史中...',
                'no-pocketvina-tasks': '暂无PocketVina任务',
                'submit-first-task': '提交第一个PocketVina任务开始使用',
                'status-completed': '已完成',
                'status-failed': '失败',
                'status-running': '运行中',
                'status-pending': '等待中',
                'load-failed': '加载失败',
                'unknown-error': '未知错误',
                'network-error-check-connection': '网络错误，请检查连接',
                'prev-page': '上一页',
                'next-page': '下一页'

            },
            en: {
                'pocketvina-title': 'PocketVina Molecular Docking',
                'pocketvina-subtitle': 'Pocket prediction-based molecular docking tool supporting high-throughput docking analysis of multiple molecules and protein files',
                'task-settings-title': 'Task Settings',
                'job-name-label': 'Job Name (Optional)',
                'job-name-placeholder': 'Custom job name',
                'job-name-help': 'Specify a recognizable name for your docking task, leave blank for auto-generation',
                'smiles-input-title': 'Molecule Input',
                'smiles-input-label': 'SMILES String',
                'smiles-input-placeholder': 'Enter one or more SMILES separated by newlines\nExample:\nO=C(O)c1ccccc1O\nCn1cnc2c1c(=O)n(C)c(=O)n2C',
                'smiles-input-help': 'Enter SMILES representation of one or more molecules, separate multiple molecules with newlines',
                'example-molecules': 'Example Molecules:',
                'protein-files-title': 'Protein Files',
                'file-drop-zone-text': 'Drag files here or click to select',
                'file-drop-zone-hint': 'Supports .pdb, .pdbqt formats',
                'uploaded-files-title': 'Uploaded Files',
                'no-files-uploaded': 'No files uploaded',
                'submit-docking': 'Submit Molecular Docking',
                'task-history-title': 'PocketVina Task History',
                'my-pocketvina-tasks': 'My PocketVina Tasks',
                'refresh-btn': 'Refresh',
                'loading-task-history': 'Loading task history...',
                'no-pocketvina-tasks': 'No PocketVina tasks',
                'submit-first-task': 'Submit your first PocketVina task to get started',
                'status-completed': 'Completed',
                'status-failed': 'Failed',
                'status-running': 'Running',
                'status-pending': 'Pending',
                'load-failed': 'Load failed',
                'unknown-error': 'Unknown error',
                'network-error-check-connection': 'Network error, please check connection',
                'prev-page': 'Previous',
                'next-page': 'Next'

            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 文件管理
        let uploadedFiles = [];

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            
            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });
            
            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            // 更新文档标题
            document.title = lang === 'zh' ? 'PocketVina - QureGenAI 药物设计平台' : 'PocketVina - QureGenAI Drug Design Platform';
            
            // 更新页面文本
            updatePageText(lang);
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];
            
            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t[key]) {
                    element.textContent = t[key];
                }
            });

            // 更新placeholder
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (t[key]) {
                    element.placeholder = t[key];
                }
            });
        }

        // 使用示例SMILES
        function useExampleSmiles(smiles) {
            const textarea = document.getElementById('smilesInput');
            const currentValue = textarea.value.trim();
            if (currentValue) {
                textarea.value = currentValue + '\n' + smiles;
            } else {
                textarea.value = smiles;
            }
        }

        // 文件拖拽上传
        function initializeFileUpload() {
            const dropZone = document.getElementById('fileDropZone');
            const fileInput = document.getElementById('fileInput');

            // 点击上传
            dropZone.addEventListener('click', () => {
                fileInput.click();
            });

            // 拖拽事件
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });

            // 文件选择事件
            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                handleFiles(files);
            });
        }

        // 处理文件
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (file.type === 'text/plain' || file.name.endsWith('.pdb') || file.name.endsWith('.pdbqt')) {
                    addFile(file);
                } else {
                    showNotification(`不支持的文件格式: ${file.name}`, 'error');
                }
            });
        }

        // 添加文件
        function addFile(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const fileData = {
                    name: file.name,
                    content: e.target.result,
                    size: file.size,
                    selected: false
                };
                uploadedFiles.push(fileData);
                updateFileList();
            };
            reader.readAsText(file);
        }

        // 更新文件列表
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            
            if (uploadedFiles.length === 0) {
                fileList.innerHTML = '<li class="no-files" data-i18n="no-files-uploaded">暂无文件上传</li>';
                return;
            }

            fileList.innerHTML = uploadedFiles.map((file, index) => `
                <li class="file-item ${file.selected ? 'selected' : ''}">
                    <input type="checkbox" ${file.selected ? 'checked' : ''} 
                           onchange="toggleFileSelection(${index})">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                    <button class="remove-file" onclick="removeFile(${index})">×</button>
                </li>
            `);
        }

        // 切换文件选择状态
        function toggleFileSelection(index) {
            uploadedFiles[index].selected = !uploadedFiles[index].selected;
            updateFileList();
        }

        // 移除文件
        function removeFile(index) {
             uploadedFiles.splice(index, 1);  // 修复参数错误：应该是splice(index, 1)而不是splice(index, 0, 1)
             updateFileList();
         }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 显示通知
        function showNotification(message, type = 'info', duration = 4000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // 表单提交
        document.getElementById('pocketvinaForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const smilesInput = document.getElementById('smilesInput').value.trim();
            const jobName = document.getElementById('jobName').value.trim();
            const selectedFiles = uploadedFiles.filter(file => file.selected);
            
            if (!smilesInput) {
                showNotification('请输入SMILES字符串', 'error');
                return;
            }
            
            if (selectedFiles.length === 0) {
                showNotification('请选择至少一个蛋白质文件', 'error');
                return;
            }

            // 禁用提交按钮
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';

            try {
                const requestData = {
                    smiles_input: smilesInput
                };
                
                if (jobName) {
                    requestData.job_name = jobName;
                }

                // 准备蛋白质文件数据
                requestData.protein_files = selectedFiles.map(file => ({
                    name: file.name,
                    content: file.content
                }));

                console.log('提交数据:', requestData);

                // 这里应该调用实际的API
                const response = await fetch('/api/pocketvina/dock', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                showNotification('任务提交成功！', 'success');
                
            } catch (error) {
                console.error('提交任务时出错:', error);
                showNotification('提交失败：' + error.message, 'error');
            } finally {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn !== 'true') {
                window.location.href = 'login';
                return;
            }

            // 显示用户信息
            const username = localStorage.getItem('username') || '未知用户';
            document.getElementById('username').textContent = username;
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
            
            // 初始化语言
            switchLanguage(currentLanguage);

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });

            // 初始化文件上传
            initializeFileUpload();
        });

        // 退出登录
        function logout() {
            fetch('/api/logout', { 
                method: 'POST',
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('退出登录成功');
                })
                .catch(error => {
                    console.error('退出登录时发生错误:', error);
                })
                .finally(() => {
                    clearLocalStorage();
                    window.location.href = 'login';
                });
        }

        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('userId');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('lastAuthCheck');
        }

        // PocketVina分页相关变量
        let currentPocketVinaPage = 1;
        let totalPocketVinaPages = 1;
        let totalPocketVinaItems = 0;
        const pocketVinaPageSize = 5; // 每页显示5条任务

        // 加载PocketVina任务历史
        function loadPocketVinaTaskHistory() {
            loadPocketVinaTaskHistoryPage(1); // 重置到第一页
        }

        // 加载指定页码的PocketVina任务历史
        async function loadPocketVinaTaskHistoryPage(page = 1) {
            console.log(`=== loadPocketVinaTaskHistoryPage called with page: ${page} ===`);
            
            // 验证页码
            if (page < 1) {
                page = 1;
            }
            if (page > totalPocketVinaPages && totalPocketVinaPages > 0) {
                page = totalPocketVinaPages;
            }
            
            currentPocketVinaPage = page;
            
            const tasksList = document.getElementById('pocketvina-tasks-list');
            const t = translations[currentLanguage];
            
            // 显示加载状态
            tasksList.innerHTML = `
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>${t['loading-task-history']}</p>
                </div>
            `;
            
            try {
                const url = `/api/tasks?task_type=pocketvina&page=${page}&page_size=${pocketVinaPageSize}&include_content=false`;
                console.log('Fetching PocketVina tasks from:', url);
                
                const response = await fetch(url);
                console.log('Response status:', response.status);
                const result = await response.json();
                
                if (result.success && result.tasks) {
                    // 更新分页信息
                    if (result.pagination) {
                        currentPocketVinaPage = result.pagination.current_page;
                        totalPocketVinaPages = result.pagination.total_pages;
                        totalPocketVinaItems = result.pagination.total_items;
                        console.log(`PocketVina任务总数: ${totalPocketVinaItems}, 当前页: ${currentPocketVinaPage}/${totalPocketVinaPages}`);
                    } else {
                        // 兼容没有分页信息的情况
                        totalPocketVinaPages = 1;
                        totalPocketVinaItems = result.tasks ? result.tasks.length : 0;
                    }
                    
                    displayPocketVinaTaskHistory(result.tasks);
                    updatePocketVinaPaginationControls();
                    
                } else {
                    tasksList.innerHTML = `
                        <div class="loading-placeholder">
                            <p style="color: #ef4444;">❌ ${t['load-failed']}: ${result.message || t['unknown-error']}</p>
                        </div>
                    `;
                    // 隐藏分页控件
                    const paginationContainer = document.getElementById('pocketvina-tasks-pagination');
                    if (paginationContainer) {
                        paginationContainer.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('加载PocketVina任务历史失败:', error);
                tasksList.innerHTML = `
                    <div class="loading-placeholder">
                        <p style="color: #ef4444;">❌ ${t['network-error-check-connection']}</p>
                    </div>
                `;
            }
        }

        // 显示PocketVina任务历史
        function displayPocketVinaTaskHistory(tasks) {
            const tasksList = document.getElementById('pocketvina-tasks-list');
            const t = translations[currentLanguage];
            // 添加调试信息
            console.log('=== PocketVina 任务列表调试信息 ===');
            console.log('接收到的任务数据:', tasks);
            
            if (!tasks || tasks.length === 0) {
                tasksList.innerHTML = `
                    <div class="loading-placeholder">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">🎯</div>
                        <h4 style="margin-bottom: 0.5rem; color: #6b7280;">${t['no-pocketvina-tasks']}</h4>
                        <p style="color: #9ca3af; font-size: 0.875rem;">${t['submit-first-task']}</p>
                    </div>
                `;
                return;
            }

            const taskItems = tasks.map(task => {
                const taskParams = typeof task.parameters === 'string' ? 
                    JSON.parse(task.parameters) : task.parameters;
                
                const moleculeCount = taskParams?.molecule_count || 0;
                const proteinFiles = taskParams?.protein_files?.length || 0;
                
                const statusClass = task.status === 'completed' ? 'completed' : 
                                   task.status === 'failed' ? 'failed' : 
                                   task.status === 'running' ? 'running' : 'pending';
                
                const statusText = task.status === 'completed' ? t['status-completed'] : 
                                  task.status === 'failed' ? t['status-failed'] : 
                                  task.status === 'running' ? t['status-running'] : t['status-pending'];
                
                const timeAgo = getTimeAgo(task.created_at);
                
                return `
                    <div class="task-item" onclick="viewPocketVinaTaskDetail('${task.task_id}')">
                        <div class="task-header">
                            <div class="task-name">${task.job_name || task.task_id}</div>
                            <div class="task-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="task-info">
                            <div class="task-info-item">
                                <div class="task-info-label">分子数量</div>
                                <div class="task-info-value">${moleculeCount} 个分子</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label">蛋白质文件</div>
                                <div class="task-info-value">${proteinFiles} 个文件</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label">创建时间</div>
                                <div class="task-info-value">${timeAgo}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label">任务ID</div>
                                <div class="task-info-value">${task.task_id}</div>
                            </div>
                        </div>
                        <div class="task-actions">
                            <button class="task-action-btn" onclick="event.stopPropagation(); viewPocketVinaTaskDetail('${task.task_id}')">
                                📊 查看详情
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            tasksList.innerHTML = taskItems;
        }

        // 更新PocketVina分页控件
        function updatePocketVinaPaginationControls() {
            const paginationContainer = document.getElementById('pocketvina-tasks-pagination');
            const paginationInfo = document.getElementById('pocketvina-pagination-info-text');
            const prevBtn = document.getElementById('pocketvina-prev-page-btn');
            const nextBtn = document.getElementById('pocketvina-next-page-btn');
            const pageNumbers = document.getElementById('pocketvina-page-numbers');
            
            if (totalPocketVinaPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            }
            
            paginationContainer.style.display = 'flex';
            
            // 更新分页信息
            const startItem = (currentPocketVinaPage - 1) * pocketVinaPageSize + 1;
            const endItem = Math.min(currentPocketVinaPage * pocketVinaPageSize, totalPocketVinaItems);
            paginationInfo.textContent = `显示 ${startItem}-${endItem} 条，共 ${totalPocketVinaItems} 条`;
            
            // 更新上一页/下一页按钮状态
            prevBtn.disabled = currentPocketVinaPage <= 1;
            nextBtn.disabled = currentPocketVinaPage >= totalPocketVinaPages;
            
            // 生成页码按钮
            let pageNumbersHtml = '';
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPocketVinaPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPocketVinaPages, startPage + maxVisiblePages - 1);
            
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPocketVinaPage ? 'active' : '';
                pageNumbersHtml += `
                    <button class="page-number ${activeClass}" onclick="loadPocketVinaTaskHistoryPage(${i})">
                        ${i}
                    </button>
                `;
            }
            
            pageNumbers.innerHTML = pageNumbersHtml;
        }

        // 查看PocketVina任务详情
        function viewPocketVinaTaskDetail(taskId) {
            window.location.href = `pocketvina_detail?task_id=${taskId}`;
        }

        // 下载PocketVina任务结果
        // async function downloadPocketVinaTaskResults(taskId) {
        //     try {
        //         const response = await fetch(`/api/tasks/${taskId}/download-zip`, {
        //             method: 'POST',
        //             credentials: 'include'
        //         });

        //         if (response.ok) {
        //             const data = await response.json();
        //             if (data.success && data.download_url) {
        //                 window.open(data.download_url, '_blank');
        //                 showNotification('下载已开始', 'success');
        //             } else {
        //                 showNotification('下载失败: ' + (data.message || '未知错误'), 'error');
        //             }
        //         } else {
        //             showNotification('下载失败，请稍后重试', 'error');
        //         }
        //     } catch (error) {
        //         console.error('下载失败:', error);
        //         showNotification('下载出错: ' + error.message, 'error');
        //     }
        // }

        // 获取时间差显示
        function getTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return '刚刚';
            if (diffMins < 60) return `${diffMins} 分钟前`;
            if (diffHours < 24) return `${diffHours} 小时前`;
            if (diffDays < 7) return `${diffDays} 天前`;
            return date.toLocaleDateString();
        }

        // 页面初始化时加载历史任务
        document.addEventListener('DOMContentLoaded', function() {
            // ... 现有初始化代码 ...
            
            // 加载历史任务
            loadPocketVinaTaskHistory();
        });

    </script>
{% endblock %}