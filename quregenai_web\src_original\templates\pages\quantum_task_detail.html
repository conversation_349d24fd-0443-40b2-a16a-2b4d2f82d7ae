{% extends "layout_with_nav.html" %}

{% block title %}任务详情 - TyxonQ{% endblock %}

{% block inline_styles %}
<link rel="stylesheet" href="/src/styles/pages/quantum_task_detail.css">
{% endblock %}

{% block content %}
            <div class="container">
                <div class="page-header">
                    <h1 id="taskTitle" data-i18n="task_detail.title">任务详情</h1>
                    <div>
                        <a href="/qau-cloud/quantum_tasks" class="btn btn-secondary" data-i18n="task_detail.return_list">返回列表</a>
                    </div>
                </div>

        <div id="loading" class="loading" data-i18n="task_detail.loading">
            加载中...
        </div>

        <div id="errorMessage" class="error-message" style="display: none;" data-i18n="task_detail.load_failed">
            加载失败，请稍后重试
        </div>

        <div id="taskContent" style="display: none;">
            <!-- 基本信息 -->
            <div class="task-info">
                <div class="info-card">
                    <h3 data-i18n="task_detail.basic_info">基本信息</h3>
                    <div class="info-item">
                        <span class="info-label" data-i18n="task_detail.fields.id">ID:</span>
                        <span class="info-value" id="taskId">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label" data-i18n="task_detail.fields.title">任务名称:</span>
                        <span class="info-value" id="taskTitleValue">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label" data-i18n="task_detail.fields.description">量子电路:</span>
                        <span class="info-value" id="taskDescription">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label" data-i18n="task_detail.fields.type">计算类型:</span>
                        <span class="info-value" id="taskType">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label" data-i18n="task_detail.fields.priority">执行队列:</span>
                        <span class="info-value" id="taskPriority">-</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3 data-i18n="task_detail.status_info">状态信息</h3>
                    <div class="info-item">
                        <span class="info-label" data-i18n="task_detail.fields.status">状态:</span>
                        <span id="taskStatus" class="status-badge">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label" data-i18n="task_detail.fields.progress">进度:</span>
                        <span class="info-value" id="taskProgressText">-</span>
                    </div>

                </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-section">
                <h3 data-i18n="task_detail.execution_progress">执行进度</h3>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%">0%</div>
                </div>
            </div>

            <!-- 时间线 -->
            <div class="timeline">
                <h3 data-i18n="task_detail.execution_timeline">执行时间线</h3>
                <div id="timelineContent">
                    <!-- 时间线项目将在这里动态生成 -->
                </div>
            </div>

            <!-- 执行结果 -->
            <div id="resultSection" class="result-section" style="display: none;">
                <h3 data-i18n="task_detail.execution_result">执行结果</h3>
                <div id="resultContent" class="result-content"></div>
            </div>

            <!-- 错误信息 -->
            <div id="errorSection" class="result-section" style="display: none;">
                <h3 data-i18n="task_detail.error_info">错误信息</h3>
                <div id="errorContent" class="result-content error-content"></div>
            </div>
        </div>
            </div>
{% endblock %}

{% block page_js %}
{{ super() }}
<script>
        let currentTaskId = null;



        async function loadTaskDetail(taskId) {
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');
            const taskContent = document.getElementById('taskContent');

            loading.style.display = 'block';
            errorMessage.style.display = 'none';
            taskContent.style.display = 'none';

            try {
                // 使用GET方法获取任务详情
                const response = await fetch(`/api/tasks/${taskId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();

                if (!data.success) {
                    throw new Error('API返回失败状态');
                }

                renderTaskDetail(data.task);

            } catch (error) {
                console.error(translations[currentLanguage]['task_detail.load_task_failed'] || '加载任务详情失败:', error);
                const errorMsg = translations[currentLanguage]['task_detail.load_failed'] || '加载任务详情失败，请稍后重试';
                showError(errorMsg);
            } finally {
                loading.style.display = 'none';
            }
        }

        function renderTaskDetail(task) {
            const taskContent = document.getElementById('taskContent');

            // 解析参数和结果
            let parsedParams = {};
            let quantumResult = task.result || {};

            try {
                if (task.parameters) {
                    if (typeof task.parameters === 'string') {
                        // 处理字符串格式的参数
                        const paramStr = task.parameters.replace(/'/g, '"');
                        parsedParams = JSON.parse(paramStr);
                    } else {
                        // 已经是对象，直接赋值
                        parsedParams = task.parameters;
                    }
                }
            } catch (e) {
                console.warn(translations[currentLanguage]['task_detail.parse_params_failed'] || '解析任务参数失败:', e);
            }

            // 基本信息
            const taskIdShort = task.task_id.split('_').pop().substring(0, 8);
            const taskDetailTitle = translations[currentLanguage]['task_detail.quantum_task_detail'] || '量子任务详情';
            document.getElementById('taskTitle').textContent = `${taskDetailTitle} - ${taskIdShort}`;
            document.getElementById('taskId').textContent = task.task_id;
            const taskTitle = translations[currentLanguage]['task_detail.quantum_computing_task'] || '量子计算任务';
            document.getElementById('taskTitleValue').textContent = `${taskTitle} - ${taskIdShort}`;

            const unknownDevice = translations[currentLanguage]['task_detail.unknown_device'] || '未知设备';
            const device = parsedParams.device || quantumResult.device || unknownDevice;
            const qubits = quantumResult.qubits || 0;
            const depth = quantumResult.depth || 0;
            const shots = parsedParams.shots || quantumResult.shots || 0;

            // 构建更科学的描述
            const tianjiProcessor = translations[currentLanguage]['task_detail.tianji_processor'] || '天机量子处理器';
            const homebrewChip = translations[currentLanguage]['task_detail.homebrew_chip'] || '自研量子芯片';
            const deviceName = device.includes('tianji') ? tianjiProcessor :
                             device.includes('homebrew') ? homebrewChip : device;

            const qubitsSystem = translations[currentLanguage]['task_detail.qubits_system'] || '量子比特系统';
            const circuitDepth = translations[currentLanguage]['task_detail.circuit_depth'] || '电路深度';
            const quantumMeasurements = translations[currentLanguage]['task_detail.quantum_measurements'] || '次量子测量';
            const quantumDesc = `${deviceName} | ${qubits}${qubitsSystem} | ${circuitDepth}${depth} | ${shots}${quantumMeasurements}`;

            document.getElementById('taskDescription').textContent = quantumDesc;
            const quantumComputingNisq = translations[currentLanguage]['task_detail.quantum_computing_nisq'] || '量子计算 (NISQ)';
            document.getElementById('taskType').textContent = quantumComputingNisq;
            const standardQueue = translations[currentLanguage]['task_detail.standard_queue'] || '标准队列';
            document.getElementById('taskPriority').textContent = quantumResult.queue || standardQueue;

            // 状态信息
            const statusBadge = document.getElementById('taskStatus');
            statusBadge.textContent = getStatusText(task.status);
            statusBadge.className = `status-badge status-${task.status}`;

            // 计算进度（根据状态）
            let progress = 0;
            if (task.status === 'completed') progress = 100;
            else if (task.status === 'running') progress = 50;
            else if (task.status === 'pending' || task.status === 'pending1') progress = 25;

            document.getElementById('taskProgressText').textContent = `${progress}%`;



            // 进度条
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = `${progress}%`;
            progressFill.textContent = `${progress}%`;

            // 时间线
            renderTimeline(task, quantumResult);

            // 量子计算结果 - 更科学的展示
            if (quantumResult.result && typeof quantumResult.result === 'object') {
                document.getElementById('resultSection').style.display = 'block';
                renderQuantumResults(quantumResult);
            }

            // 错误信息
            if (task.error_message) {
                document.getElementById('errorSection').style.display = 'block';
                document.getElementById('errorContent').textContent = task.error_message;
            }



            taskContent.style.display = 'block';
        }

        function renderQuantumResults(quantumResult) {
            const resultContent = document.getElementById('resultContent');
            const results = quantumResult.result;
            const shots = quantumResult.shots || 0;

            // 计算概率分布
            const totalCounts = Object.values(results).reduce((sum, count) => sum + count, 0);
            const probabilities = Object.entries(results).map(([state, count]) => ({
                state,
                count,
                probability: (count / totalCounts * 100).toFixed(2)
            }));

            // 构建科学的结果展示
            const measurementAnalysis = translations[currentLanguage]['task_detail.measurement_result_analysis'] || '量子测量结果分析';
            const experimentParams = translations[currentLanguage]['task_detail.experiment_params'] || '实验参数';
            const qubitsCount = translations[currentLanguage]['task_detail.qubits_count'] || '量子比特数';
            const depth = translations[currentLanguage]['task_detail.depth'] || '电路深度';
            const measurementCount = translations[currentLanguage]['task_detail.measurement_count'] || '测量次数';
            const quantumDevice = translations[currentLanguage]['task_detail.quantum_device'] || '量子设备';
            const chipExecutionTime = translations[currentLanguage]['task_detail.chip_execution_time'] || '芯片执行时间';
            const quantumStateDistribution = translations[currentLanguage]['task_detail.quantum_state_distribution'] || '量子态概率分布';
            const unknown = translations[currentLanguage]['task_detail.unknown'] || '未知';

            let resultHtml = `
                <div style="font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;">
                    <h4 style="color: #667eea; margin-bottom: 15px;">🔬 ${measurementAnalysis}</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <strong>${experimentParams}:</strong><br>
                        • ${qubitsCount}: ${quantumResult.qubits || 0}<br>
                        • ${depth}: ${quantumResult.depth || 0}<br>
                        • ${measurementCount}: ${shots}<br>
                        • ${quantumDevice}: ${quantumResult.device || unknown}<br>
                        • ${chipExecutionTime}: ${formatDuration(quantumResult.durChip)}<br>
                    </div>

                    <h5 style="color: #764ba2; margin-bottom: 10px;">📊 ${quantumStateDistribution}:</h5>
            `;

            const timesUnit = translations[currentLanguage]['task_detail.times_unit'] || '次';
            const quantumState = translations[currentLanguage]['task_detail.quantum_state'] || '量子态';
            const measurementTimes = translations[currentLanguage]['task_detail.measurement_times'] || '测量次数';
            const probabilityText = translations[currentLanguage]['task_detail.probability'] || '概率';

            // 使用表格显示量子态概率分布
            resultHtml += `
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;">
                    <thead>
                        <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                            <th style="padding: 8px 12px; text-align: left; font-weight: 600; color: #495057;">${quantumState}</th>
                            <th style="padding: 8px 12px; text-align: center; font-weight: 600; color: #495057;">${measurementTimes}</th>
                            <th style="padding: 8px 12px; text-align: center; font-weight: 600; color: #495057;">${probabilityText}</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            probabilities.forEach(({state, count, probability}) => {
                resultHtml += `
                    <tr style="border-bottom: 1px solid #e9ecef;">
                        <td style="padding: 6px 12px; font-weight: bold; color: #333;">|${state}⟩</td>
                        <td style="padding: 6px 12px; text-align: center; color: #666;">${count}${timesUnit}</td>
                        <td style="padding: 6px 12px; text-align: center; color: #666;">${probability}%</td>
                    </tr>
                `;
            });

            resultHtml += `
                    </tbody>
                </table>
            `;

            // 添加量子纠缠和相干性分析（模拟）
            const entropy = calculateQuantumEntropy(probabilities);
            const fidelity = calculateFidelity(probabilities);

            const quantumAnalysis = translations[currentLanguage]['task_detail.quantum_analysis'] || '量子特性分析';
            const shannonEntropy = translations[currentLanguage]['task_detail.shannon_entropy'] || '香农熵';
            const fidelityText = translations[currentLanguage]['task_detail.fidelity'] || '保真度';
            const entropyDesc = translations[currentLanguage]['task_detail.entropy_desc'] || '衡量量子态的随机性';
            const fidelityDesc = translations[currentLanguage]['task_detail.fidelity_desc'] || '量子态制备的准确性';

            resultHtml += `
                    <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                        <h5 style="color: #0c5460; margin-bottom: 10px;">⚛️ ${quantumAnalysis}:</h5>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <strong>${shannonEntropy}:</strong> ${entropy.toFixed(4)}<br>
                                <small style="color: #666;">${entropyDesc}</small>
                            </div>
                            <div>
                                <strong>${fidelityText}:</strong> ${fidelity.toFixed(4)}<br>
                                <small style="color: #666;">${fidelityDesc}</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            resultContent.innerHTML = resultHtml;
        }

        function renderTimeline(task, quantumResult) {
            const timelineContent = document.getElementById('timelineContent');
            let html = '';

            // 创建时间
            if (task.created_at) {
                const taskCreated = translations[currentLanguage]['task_detail.task_created'] || '任务创建';
                const circuitCompiled = translations[currentLanguage]['task_detail.circuit_compiled'] || '量子电路编译完成';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-icon submitted"></div>
                        <div class="timeline-text">
                            <strong>🚀 ${taskCreated}</strong>
                            <br><small>${circuitCompiled}</small>
                        </div>
                        <div class="timeline-time">${formatTimestamp(task.created_at)}</div>
                    </div>
                `;
            }

            // 调度时间（从量子结果中获取）
            if (quantumResult.ts && quantumResult.ts.scheduled) {
                const taskScheduled = translations[currentLanguage]['task_detail.task_scheduled'] || '任务调度';
                const enterQueue = translations[currentLanguage]['task_detail.enter_queue'] || '进入量子处理器调度队列';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-icon submitted"></div>
                        <div class="timeline-text">
                            <strong>📋 ${taskScheduled}</strong>
                            <br><small>${enterQueue}</small>
                        </div>
                        <div class="timeline-time">${formatTimestamp(quantumResult.ts.scheduled)}</div>
                    </div>
                `;
            }

            // 等待时间
            if (quantumResult.ts && quantumResult.ts.pending) {
                const queueWaiting = translations[currentLanguage]['task_detail.queue_waiting'] || '队列等待';
                const waitResources = translations[currentLanguage]['task_detail.wait_resources'] || '等待量子处理器资源分配';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-icon started"></div>
                        <div class="timeline-text">
                            <strong>⏳ ${queueWaiting}</strong>
                            <br><small>${waitResources}</small>
                        </div>
                        <div class="timeline-time">${formatTimestamp(quantumResult.ts.pending)}</div>
                    </div>
                `;
            }

            // 量子芯片执行时间
            if (quantumResult.atChip) {
                const quantumExecution = translations[currentLanguage]['task_detail.quantum_execution'] || '量子执行';
                const executeOnChip = translations[currentLanguage]['task_detail.execute_on_chip'] || '在量子芯片上执行';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-icon started"></div>
                        <div class="timeline-text">
                            <strong>⚛️ ${quantumExecution}</strong>
                            <br><small>${executeOnChip} (${formatDuration(quantumResult.durChip)})</small>
                        </div>
                        <div class="timeline-time">${formatTimestamp(quantumResult.atChip)}</div>
                    </div>
                `;
            }

            // 完成时间
            if (task.completed_at && task.completed_at !== 'NaT') {
                const iconClass = task.status === 'completed' ? 'completed' : 'failed';
                const executionCompleted = translations[currentLanguage]['task_detail.execution_completed'] || '执行完成';
                const executionFailed = translations[currentLanguage]['task_detail.execution_failed'] || '执行失败';
                const statusText = task.status === 'completed' ? `✅ ${executionCompleted}` : `❌ ${executionFailed}`;
                const totalExecutionTime = translations[currentLanguage]['task_detail.total_execution_time'] || '总执行时长';
                const unknown = translations[currentLanguage]['task_detail.unknown'] || '未知';
                const totalTime = quantumResult.runDur ? formatDuration(quantumResult.runDur) : unknown;
                const measurementCompleted = translations[currentLanguage]['task_detail.measurement_completed'] || '量子测量完成，获得';
                const quantumStates = translations[currentLanguage]['task_detail.quantum_states'] || '种量子态';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-icon ${iconClass}"></div>
                        <div class="timeline-text">
                            <strong>${statusText}</strong>
                            <br><small>${totalExecutionTime}: ${totalTime}</small>
                            ${quantumResult.result ? `<br><small>${measurementCompleted} ${Object.keys(quantumResult.result).length} ${quantumStates}</small>` : ''}
                        </div>
                        <div class="timeline-time">${formatTimestamp(task.completed_at)}</div>
                    </div>
                `;
            } else if (quantumResult.ts && quantumResult.ts.completed) {
                const iconClass = task.status === 'completed' ? 'completed' : 'failed';
                const executionCompleted = translations[currentLanguage]['task_detail.execution_completed'] || '执行完成';
                const executionFailed = translations[currentLanguage]['task_detail.execution_failed'] || '执行失败';
                const statusText = task.status === 'completed' ? `✅ ${executionCompleted}` : `❌ ${executionFailed}`;
                const totalExecutionTime = translations[currentLanguage]['task_detail.total_execution_time'] || '总执行时长';
                const unknown = translations[currentLanguage]['task_detail.unknown'] || '未知';
                const totalTime = quantumResult.runDur ? formatDuration(quantumResult.runDur) : unknown;
                const measurementCompleted = translations[currentLanguage]['task_detail.measurement_completed'] || '量子测量完成，获得';
                const quantumStates = translations[currentLanguage]['task_detail.quantum_states'] || '种量子态';
                html += `
                    <div class="timeline-item">
                        <div class="timeline-icon ${iconClass}"></div>
                        <div class="timeline-text">
                            <strong>${statusText}</strong>
                            <br><small>${totalExecutionTime}: ${totalTime}</small>
                            ${quantumResult.result ? `<br><small>${measurementCompleted} ${Object.keys(quantumResult.result).length} ${quantumStates}</small>` : ''}
                        </div>
                        <div class="timeline-time">${formatTimestamp(quantumResult.ts.completed)}</div>
                    </div>
                `;
            }

            timelineContent.innerHTML = html;
        }

        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        function formatTimestamp(timestamp) {
            if (!timestamp || timestamp === 'NaT') return translations[currentLanguage]['task_detail.unknown'] || '未知';
            // 处理字符串时间戳和微秒时间戳
            let date;
            if (typeof timestamp === 'string') {
                date = new Date(timestamp);
            } else {
                date = new Date(timestamp > 1e12 ? timestamp / 1000 : timestamp * 1000);
            }
            return date.toLocaleString(currentLanguage === 'en' ? 'en-US' : 'zh-CN');
        }

        function formatDuration(microseconds) {
            if (!microseconds) return translations[currentLanguage]['task_detail.unknown'] || '未知';

            if (microseconds < 1000) {
                return `${microseconds} μs`;
            } else if (microseconds < 1000000) {
                return `${(microseconds / 1000).toFixed(2)} ms`;
            } else {
                return `${(microseconds / 1000000).toFixed(2)} s`;
            }
        }

        function calculateQuantumEntropy(probabilities) {
            // 计算香农熵 H = -Σ p_i * log2(p_i)
            return probabilities.reduce((entropy, {probability}) => {
                const p = parseFloat(probability) / 100;
                return p > 0 ? entropy - p * Math.log2(p) : entropy;
            }, 0);
        }

        function calculateFidelity(probabilities) {
            // 简化的保真度计算（假设理想态为均匀叠加态）
            const n = probabilities.length;
            const idealProb = 1 / n;
            return probabilities.reduce((fidelity, {probability}) => {
                const p = parseFloat(probability) / 100;
                return fidelity + Math.sqrt(p * idealProb);
            }, 0);
        }

        function getStatusText(status) {
            // 直接使用translations对象
            const statusKey = `tasks.status.${status}`;
            if (translations[currentLanguage] && translations[currentLanguage][statusKey]) {
                return translations[currentLanguage][statusKey];
            }

            // 回退到默认文本
            const statusMap = {
                'pending': '等待中',
                'pending1': '等待中',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消',
                'scheduled': '已调度'
            };
            return statusMap[status] || status;
        }
        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('userId');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('lastAuthCheck');
            localStorage.removeItem('email');
        }
        // 退出登录
        function logout() {
            // 直接使用fetch，避免apiRequest的认证检查逻辑
            fetch('/api/logout', { 
                method: 'POST',
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('退出登录成功');
                })
                .catch(error => {
                    console.error('退出登录时发生错误:', error);
                })
                .finally(() => {
                    // 总是清除客户端存储并跳转
                    clearLocalStorage();
                    window.location.href = 'login';
                });
        }

        // 多语言文本配置
        const translations = {
            zh: {
                'task_detail.title': '任务详情',
                'task_detail.return_list': '返回列表',
                'task_detail.loading': '加载中...',
                'task_detail.load_failed': '加载失败，请稍后重试',
                'task_detail.basic_info': '基本信息',
                'task_detail.status_info': '状态信息',
                'task_detail.execution_progress': '执行进度',
                'task_detail.execution_timeline': '执行时间线',
                'task_detail.execution_result': '执行结果',
                'task_detail.error_info': '错误信息',
                'task_detail.fields.id': 'ID:',
                'task_detail.fields.title': '任务名称:',
                'task_detail.fields.description': '量子电路:',
                'task_detail.fields.type': '计算类型:',
                'task_detail.fields.priority': '执行队列:',
                'task_detail.fields.status': '状态:',
                'task_detail.fields.progress': '进度:',
                'task_detail.fields.estimated_cost': '预估费用:',
                'task_detail.fields.actual_cost': '实际费用:',
                'task_detail.unknown': '未知',
                'task_detail.unknown_user': '未知用户',
                'task_detail.unknown_device': '未知设备',
                'task_detail.load_task_failed': '加载任务详情失败',
                'task_detail.parse_params_failed': '解析任务参数失败',
                'task_detail.quantum_task_detail': '量子任务详情',
                'task_detail.quantum_computing_task': '量子计算任务',
                'task_detail.tianji_processor': '天机量子处理器',
                'task_detail.homebrew_chip': '自研量子芯片',
                'task_detail.qubits_system': '量子比特系统',
                'task_detail.circuit_depth': '电路深度',
                'task_detail.quantum_measurements': '次量子测量',
                'task_detail.quantum_computing_nisq': '量子计算 (NISQ)',
                'task_detail.standard_queue': '标准队列',
                'task_detail.experiment_params': '实验参数',
                'task_detail.qubits_count': '量子比特数',
                'task_detail.depth': '电路深度',
                'task_detail.measurement_count': '测量次数',
                'task_detail.quantum_device': '量子设备',
                'task_detail.chip_execution_time': '芯片执行时间',
                'task_detail.quantum_state_distribution': '量子态概率分布',
                'task_detail.quantum_analysis': '量子特性分析',
                'task_detail.shannon_entropy': '香农熵',
                'task_detail.fidelity': '保真度',
                'task_detail.entropy_desc': '衡量量子态的随机性',
                'task_detail.fidelity_desc': '量子态制备的准确性',
                'task_detail.times_unit': '次',
                'task_detail.quantum_state': '量子态',
                'task_detail.measurement_times': '测量次数',
                'task_detail.probability': '概率',
                'task_detail.task_created': '任务创建',
                'task_detail.circuit_compiled': '量子电路编译完成',
                'task_detail.task_scheduled': '任务调度',
                'task_detail.enter_queue': '进入量子处理器调度队列',
                'task_detail.queue_waiting': '队列等待',
                'task_detail.wait_resources': '等待量子处理器资源分配',
                'task_detail.quantum_execution': '量子执行',
                'task_detail.execute_on_chip': '在量子芯片上执行',
                'task_detail.execution_completed': '执行完成',
                'task_detail.execution_failed': '执行失败',
                'task_detail.total_execution_time': '总执行时长',
                'task_detail.measurement_completed': '量子测量完成，获得',
                'task_detail.quantum_states': '种量子态',
                'task_detail.measurement_result_analysis': '量子测量结果分析',
                'tasks.status.pending': '等待中',
                'tasks.status.pending1': '等待中',
                'tasks.status.running': '运行中',
                'tasks.status.completed': '已完成',
                'tasks.status.failed': '失败',
                'tasks.status.cancelled': '已取消',
                'tasks.status.scheduled': '已调度',
            },
            en: {
                'task_detail.title': 'Task Details',
                'task_detail.return_list': 'Return to List',
                'task_detail.loading': 'Loading...',
                'task_detail.load_failed': 'Load failed, please try again later',
                'task_detail.basic_info': 'Basic Information',
                'task_detail.status_info': 'Status Information',
                'task_detail.execution_progress': 'Execution Progress',
                'task_detail.execution_timeline': 'Execution Timeline',
                'task_detail.execution_result': 'Execution Result',
                'task_detail.error_info': 'Error Information',
                'task_detail.fields.id': 'ID:',
                'task_detail.fields.title': 'Task Name:',
                'task_detail.fields.description': 'Quantum Circuit:',
                'task_detail.fields.type': 'Computing Type:',
                'task_detail.fields.priority': 'Execution Queue:',
                'task_detail.fields.status': 'Status:',
                'task_detail.fields.progress': 'Progress:',
                'task_detail.fields.estimated_cost': 'Estimated Cost:',
                'task_detail.fields.actual_cost': 'Actual Cost:',
                'task_detail.unknown': 'Unknown',
                'task_detail.unknown_user': 'Unknown User',
                'task_detail.unknown_device': 'Unknown Device',
                'task_detail.load_task_failed': 'Failed to load task details',
                'task_detail.parse_params_failed': 'Failed to parse task parameters',
                'task_detail.quantum_task_detail': 'Quantum Task Details',
                'task_detail.quantum_computing_task': 'Quantum Computing Task',
                'task_detail.tianji_processor': 'Tianji Quantum Processor',
                'task_detail.homebrew_chip': 'Homebrew Quantum Chip',
                'task_detail.qubits_system': 'Qubit System',
                'task_detail.circuit_depth': 'Circuit Depth',
                'task_detail.quantum_measurements': ' Quantum Measurements',
                'task_detail.quantum_computing_nisq': 'Quantum Computing (NISQ)',
                'task_detail.standard_queue': 'Standard Queue',
                'task_detail.experiment_params': 'Experiment Parameters',
                'task_detail.qubits_count': 'Qubits Count',
                'task_detail.depth': 'Circuit Depth',
                'task_detail.measurement_count': 'Measurement Count',
                'task_detail.quantum_device': 'Quantum Device',
                'task_detail.chip_execution_time': 'Chip Execution Time',
                'task_detail.quantum_state_distribution': 'Quantum State Probability Distribution',
                'task_detail.quantum_analysis': 'Quantum Characteristics Analysis',
                'task_detail.shannon_entropy': 'Shannon Entropy',
                'task_detail.fidelity': 'Fidelity',
                'task_detail.entropy_desc': 'Measures quantum state randomness',
                'task_detail.fidelity_desc': 'Quantum state preparation accuracy',
                'task_detail.times_unit': ' times',
                'task_detail.quantum_state': 'Quantum State',
                'task_detail.measurement_times': 'Measurement Count',
                'task_detail.probability': 'Probability',
                'task_detail.task_created': 'Task Created',
                'task_detail.circuit_compiled': 'Quantum circuit compilation completed',
                'task_detail.task_scheduled': 'Task Scheduled',
                'task_detail.enter_queue': 'Entered quantum processor scheduling queue',
                'task_detail.queue_waiting': 'Queue Waiting',
                'task_detail.wait_resources': 'Waiting for quantum processor resource allocation',
                'task_detail.quantum_execution': 'Quantum Execution',
                'task_detail.execute_on_chip': 'Executing on quantum chip',
                'task_detail.execution_completed': 'Execution Completed',
                'task_detail.execution_failed': 'Execution Failed',
                'task_detail.total_execution_time': 'Total Execution Time',
                'task_detail.measurement_completed': 'Quantum measurement completed, obtained',
                'task_detail.quantum_states': 'quantum states',
                'task_detail.measurement_result_analysis': 'Quantum Measurement Result Analysis',
                'tasks.status.pending': 'Pending',
                'tasks.status.pending1': 'Pending',
                'tasks.status.running': 'Running',
                'tasks.status.completed': 'Completed',
                'tasks.status.failed': 'Failed',
                'tasks.status.cancelled': 'Cancelled',
                'tasks.status.scheduled': 'Scheduled',
            }
        };

        // 简单的国际化实现
        let currentLanguage = localStorage.getItem('language') || 'en';

        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });

            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';

            updatePageText();

            // 重新渲染页面文本
            updatePageText();
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 从本地存储显示用户信息
        function displayUserInfoFromLocal() {
            const username = localStorage.getItem('username') || (translations[currentLanguage]['task_detail.unknown_user'] || '未知用户');
            const loginTime = localStorage.getItem('loginTime');

            document.getElementById('username').textContent = username;
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
        }

        function updatePageText() {
            const elements = document.querySelectorAll('[data-i18n]');
            elements.forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (translations[currentLanguage] && translations[currentLanguage][key]) {
                    element.textContent = translations[currentLanguage][key];
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置页面语言
            document.documentElement.lang = currentLanguage === 'zh' ? 'zh-CN' : 'en';

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === currentLanguage);
            });

            updatePageText();
            displayUserInfoFromLocal();

            // 从 URL 获取任务 ID
            const urlParams = new URLSearchParams(window.location.search);
            currentTaskId = urlParams.get('id');

            if (currentTaskId) {
                loadTaskDetail(currentTaskId);
            } else {
                const noTaskIdMsg = currentLanguage === 'en' ? 'No task ID specified' : '未指定任务 ID';
                showError(noTaskIdMsg);
            }

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });
        });
    </script>

    <!-- 国际化脚本 -->
    <script>
        // 监听语言变化事件，重新渲染页面内容
        window.addEventListener('languageChanged', function(e) {
            // 重新渲染页面文本
            updatePageText();
        });
</script>
{% endblock %}
