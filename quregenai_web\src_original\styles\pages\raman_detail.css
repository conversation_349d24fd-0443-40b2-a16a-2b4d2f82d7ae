.back-btn {
    background: #87ceeb;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.back-btn:hover {
    background: #5f9ea0;
    transform: translateY(-1px);
}

.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page-header {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    border-left: 4px solid #87ceeb;
}

.page-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #87ceeb 0%, #5f9ea0 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.task-info {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.task-info h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    border-bottom: 2px solid #e8f4f8;
    padding-bottom: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-section {
    background: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e1e5e9;
}

.info-section h4 {
    color: #1976d2;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e8f4f8;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #5a6c7d;
    font-weight: 600;
    font-size: 0.9rem;
}

.info-value {
    color: #2c3e50;
    font-weight: 500;
    font-size: 0.9rem;
    text-align: right;
    max-width: 200px;
    word-break: break-word;
}

.spectrum-chart {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.spectrum-chart h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    border-bottom: 2px solid #e8f4f8;
    padding-bottom: 0.5rem;
}

.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

.loading {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.error {
    text-align: center;
    padding: 3rem;
    color: #c62828;
    background: #ffebee;
    border-radius: 0.75rem;
    border: 1px solid #ffcdd2;
}

.detection-id {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e3a8a;
    background: #f0f8ff;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #87ceeb;
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .content {
        padding: 1rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 300px;
    }
}