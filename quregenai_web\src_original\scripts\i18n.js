/**
 * Internationalization (i18n) Module
 * 国际化多语言模块
 * 
 * 功能: 语言切换、文本翻译、本地化存储
 * 依赖: 无
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

(function() {
    'use strict';

    // ===== 多语言文本配置 =====
    const translations = {
        zh: {
            'platform-name': 'QureGenAI 药物设计平台',
            'logout-btn': '退出登录',
            'sidebar-title': '功能模块',
            'autodock-desc': '高通量筛选与分子对接',
            'diffdock-desc': '口袋预测与分子对接',
            'protenix-desc': '蛋白质结构预测',
            'quantum-tasks-name': '量子计算任务',
            'quantum-tasks-desc': '量子计算任务管理',
            'userprofile-name': '我的（API KEY申请）',
            'userprofile-desc': '账户管理',
            'welcome-title': '欢迎使用 QureGenAI 药物设计平台',
            'welcome-text': '欢迎来到 QureGenAI！\n\n我们是您AI与量子计算驱动的药物发现引擎。您在此可无缝调用顶尖工具：\n\n前沿AI模型：DiffDock（精确分子对接）、AlphaFold（蛋白结构预测）等，赋能分子设计、靶点识别。\n量子计算加速：突破传统计算瓶颈，探索更广阔药物空间。\n灵活使用：支持网页操作、API集成及MCP服务，无缝融入您的工作流。\n我们持续进化：模型库将不断加入尖端工具，助您始终掌握科技前沿。\n\n精准付费，绝不浪费：采用按秒计费模式，真正按用量付费，尤其适合大模型调用，高效灵活。\n\n即刻开始，让QureGenAI加速您的突破！',
            'autodock-detail': 'AutoDock是一套自动化的分子对接软件，用于快速大批量预测小分子配体与蛋白质受体的结合模式和结合亲和力。',
            'diffdock-detail': 'DiffDock是基于扩散模型的新一代扩散分子对接方法，无需指定蛋白质口袋位置，更准确地预测蛋白质-配体复合物的三维结构。',
            'protenix-detail': 'Protenix是先进的蛋白质结构预测平台，能够从氨基酸序列或者结构文件预测蛋白质的三维结构和相互作用等功能。',
            'quantum-computing': 'TyxonQ 量子计算',
            'quantum-detail': '利用量子计算的强大能力加速药物发现过程，突破传统计算瓶颈，探索更广阔的分子空间和复杂的量子化学计算。',
            'classic-algorithm': '经典算法',
            'ai-driven': 'AI驱动',
            'structure-prediction': '结构预测',
            'quantum-acceleration': '量子加速',
            'just-logged-in': '刚刚登录',
            'minutes-ago': '分钟前登录',
            'hours-ago': '小时前登录',
            'days-ago': '天前登录',
            'molmap-desc': 'ADMET 性质预测',
            'molmap-detail': 'MolMap是先进的分子性质预测平台，能够快速准确地预测小分子的ADMET性质，包括吸收、分布、代谢、排泄和毒性等关键药物性质。',
            'admet-prediction': 'ADMET预测',
            'raman-desc': '低成本光谱分析检测',
            'raman-detail': 'Raman光谱检测是一种低成本、高效的光谱分析技术，广泛应用于中草药成分含量检测、农作物农药残留检测、药物成分分析等领域，为食品安全和药物质量控制提供可靠的技术支持。',
            'spectral-analysis': '光谱分析',
            'pocketvina-desc': '全自动口袋发现与高通量筛选',
            'pocketvina-detail': 'PocketVina是新一代全自动口袋发现与高通量筛选工具，无需手动指定结合位点，自动识别蛋白质表面的潜在结合口袋，支持多个分子和蛋白质文件的批量对接分析。',
            'auto-pocket-docking': '全自动口袋对接'
        },
        en: {
            'platform-name': 'QureGenAI Drug Design Platform',
            'logout-btn': 'Logout',
            'sidebar-title': 'Function Modules',
            'autodock-desc': 'High-throughput Screening and Molecular Docking',
            'diffdock-desc': 'Pocket Prediction and Molecular Docking',
            'protenix-desc': 'Protein Structure Prediction',
            'quantum-tasks-name': 'Quantum Computing Tasks',
            'quantum-tasks-desc': 'Quantum Computing Task Management',
            'userprofile-name': 'My Profile(API Key Application)',
            'userprofile-desc': 'Account Management',
            'welcome-title': 'Welcome to QureGenAI Drug Design Platform',
            'welcome-text': 'Welcome to QureGenAI!\n\nWe are your AI and quantum computing-driven drug discovery engine. Here you can seamlessly access cutting-edge tools:\n\nFrontier AI Models: DiffDock (precise molecular docking), AlphaFold (protein structure prediction), and more, empowering molecular design and target identification.\nQuantum Computing Acceleration: Breaking through traditional computational bottlenecks to explore a broader drug space.\nFlexible Usage: Supporting web operations, API integration, and MCP services, seamlessly integrating into your workflow.\nWe Continuously Evolve: Our model library will continuously incorporate cutting-edge tools, helping you stay at the forefront of technology.\n\nPrecise Billing, No Waste: Adopting a per-second billing model, truly pay-as-you-use, especially suitable for large model calls, efficient and flexible.\n\nStart now and let QureGenAI accelerate your breakthroughs!',
            'autodock-detail': 'AutoDock is a suite of automated molecular docking software for rapid and high-throughput prediction of binding modes and binding affinities between small molecule ligands and protein receptors.',
            'diffdock-detail': 'DiffDock is a new generation of diffusion-based molecular docking method that can accurately predict the three-dimensional structure of protein-ligand complexes without specifying protein pocket locations.',
            'protenix-detail': 'Protenix is an advanced protein structure prediction platform capable of predicting protein three-dimensional structures and interactions from amino acid sequences or structure files.',
            'quantum-computing': 'TyxonQ Quantum Computing',
            'quantum-detail': 'Harness the power of quantum computing to accelerate drug discovery processes, breaking through traditional computational bottlenecks and exploring broader molecular spaces and complex quantum chemical calculations.',
            'classic-algorithm': 'Classic Algorithm',
            'ai-driven': 'AI-Driven',
            'structure-prediction': 'Structure Prediction',
            'quantum-acceleration': 'Quantum Acceleration',
            'just-logged-in': 'Just logged in',
            'minutes-ago': 'minutes ago',
            'hours-ago': 'hours ago',
            'days-ago': 'days ago',
            'molmap-desc': 'ADMET Property Prediction',
            'molmap-detail': 'MolMap is an advanced molecular property prediction platform that can quickly and accurately predict ADMET properties of small molecules, including absorption, distribution, metabolism, excretion and toxicity.',
            'admet-prediction': 'ADMET Prediction',
            'raman-desc': 'Low-cost Spectral Analysis Detection',
            'raman-detail': 'Raman spectroscopy detection is a low-cost, efficient spectral analysis technology widely used in traditional Chinese medicine component content detection, crop pesticide residue detection, drug component analysis and other fields, providing reliable technical support for food safety and drug quality control.',
            'spectral-analysis': 'Spectral Analysis',
            'pocketvina-desc': 'Auto Pocket Finding & High-throughput docking',
            'pocketvina-detail': 'PocketVina is a new generation of fully automated pocket finding and high-throughput docking tool that automatically identifies potential binding pockets on protein surfaces without manual specification of binding sites, supporting batch docking analysis of multiple molecules and protein files.',
            'auto-pocket-docking': 'Auto Pocket Docking'
        }
    };

    // ===== 私有变量 =====
    let currentLanguage = localStorage.getItem('language') || 'en';

    // ===== 切换语言函数 =====
    function switchLanguage(lang) {
        currentLanguage = lang;
        localStorage.setItem('language', lang);
        
        // 更新语言按钮状态
        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === lang);
        });
        
        // 更新页面语言
        document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
        
        // 更新文档标题
        updateDocumentTitle(lang);
        
        // 更新页面文本
        updatePageText(lang);
        
        // 更新登录时间显示
        const loginTime = localStorage.getItem('loginTime');
        if (loginTime && window.Navigation) {
            window.Navigation.updateLoginTimeDisplay(loginTime);
        }
    }

    // ===== 更新页面文本 =====
    function updatePageText(lang) {
        const t = translations[lang];
        
        // 更新带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            if (t[key]) {
                element.textContent = t[key];
            }
        });
    }

    // ===== 更新文档标题 =====
    function updateDocumentTitle(lang) {
        const currentPath = window.location.pathname;
        const pageTitles = {
            zh: {
                '/home': 'QureGenAI 药物设计平台 - 主页',
                '/autodock': 'AutoDock - QureGenAI 药物设计平台',
                '/diffdock': 'DiffDock - QureGenAI 药物设计平台',
                '/protenix': 'Protenix - QureGenAI 药物设计平台',
                '/molmap': 'MolMap - QureGenAI 药物设计平台',
                '/pocketvina': 'PocketVina - QureGenAI 药物设计平台',
                '/raman': 'Raman Spectral - QureGenAI 药物设计平台',
                '/quantum_tasks': '量子计算任务 - QureGenAI 药物设计平台',
                '/userprofile': '我的资料 - QureGenAI 药物设计平台'
            },
            en: {
                '/home': 'QureGenAI Drug Design Platform - Home',
                '/autodock': 'AutoDock - QureGenAI Drug Design Platform',
                '/diffdock': 'DiffDock - QureGenAI Drug Design Platform',
                '/protenix': 'Protenix - QureGenAI Drug Design Platform',
                '/molmap': 'MolMap - QureGenAI Drug Design Platform',
                '/pocketvina': 'PocketVina - QureGenAI Drug Design Platform',
                '/raman': 'Raman Spectral - QureGenAI Drug Design Platform',
                '/quantum_tasks': 'Quantum Computing Tasks - QureGenAI Drug Design Platform',
                '/userprofile': 'My Profile - QureGenAI Drug Design Platform'
            }
        };

        const title = pageTitles[lang] && pageTitles[lang][currentPath] || 
                     (lang === 'zh' ? 'QureGenAI 药物设计平台' : 'QureGenAI Drug Design Platform');
        document.title = title;
    }

    // ===== 获取翻译文本 =====
    function getTranslation(key, lang = currentLanguage) {
        return translations[lang] && translations[lang][key] || key;
    }

    // ===== 事件处理 =====
    function bindEvents() {
        // 使用事件委托处理语言切换按钮
        document.addEventListener('click', function(e) {
            if (e.target.matches('.js-language-btn') || e.target.matches('.language-btn')) {
                e.preventDefault();
                const lang = e.target.dataset.lang;
                if (lang) {
                    switchLanguage(lang);
                }
            }
        });
    }

    // ===== 初始化函数 =====
    function init() {
        // 初始化语言设置
        switchLanguage(currentLanguage);
        
        // 绑定事件
        bindEvents();
    }

    // ===== 页面加载完成后初始化 =====
    document.addEventListener('DOMContentLoaded', init);

    // ===== 全局暴露的API =====
    window.translations = translations;
    window.I18n = {
        switchLanguage: switchLanguage,
        updatePageText: updatePageText,
        getTranslation: getTranslation,
        getCurrentLanguage: function() { return currentLanguage; },
        getSupportedLanguages: function() { return Object.keys(translations); }
    };

})();
