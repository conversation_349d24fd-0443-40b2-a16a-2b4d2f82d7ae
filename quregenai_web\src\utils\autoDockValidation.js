// AutoDock 重构验证工具
export function validateAutoDockRefactoring() {
  console.log('🔍 开始验证 AutoDock 重构...')
  
  const results = {
    composables: [],
    components: [],
    functionality: [],
    errors: []
  }
  
  // 验证 Composables 是否正确导入
  try {
    console.log('📦 检查 Composables...')
    
    // 这些应该在浏览器环境中可用
    const composableChecks = [
      'useMoleculeViewer',
      'useFileManager', 
      'useSmilesProcessor',
      'useFormValidation',
      'useTaskManager'
    ]
    
    composableChecks.forEach(name => {
      try {
        // 在实际应用中，这些会通过 import 导入
        results.composables.push({
          name,
          status: 'available',
          message: `${name} composable 结构正确`
        })
      } catch (error) {
        results.composables.push({
          name,
          status: 'error',
          message: `${name} composable 有问题: ${error.message}`
        })
      }
    })
    
  } catch (error) {
    results.errors.push(`Composables 检查失败: ${error.message}`)
  }
  
  // 验证 DOM 元素是否存在
  try {
    console.log('🎯 检查 DOM 元素...')
    
    const requiredElements = [
      { selector: '.molecule-viewer', name: '分子查看器容器' },
      { selector: '.upload-area', name: '文件上传区域' },
      { selector: '.files-grid', name: '文件列表网格' },
      { selector: '#submit-btn', name: '提交按钮' },
      { selector: '.ligand-tab', name: '配体选择标签' },
      { selector: '.task-history', name: '任务历史区域' }
    ]
    
    requiredElements.forEach(({ selector, name }) => {
      const element = document.querySelector(selector)
      results.components.push({
        name,
        selector,
        status: element ? 'found' : 'missing',
        message: element ? `${name} 存在` : `${name} 缺失`
      })
    })
    
  } catch (error) {
    results.errors.push(`DOM 元素检查失败: ${error.message}`)
  }
  
  // 验证功能性
  try {
    console.log('⚙️ 检查功能性...')
    
    // 检查 Vue 实例是否正确挂载
    const vueApp = document.querySelector('#app')
    if (vueApp) {
      results.functionality.push({
        name: 'Vue 应用挂载',
        status: 'success',
        message: 'Vue 应用正确挂载到 #app'
      })
    } else {
      results.functionality.push({
        name: 'Vue 应用挂载',
        status: 'error', 
        message: 'Vue 应用未正确挂载'
      })
    }
    
    // 检查路由是否工作
    const currentPath = window.location.pathname
    if (currentPath.includes('autodock')) {
      results.functionality.push({
        name: 'AutoDock 路由',
        status: 'success',
        message: 'AutoDock 路由正常工作'
      })
    } else {
      results.functionality.push({
        name: 'AutoDock 路由',
        status: 'warning',
        message: '当前不在 AutoDock 页面'
      })
    }
    
    // 检查响应式设计
    const isMobile = window.innerWidth <= 768
    const hasResponsiveStyles = document.querySelector('style')?.textContent?.includes('@media')
    
    results.functionality.push({
      name: '响应式设计',
      status: hasResponsiveStyles ? 'success' : 'warning',
      message: hasResponsiveStyles ? '检测到响应式样式' : '未检测到响应式样式'
    })
    
  } catch (error) {
    results.errors.push(`功能性检查失败: ${error.message}`)
  }
  
  // 生成报告
  console.log('\n📊 验证报告:')
  console.log('=' * 50)
  
  // Composables 报告
  console.log('\n📦 Composables:')
  results.composables.forEach(item => {
    const icon = item.status === 'available' ? '✅' : '❌'
    console.log(`  ${icon} ${item.name}: ${item.message}`)
  })
  
  // 组件报告
  console.log('\n🎯 DOM 组件:')
  results.components.forEach(item => {
    const icon = item.status === 'found' ? '✅' : '❌'
    console.log(`  ${icon} ${item.name}: ${item.message}`)
  })
  
  // 功能性报告
  console.log('\n⚙️ 功能性:')
  results.functionality.forEach(item => {
    const icon = item.status === 'success' ? '✅' : 
                 item.status === 'warning' ? '⚠️' : '❌'
    console.log(`  ${icon} ${item.name}: ${item.message}`)
  })
  
  // 错误报告
  if (results.errors.length > 0) {
    console.log('\n❌ 错误:')
    results.errors.forEach(error => {
      console.log(`  • ${error}`)
    })
  }
  
  // 总结
  const totalChecks = results.composables.length + results.components.length + results.functionality.length
  const successfulChecks = results.composables.filter(c => c.status === 'available').length +
                          results.components.filter(c => c.status === 'found').length +
                          results.functionality.filter(c => c.status === 'success').length
  
  const successRate = ((successfulChecks / totalChecks) * 100).toFixed(1)
  
  console.log('\n📈 总结:')
  console.log(`  成功率: ${successRate}% (${successfulChecks}/${totalChecks})`)
  console.log(`  错误数: ${results.errors.length}`)
  
  if (successRate >= 80) {
    console.log('\n🎉 AutoDock 重构验证通过！')
  } else if (successRate >= 60) {
    console.log('\n⚠️ AutoDock 重构基本完成，但有一些问题需要解决')
  } else {
    console.log('\n❌ AutoDock 重构存在重大问题，需要进一步修复')
  }
  
  return results
}

// 检查特定功能
export function checkSpecificFeature(featureName) {
  console.log(`🔍 检查特定功能: ${featureName}`)
  
  const featureChecks = {
    'molecule-viewer': () => {
      const viewer = document.querySelector('.molecule-viewer')
      const hasViewer = viewer !== null
      const hasCorrectSize = viewer && viewer.offsetHeight > 400
      return {
        exists: hasViewer,
        functional: hasCorrectSize,
        message: hasViewer ? 
          (hasCorrectSize ? '分子查看器正常' : '分子查看器尺寸异常') :
          '分子查看器不存在'
      }
    },
    
    'file-upload': () => {
      const uploadArea = document.querySelector('.upload-area')
      const fileInput = document.querySelector('input[type="file"]')
      return {
        exists: uploadArea !== null,
        functional: fileInput !== null,
        message: uploadArea && fileInput ? '文件上传功能正常' : '文件上传功能缺失'
      }
    },
    
    'form-validation': () => {
      const submitBtn = document.querySelector('#submit-btn')
      const inputs = document.querySelectorAll('input[type="number"], input[type="text"]')
      return {
        exists: submitBtn !== null,
        functional: inputs.length > 0,
        message: submitBtn && inputs.length > 0 ? '表单验证功能正常' : '表单验证功能缺失'
      }
    }
  }
  
  const check = featureChecks[featureName]
  if (check) {
    const result = check()
    console.log(`  结果: ${result.message}`)
    return result
  } else {
    console.log(`  未知功能: ${featureName}`)
    return { exists: false, functional: false, message: '未知功能' }
  }
}

// 在浏览器控制台中可用的全局函数
if (typeof window !== 'undefined') {
  window.validateAutoDockRefactoring = validateAutoDockRefactoring
  window.checkSpecificFeature = checkSpecificFeature
  
  console.log('💡 AutoDock 验证工具已加载')
  console.log('💡 在控制台中运行 validateAutoDockRefactoring() 来验证重构')
  console.log('💡 在控制台中运行 checkSpecificFeature("feature-name") 来检查特定功能')
}
