// 表单验证和提交 Composable
import { ref, computed } from 'vue'

export function useFormValidation() {
  // 状态管理
  const isSubmitting = ref(false)
  const validationErrors = ref([])
  const lastValidationResult = ref(null)
  
  // 验证对接参数
  const validateDockingParameters = (params) => {
    const errors = []
    
    // 验证口袋坐标
    if (!params.pocketX || isNaN(parseFloat(params.pocketX))) {
      errors.push('请输入有效的口袋 X 坐标')
    }
    
    if (!params.pocketY || isNaN(parseFloat(params.pocketY))) {
      errors.push('请输入有效的口袋 Y 坐标')
    }
    
    if (!params.pocketZ || isNaN(parseFloat(params.pocketZ))) {
      errors.push('请输入有效的口袋 Z 坐标')
    }
    
    // 验证对接盒子尺寸
    const sizeX = parseFloat(params.sizeX)
    const sizeY = parseFloat(params.sizeY)
    const sizeZ = parseFloat(params.sizeZ)
    
    if (!sizeX || sizeX <= 0 || sizeX > 100) {
      errors.push('对接盒子 X 尺寸必须在 1-100 Å 之间')
    }
    
    if (!sizeY || sizeY <= 0 || sizeY > 100) {
      errors.push('对接盒子 Y 尺寸必须在 1-100 Å 之间')
    }
    
    if (!sizeZ || sizeZ <= 0 || sizeZ > 100) {
      errors.push('对接盒子 Z 尺寸必须在 1-100 Å 之间')
    }
    
    // 验证线程数
    const threads = parseInt(params.threads)
    if (!threads || threads <= 0 || threads > 10000) {
      errors.push('线程数必须在 1-10000 之间')
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
  
  // 验证文件选择
  const validateFileSelection = (fileSelection) => {
    const errors = []
    
    // 验证蛋白质文件
    if (!fileSelection.protein) {
      errors.push('请选择一个蛋白质文件（PDB 或 PDBQT 格式）')
    } else {
      // 验证蛋白质文件格式
      const validProteinTypes = ['pdb', 'pdbqt']
      if (!validProteinTypes.includes(fileSelection.protein.type.toLowerCase())) {
        errors.push('蛋白质文件必须是 PDB 或 PDBQT 格式')
      }
    }
    
    // 验证配体文件
    if (!fileSelection.ligands || fileSelection.ligands.length === 0) {
      errors.push('请至少选择一个配体文件或输入 SMILES 分子')
    } else {
      // 验证配体文件格式
      const validLigandTypes = ['sdf', 'mol2', 'pdbqt', 'smiles']
      const invalidLigands = fileSelection.ligands.filter(ligand => 
        !validLigandTypes.includes(ligand.type.toLowerCase())
      )
      
      if (invalidLigands.length > 0) {
        errors.push('配体文件必须是 SDF、MOL2、PDBQT 或 SMILES 格式')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
  
  // 综合验证
  const validateForm = (formData) => {
    const allErrors = []
    
    // 验证对接参数
    const paramValidation = validateDockingParameters(formData.parameters)
    if (!paramValidation.isValid) {
      allErrors.push(...paramValidation.errors)
    }
    
    // 验证文件选择
    const fileValidation = validateFileSelection(formData.files)
    if (!fileValidation.isValid) {
      allErrors.push(...fileValidation.errors)
    }
    
    // 更新验证状态
    validationErrors.value = allErrors
    lastValidationResult.value = {
      isValid: allErrors.length === 0,
      errors: allErrors,
      timestamp: new Date().toISOString()
    }
    
    return lastValidationResult.value
  }
  
  // 准备提交数据
  const prepareSubmissionData = (formData) => {
    const submissionData = {
      // 对接参数
      pocket_center: {
        x: parseFloat(formData.parameters.pocketX),
        y: parseFloat(formData.parameters.pocketY),
        z: parseFloat(formData.parameters.pocketZ)
      },
      box_size: {
        x: parseFloat(formData.parameters.sizeX),
        y: parseFloat(formData.parameters.sizeY),
        z: parseFloat(formData.parameters.sizeZ)
      },
      threads: parseInt(formData.parameters.threads),
      
      // 文件数据
      receptor: {
        name: formData.files.protein.name,
        type: formData.files.protein.type,
        content: formData.files.protein.content
      },
      
      // 配体数据
      ligands: formData.files.ligands.map(ligand => ({
        name: ligand.name,
        type: ligand.type,
        content: ligand.type === 'smiles' ? ligand.smiles : ligand.content
      })),
      
      // 元数据
      mode: formData.files.mode,
      ligand_count: formData.files.ligands.length,
      timestamp: new Date().toISOString()
    }
    
    return submissionData
  }
  
  // 提交表单
  const submitForm = async (formData, apiEndpoint = '/api/autodock/submit') => {
    try {
      isSubmitting.value = true
      validationErrors.value = []
      
      // 验证表单
      const validation = validateForm(formData)
      if (!validation.isValid) {
        throw new Error('表单验证失败: ' + validation.errors.join(', '))
      }
      
      // 准备提交数据
      const submissionData = prepareSubmissionData(formData)
      
      // 发送请求
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData)
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      return {
        success: true,
        data: result,
        message: '对接任务提交成功'
      }
    } catch (error) {
      console.error('提交表单时出错:', error)
      
      return {
        success: false,
        error: error.message,
        message: '提交失败: ' + error.message
      }
    } finally {
      isSubmitting.value = false
    }
  }
  
  // 重置验证状态
  const resetValidation = () => {
    validationErrors.value = []
    lastValidationResult.value = null
  }
  
  // 获取验证摘要
  const getValidationSummary = () => {
    return {
      hasErrors: validationErrors.value.length > 0,
      errorCount: validationErrors.value.length,
      errors: validationErrors.value,
      lastValidated: lastValidationResult.value?.timestamp
    }
  }
  
  // 检查特定字段是否有错误
  const hasFieldError = (fieldName) => {
    return validationErrors.value.some(error => 
      error.toLowerCase().includes(fieldName.toLowerCase())
    )
  }
  
  // 获取特定字段的错误信息
  const getFieldErrors = (fieldName) => {
    return validationErrors.value.filter(error => 
      error.toLowerCase().includes(fieldName.toLowerCase())
    )
  }
  
  // 计算属性
  const isFormValid = computed(() => validationErrors.value.length === 0)
  const canSubmit = computed(() => isFormValid.value && !isSubmitting.value)
  
  return {
    // 状态
    isSubmitting,
    validationErrors,
    lastValidationResult,
    
    // 计算属性
    isFormValid,
    canSubmit,
    
    // 方法
    validateDockingParameters,
    validateFileSelection,
    validateForm,
    prepareSubmissionData,
    submitForm,
    resetValidation,
    getValidationSummary,
    hasFieldError,
    getFieldErrors
  }
}
