/**
 * Navigation Component JavaScript
 * 导航栏组件脚本
 * 
 * 功能: 用户认证、登录状态管理、导航交互
 * 依赖: jQuery, i18n.js
 * 作者: QureGenAI Team
 * 更新: 2025-08-16
 */

(function() {
    'use strict';

    // ===== 私有变量 =====
    let isInitialized = false;

    // ===== 通用API请求函数 =====
    function apiRequest(url, options = {}) {
        // 确保包含credentials
        options.credentials = 'include';
        
        return fetch(url, options)
            .then(response => {
                if (response.status === 401) {
                    // 认证失败，清除本地存储并跳转
                    clearLocalStorage();
                    window.location.href = 'login';
                    throw new Error('认证失败，请重新登录');
                }
                return response.json();
            })
            .then(data => {
                if (data.redirect === '/login') {
                    // 服务器要求重新登录
                    clearLocalStorage();
                    window.location.href = 'login';
                    throw new Error(data.message || '需要重新登录');
                }
                return data;
            });
    }

    // ===== 本地存储管理 =====
    function clearLocalStorage() {
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('username');
        localStorage.removeItem('userId');
        localStorage.removeItem('loginTime');
        localStorage.removeItem('lastAuthCheck');
    }

    // ===== 登录状态检查 =====
    function checkLocalLoginStatus() {
        const isLoggedIn = localStorage.getItem('isLoggedIn');
        
        // 如果本地没有登录状态，跳转到登录页
        if (isLoggedIn !== 'true') {
            window.location.href = 'login';
            return;
        }

        // 显示本地存储的用户信息
        displayUserInfoFromLocal();
    }

    // ===== 用户信息显示 =====
    function displayUserInfoFromLocal() {
        const username = localStorage.getItem('username') || '未知用户';
        const loginTime = localStorage.getItem('loginTime');
        
        const usernameEl = document.getElementById('username');
        const userAvatarEl = document.getElementById('userAvatar');
        
        if (usernameEl) {
            usernameEl.textContent = username;
        }
        
        if (userAvatarEl) {
            userAvatarEl.textContent = username.charAt(0).toUpperCase();
        }
        
        if (loginTime) {
            updateLoginTimeDisplay(loginTime);
        }
    }

    // ===== 更新登录时间显示 =====
    function updateLoginTimeDisplay(loginTime) {
        const loginTimeEl = document.getElementById('loginTime');
        if (!loginTimeEl) return;

        const loginDate = new Date(loginTime);
        const now = new Date();
        const diffMinutes = Math.floor((now - loginDate) / (1000 * 60));
        
        // 获取当前语言的翻译
        const currentLanguage = localStorage.getItem('language') || 'en';
        const t = window.translations && window.translations[currentLanguage] || {};
        
        let timeText;
        if (diffMinutes < 1) {
            timeText = t['just-logged-in'] || 'Just logged in';
        } else if (diffMinutes < 60) {
            timeText = currentLanguage === 'zh' 
                ? `${diffMinutes}${t['minutes-ago'] || '分钟前'}` 
                : `${diffMinutes} ${t['minutes-ago'] || 'minutes ago'}`;
        } else {
            const diffHours = Math.floor(diffMinutes / 60);
            if (diffHours < 24) {
                timeText = currentLanguage === 'zh' 
                    ? `${diffHours}${t['hours-ago'] || '小时前'}` 
                    : `${diffHours} ${t['hours-ago'] || 'hours ago'}`;
            } else {
                const diffDays = Math.floor(diffHours / 24);
                timeText = currentLanguage === 'zh' 
                    ? `${diffDays}${t['days-ago'] || '天前'}` 
                    : `${diffDays} ${t['days-ago'] || 'days ago'}`;
            }
        }
        
        loginTimeEl.textContent = timeText;
    }

    // ===== 退出登录 =====
    function logout() {
        // 直接使用fetch，避免apiRequest的认证检查逻辑
        fetch('/api/logout', { 
            method: 'POST',
            credentials: 'include'
        })
            .then(response => response.json())
            .then(data => {
                console.log('退出登录成功');
            })
            .catch(error => {
                console.error('退出登录时发生错误:', error);
            })
            .finally(() => {
                // 总是清除客户端存储并跳转
                clearLocalStorage();
                window.location.href = 'login';
            });
    }

    // ===== 导航功能初始化 =====
    function initNavigation() {
        // 给功能卡片添加点击跳转功能（如果存在）
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach(card => {
            card.style.cursor = 'pointer';
            card.addEventListener('click', function() {
                if (this.classList.contains('autodock')) {
                    window.location.href = 'autodock';
                } else if (this.classList.contains('diffdock')) {
                    window.location.href = 'diffdock';
                } else if (this.classList.contains('protenix')) {
                    window.location.href = 'protenix';
                } else if (this.classList.contains('quantum')) {
                    window.location.href = 'quantum_tasks';
                } else if (this.classList.contains('molmap')) {
                    window.location.href = 'molmap';
                } else if (this.classList.contains('pocketvina')) {
                    window.location.href = 'pocketvina';
                } else if (this.classList.contains('raman')) {
                    window.location.href = 'raman';
                }
            });
        });

        // 移动端侧边栏切换（如果需要）
        const sidebarToggle = document.querySelector('.js-sidebar-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
            });
        }
    }

    // ===== 事件处理 =====
    function bindEvents() {
        // 使用事件委托处理退出登录按钮
        document.addEventListener('click', function(e) {
            if (e.target.matches('.js-logout-btn') || e.target.matches('.logout-btn')) {
                e.preventDefault();
                logout();
            }
        });

        // 定期更新登录时间显示
        setInterval(function() {
            const loginTime = localStorage.getItem('loginTime');
            if (loginTime) {
                updateLoginTimeDisplay(loginTime);
            }
        }, 60000); // 每分钟更新一次
    }

    // ===== 初始化函数 =====
    function init() {
        if (isInitialized) return;
        
        checkLocalLoginStatus();
        initNavigation();
        bindEvents();
        
        isInitialized = true;
    }

    // ===== 页面加载完成后初始化 =====
    document.addEventListener('DOMContentLoaded', init);

    // ===== 全局暴露的API =====
    window.Navigation = {
        apiRequest: apiRequest,
        clearLocalStorage: clearLocalStorage,
        logout: logout,
        checkLocalLoginStatus: checkLocalLoginStatus,
        displayUserInfoFromLocal: displayUserInfoFromLocal,
        updateLoginTimeDisplay: updateLoginTimeDisplay
    };

})();
