{% extends "layout_with_nav.html" %}

{% block title %}DiffDock - QureGenAI 药物设计平台{% endblock %}



{% block inline_styles %}
<link rel="stylesheet" href="/src/styles/pages/diffdock.css">
{% endblock %}

{% block content %}
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%;">
        <div style="flex: 1;">
            <h1 class="page-title">
                <span class="page-icon"><img src="/src/images/diffdock-tetrahedral-methane.svg" alt="DiffDock"></span>
                <span data-i18n="page-title">DiffDock 口袋预测与分子对接</span>
                <a href="https://mp.weixin.qq.com/s/xNIv548O2GtGz2h3AgmKog" target="_blank" class="tutorial-btn">
                    <span>📚</span>
                    <span data-i18n="tutorial-btn">平台教程</span>
                </a>
            </h1>
            <p class="page-subtitle" data-i18n="page-subtitle">DiffDock是基于扩散模型的新一代分子对接方法，能够更准确地预测蛋白质-配体复合物的三维结构。它采用深度学习技术，通过扩散过程逐步优化配体的位置和取向，实现高精度的分子对接预测。</p>
        </div>
    </div>
</div>

<form id="diffdockForm">
    <!-- 蛋白质结构 -->
    <section class="section">
        <h2 class="section-title" data-i18n="protein-structure-title">
            🧬 蛋白质结构
        </h2>
        
        <div class="form-group">
            <label class="required" data-i18n="protein-input-method-label">蛋白质输入方式</label>
            <div class="protein-tabs">
                <button type="button" class="protein-tab active" data-tab="file" data-i18n="file-upload-tab">文件上传</button>
                <button type="button" class="protein-tab" data-tab="sequence" data-i18n="sequence-input-tab">序列输入</button>
            </div>
            
            <!-- 蛋白质输入说明 -->
            <div class="input-hint" style="margin: 1rem 0; padding: 0.75rem; background-color: #f8f9fa; border-left: 4px solid #17a2b8; border-radius: 0.25rem;">
                <div style="font-size: 0.875rem; color: #495057; text-align: left;" data-i18n="protein-input-explanation">
                    <strong>📋 输入方式说明：</strong><br>
                    • <strong>文件上传</strong>：上传蛋白质结构文件（PDB格式），对接结果预览中将显示蛋白质三维结构<br>
                    • <strong>序列输入</strong>：输入蛋白质氨基酸序列，系统将预测结构，对接结果预览中仅显示小分子结构
                </div>
            </div>
            
            <!-- 文件上传选项 -->
            <div id="proteinFileUpload" class="upload-area">
                <div class="upload-icon">📁</div>
                <p data-i18n="protein-file-upload-text">点击上传PDB文件或拖拽文件到此处</p>
                <p style="font-size: 0.875rem; color: #666; margin-top: 0.5rem;" data-i18n="protein-file-format-hint">
                    支持.pdb格式文件
                </p>
                <input type="file" class="file-input" accept=".pdb" id="proteinFile">
            </div>

            <!-- 序列输入选项 -->
            <div id="proteinSequenceInput" class="form-group" style="display: none; margin-bottom: 0;">
                <textarea name="protein_sequence" id="proteinSequenceTextarea" data-i18n-placeholder="protein-sequence-placeholder" placeholder="请输入蛋白质氨基酸序列，例如：MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL&#10;要求至少5个氨基酸，不超过1000个氨基酸"></textarea>
                <div class="input-hint" data-i18n="sequence-length-hint">
                    <span id="sequenceLength">0</span>/1000 个氨基酸 (最少5个)
                </div>
            </div>
        </div>
    </section>

    <!-- 小分子配体 -->
    <section class="section">
        <h2 class="section-title" data-i18n="ligand-section-title">
            🧪 小分子配体
        </h2>
        
        <div class="form-group">
            <label class="required" data-i18n="ligand-input-method-label">配体输入方式</label>
            <div class="ligand-tabs">
                <button type="button" class="ligand-tab active" data-tab="file" data-i18n="file-upload-tab">文件上传</button>
                <button type="button" class="ligand-tab" data-tab="smiles" data-i18n="smiles-input-tab">SMILES输入</button>
            </div>
            
            <!-- 文件上传选项 -->
            <div id="ligandFileUpload" class="upload-area">
                <div class="upload-icon">🧪</div>
                <p data-i18n="ligand-file-upload-text">点击上传配体文件或拖拽文件到此处</p>
                <p style="font-size: 0.875rem; color: #666; margin-top: 0.5rem;" data-i18n="ligand-file-format-hint">
                    支持.sdf、.mol2、.mol格式文件
                </p>
                <input type="file" class="file-input" accept=".sdf,.mol2,.mol" id="ligandFile">
        </div>

            <!-- SMILES输入选项 -->
            <div id="ligandSmilesInput" class="form-group" style="display: none; margin-bottom: 0;">
                <input type="text" name="smiles" id="smilesInput" data-i18n-placeholder="smiles-input-placeholder" placeholder="请输入单个SMILES字符串，例如：CCO（乙醇）" maxlength="500">
                <div class="input-hint" style="margin-top: 0.5rem; font-size: 0.75rem; color: #6b7280;" data-i18n="smiles-input-hint">
                    ℹ️ DiffDock只支持单个SMILES字符串输入，不支持多个分子
                </div>
            </div>
        </div>
    </section>

    <!-- 高级参数 -->
    <section class="section">
        <h2 class="section-title" data-i18n="advanced-params-title">
            ⚙️ 高级参数
        </h2>
        
        <div class="form-row">
            <div class="form-group">
                <label for="sampleCount" data-i18n="sample-count-label">采样数量 (num_samples)</label>
                <input type="number" id="sampleCount" name="sampleCount" value="10" min="1" max="100">
            </div>
        </div>
    </section>

    <button type="submit" class="submit-btn" data-i18n="submit-btn">开始DiffDock对接</button>
</form>

<!-- DiffDock任务历史区域 -->
<section class="section" id="diffdock-history-section" style="margin-top: 2rem;">
    <h2 class="section-title" data-i18n="task-history-title">
        📋 DiffDock任务历史
    </h2>
    
    
    <div class="tasks-container">
        <div class="tasks-header">
            <h3 data-i18n="my-diffdock-tasks">我的DiffDock任务</h3>
            <button class="refresh-btn" onclick="loadDiffDockTaskHistory()" data-i18n="refresh-btn">
                刷新
            </button>
            </div>
        
        <div id="diffdock-tasks-list" class="tasks-list">
            <!-- 任务列表将在这里动态加载 -->
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p data-i18n="loading-task-history">加载任务历史中...</p>
            </div>
        </div>

        <!-- 添加分页控件 -->
        <div id="diffdock-tasks-pagination" class="pagination-container" style="display: none;">
            <div class="pagination-info">
                <span id="diffdock-pagination-info-text"></span>
            </div>
            <div class="pagination-controls">
                <button id="diffdock-prev-page-btn" class="pagination-btn" onclick="loadDiffDockTaskHistoryPage(currentDiffDockPage - 1)">
                    <span data-i18n="prev-page">上一页</span>
                </button>
                <div id="diffdock-page-numbers" class="page-numbers">
                    <!-- 页码按钮将在这里动态生成 -->
                </div>
                <button id="diffdock-next-page-btn" class="pagination-btn" onclick="loadDiffDockTaskHistoryPage(currentDiffDockPage + 1)">
                    <span data-i18n="next-page">下一页</span>
                </button>
            </div>
        </div>

    </div>
</section>

<!-- 任务详情模态框 -->
<div id="taskDetailsModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 data-i18n="task-details-title">DiffDock任务详情</h3>
            <span class="close" onclick="closeTaskDetailsModal()">&times;</span>
        </div>
        <div class="modal-body" id="taskDetailsContent">
            <!-- 任务详情内容将在这里动态加载 -->
        </div>
    </div>
</div>
{% endblock %}

{% block page_js %}
{{ super() }}
<script src="/src/scripts/3dmol.js"></script>
<script>
        // 多语言文本配置
        const translations = {
            zh: {
                'page-title': 'DiffDock 口袋预测与分子对接',
                'page-subtitle': 'DiffDock是基于扩散模型的新一代分子对接方法，能够更准确地预测蛋白质-配体复合物的三维结构。它采用深度学习技术，通过扩散过程逐步优化配体的位置和取向，实现高精度的分子对接预测。',
                'tutorial-btn': '平台教程',
                'protein-structure-title': '🧬 蛋白质结构',
                'protein-input-method-label': '蛋白质输入方式',
                'file-upload-tab': '文件上传',
                'sequence-input-tab': '序列输入',
                'protein-input-explanation': '<strong>📋 输入方式说明：</strong><br>• <strong>文件上传</strong>：上传蛋白质结构文件（PDB格式），对接结果预览中将显示蛋白质三维结构<br>• <strong>序列输入</strong>：输入蛋白质氨基酸序列，系统将预测结构，对接结果预览中仅显示小分子结构',
                'protein-file-upload-text': '点击上传PDB文件或拖拽文件到此处',
                'protein-file-format-hint': '支持.pdb格式文件',
                'protein-sequence-placeholder': '请输入蛋白质氨基酸序列，例如：MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL&#10;要求至少5个氨基酸，不超过1000个氨基酸',
                'sequence-length-hint': '最多 1000 个氨基酸 (最少5个)',
                'ligand-section-title': '🧪 小分子配体',
                'ligand-input-method-label': '配体输入方式',
                'smiles-input-tab': 'SMILES输入',
                'ligand-file-upload-text': '点击上传配体文件或拖拽文件到此处',
                'ligand-file-format-hint': '支持.sdf、.mol2、.mol格式文件',
                'smiles-input-placeholder': '请输入单个SMILES字符串，例如：CCO（乙醇）',
                'smiles-input-hint': 'ℹ️ DiffDock只支持单个SMILES字符串输入，不支持多个分子',
                'advanced-params-title': '⚙️ 高级参数',
                'sample-count-label': '采样数量 (num_samples)',
                'submit-btn': '开始DiffDock对接',
                'task-history-title': '📋 DiffDock任务历史',
                'my-diffdock-tasks': '我的DiffDock任务',
                'refresh-btn': '刷新',
                'loading-task-history': '加载任务历史中...',
                'task-details-title': 'DiffDock任务详情',
                'pending': '等待中',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败',
                'task-id-label': '任务ID',
                'task-name-label': '任务名称',
                'created-time-label': '创建时间',
                'updated-time-label': '更新时间',
                'completed-time-label': '完成时间',
                'view-details-btn': '查看详情',
                'view-results-btn': '查看结果',
                'delete-task-btn': '删除',
                'basic-info-title': '基本信息',
                'params-info-title': '参数信息',
                'result-info-title': '结果信息',
                'error-info-title': '错误信息',
                'no-params': '无参数信息',
                'no-results': '暂无结果',
                'result-processing': '结果处理中...',
                'task-results-title': '任务结果',
                'files-count': '个文件',
                'no-result-files': '暂无结果文件',
                'download-file': '下载文件',
                'display-structure': '显示结构',
                'no-download-link': '无链接',
                'molecular-structure-title': '分子结构',
                'close-viewer': '关闭',
                'loading-structure': '正在加载分子结构...',
                'structure-load-success': '分子结构加载成功',
                'structure-load-failed': '加载分子结构失败',
                'load-failed': '加载失败',
                'collapse-results': '收起结果',
                'expand-results': '查看结果',
                'task-submit-success': 'DiffDock任务已成功提交！',
                'task-id': '任务ID',
                'status': '状态',
                'task-submit-failed': '提交失败',
                'task-submitting': '正在提交任务...',
                'network-error': '提交出错，请检查网络连接',
                'task-deleted-success': '任务删除成功',
                'task-delete-failed': '删除任务失败',
                'confirm-delete': '确定要删除这个任务吗？此操作不可撤销。',
                'no-diffdock-tasks': '暂无DiffDock任务',
                'unknown-error': '未知错误',
                'completed-at': '完成于',
                'get-task-results-error': '获取任务结果出错',
                'file-selected': '已选择文件',
                'protein-file-upload-default': '点击上传PDB文件或拖拽文件到此处',
                'ligand-file-upload-default': '点击上传配体文件或拖拽文件到此处',
                'unknown-user': '未知用户',
                'upload-protein-pdb': '请上传蛋白质PDB文件',
                'enter-protein-sequence': '请输入蛋白质序列',
                'protein-sequence-min-length': '蛋白质序列至少需要5个氨基酸',
                'protein-sequence-max-length': '蛋白质序列不能超过1000个氨基酸',
                'protein-sequence-invalid-chars': '蛋白质序列包含无效字符，请只输入标准氨基酸字母',
                'upload-ligand-file': '请上传配体文件',
                'enter-smiles-string': '请输入SMILES字符串',
                'smiles-single-line': 'DiffDock只支持单个SMILES字符串，请删除换行符',
                'smiles-single-molecule': 'DiffDock只支持单个SMILES字符串，请只输入一个分子的SMILES',
                'smiles-length-limit': 'SMILES字符串长度应在1-500个字符之间',
                'task-submit-success-short': 'DiffDock任务已成功提交！',
                'submit-failed-prefix': '提交失败: ',
                'network-connection-error': '提交出错，请检查网络连接',
                'get-task-details-failed': '获取任务详情失败: ',
                'network-error-check-connection': '网络错误，请检查连接',
                'molecule-structure-loaded': '分子结构已加载',
                'no-diffdock-task-history': '暂无DiffDock任务历史',
                'submit-first-task': '提交第一个DiffDock任务开始使用',
                'task-name-field': '任务名称',
                'unnamed': '未命名',
                'created-time-field': '创建时间',
                'completed-time-field': '完成时间',
                'error-message-field': '错误信息',
                'view-details': '查看详情',
                'view-results': '查看结果',
                'loading-results': '加载结果中...',
                'basic-info-field': '基本信息',
                'task-id-field': '任务ID',
                'job-id-field': 'Job ID',
                'status-field': '状态',
                'updated-time-field': '更新时间',
                'task-params-field': '任务参数',
                'file-content-omitted': '[文件内容已省略]',
                'error-info-field': '错误信息',
                'task-results-files': '任务结果',
                'completed-at-time': '完成于',
                'no-result-files-available': '暂无结果文件',
                'task-may-be-processing': '任务可能仍在处理中或处理失败',
                'get-results-failed': '获取结果失败',
                'network-error-results': '网络错误，请检查连接',
                'download-file-title': '下载文件',
                'no-download-link-available': '无下载链接',
                'display-molecule-structure': '展示分子结构',
                'get-task-results-error-console': '获取任务结果失败',
                'unknown-time': '未知',
                'just-now': '刚刚',
                'files-count': '个文件',
                'unknown-error': '未知错误',
                'status-pending': '等待中',
                'status-running': '运行中',
                'status-completed': '已完成',
                'status-failed': '失败',
                'molecule-structure': '分子结构',
                'protein-structure': '蛋白质',
                'close-btn': '关闭',
                'loading-molecule-structure': '加载分子结构中...',
                'loading-failed': '加载失败',
                'failed-to-load-molecule-structure': '加载分子结构失败',
                'page-title-html': 'DiffDock - 分子设计平台',
                'prev-page': '上一页',
                'next-page': '下一页',
                'load-failed': '加载失败',
                'download-all-files': '下载全部',
                'prepare-download': '准备下载',
                'getting-file-list': '正在获取文件列表...',
                'no-files-to-download': '该任务暂无可下载的结果文件',
                'download-confirm': '确定要下载任务的所有结果吗？',
                'downloading-files': '正在下载文件...',
                'download-complete': '下载完成!',
                'download-failed': '获取任务结果失败，请稍后重试',

            },
            en: {
                'page-title': 'DiffDock Pocket Prediction and Molecular Docking',
                'page-subtitle': 'DiffDock is a new generation molecular docking method based on diffusion models, capable of more accurately predicting three-dimensional structures of protein-ligand complexes. It uses deep learning technology to gradually optimize ligand position and orientation through diffusion processes, achieving high-precision molecular docking predictions.',
                'tutorial-btn': 'Platform Tutorial',
                'protein-structure-title': '🧬 Protein Structure',
                'protein-input-method-label': 'Protein Input Method',
                'file-upload-tab': 'File Upload',
                'sequence-input-tab': 'Sequence Input',
                'protein-input-explanation': '<strong>📋 Input Method Description:</strong><br>• <strong>File Upload</strong>: Upload protein structure file (PDB format), protein 3D structure will be displayed in docking result preview<br>• <strong>Sequence Input</strong>: Input protein amino acid sequence, system will predict structure, only small molecule structure will be displayed in docking result preview',
                'protein-file-upload-text': 'Click to upload PDB file or drag file here',
                'protein-file-format-hint': 'Supports .pdb format files',
                'protein-sequence-placeholder': 'Enter protein amino acid sequence, for example: MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL&#10;Requires at least 5 amino acids, no more than 1000 amino acids',
                'sequence-length-hint': 'At most 1000 amino acids (minimum 5)',
                'ligand-section-title': '🧪 Small Molecule Ligand',
                'ligand-input-method-label': 'Ligand Input Method',
                'smiles-input-tab': 'SMILES Input',
                'ligand-file-upload-text': 'Click to upload ligand file or drag file here',
                'ligand-file-format-hint': 'Supports .sdf, .mol2, .mol format files',
                'smiles-input-placeholder': 'Enter a single SMILES string, for example: CCO (ethanol)',
                'smiles-input-hint': 'ℹ️ DiffDock only supports single SMILES string input, does not support multiple molecules',
                'advanced-params-title': '⚙️ Advanced Parameters',
                'sample-count-label': 'Sample Count (num_samples)',
                'submit-btn': 'Start DiffDock Docking',
                'task-history-title': '📋 DiffDock Task History',
                'my-diffdock-tasks': 'My DiffDock Tasks',
                'refresh-btn': 'Refresh',
                'loading-task-history': 'Loading task history...',
                'task-details-title': 'DiffDock Task Details',
                'pending': 'Pending',
                'running': 'Running',
                'completed': 'Completed',
                'failed': 'Failed',
                'task-id-label': 'Task ID',
                'task-name-label': 'Task Name',
                'created-time-label': 'Created Time',
                'updated-time-label': 'Updated Time',
                'completed-time-label': 'Completed Time',
                'view-details-btn': 'View Details',
                'view-results-btn': 'View Results',
                'delete-task-btn': 'Delete',
                'basic-info-title': 'Basic Information',
                'params-info-title': 'Parameter Information',
                'result-info-title': 'Result Information',
                'error-info-title': 'Error Information',
                'no-params': 'No parameter information',
                'no-results': 'No results available',
                'result-processing': 'Processing results...',
                'task-results-title': 'Task Results',
                'files-count': 'files',
                'no-result-files': 'No result files',
                'download-file': 'Download',
                'display-structure': 'Display Structure',
                'no-download-link': 'No link',
                'molecular-structure-title': 'Molecular Structure',
                'close-viewer': 'Close',
                'loading-structure': 'Loading molecular structure...',
                'structure-load-success': 'Molecular structure loaded successfully',
                'structure-load-failed': 'Failed to load molecular structure',
                'load-failed': 'Load Failed',
                'collapse-results': 'Collapse Results',
                'expand-results': 'View Results',
                'task-submit-success': 'DiffDock task submitted successfully!',
                'task-id': 'Task ID',
                'status': 'Status',
                'task-submit-failed': 'Submission failed',
                'task-submitting': 'Submitting task...',
                'network-error': 'Submission error, please check network connection',
                'task-deleted-success': 'Task deleted successfully',
                'task-delete-failed': 'Failed to delete task',
                'confirm-delete': 'Are you sure you want to delete this task? This operation cannot be undone.',
                'no-diffdock-tasks': 'No DiffDock tasks',
                'unknown-error': 'Unknown error',
                'completed-at': 'Completed at',
                'get-task-results-error': 'Error getting task results',
                'file-selected': 'File selected',
                'protein-file-upload-default': 'Click to upload PDB file or drag file here',
                'ligand-file-upload-default': 'Click to upload ligand file or drag file here',
                'unknown-user': 'Unknown User',
                'upload-protein-pdb': 'Please upload protein PDB file',
                'enter-protein-sequence': 'Please enter protein sequence',
                'protein-sequence-min-length': 'Protein sequence requires at least 5 amino acids',
                'protein-sequence-max-length': 'Protein sequence cannot exceed 1000 amino acids',
                'protein-sequence-invalid-chars': 'Protein sequence contains invalid characters, please only enter standard amino acid letters',
                'upload-ligand-file': 'Please upload ligand file',
                'enter-smiles-string': 'Please enter SMILES string',
                'smiles-single-line': 'DiffDock only supports single SMILES string, please remove line breaks',
                'smiles-single-molecule': 'DiffDock only supports single SMILES string, please enter only one molecule SMILES',
                'smiles-length-limit': 'SMILES string length should be between 1-500 characters',
                'task-submit-success-short': 'DiffDock task submitted successfully!',
                'submit-failed-prefix': 'Submission failed: ',
                'network-connection-error': 'Submission error, please check network connection',
                'get-task-details-failed': 'Failed to get task details: ',
                'network-error-check-connection': 'Network error, please check connection',
                'molecule-structure-loaded': 'Molecule structure loaded',
                'no-diffdock-task-history': 'No DiffDock task history',
                'submit-first-task': 'Submit your first DiffDock task to get started',
                'task-name-field': 'Task Name',
                'unnamed': 'Unnamed',
                'created-time-field': 'Created Time',
                'completed-time-field': 'Completed Time',
                'error-message-field': 'Error Message',
                'view-details': 'View Details',
                'view-results': 'View Results',
                'loading-results': 'Loading results...',
                'basic-info-field': 'Basic Information',
                'task-id-field': 'Task ID',
                'job-id-field': 'Job ID',
                'status-field': 'Status',
                'updated-time-field': 'Updated Time',
                'task-params-field': 'Task Parameters',
                'file-content-omitted': '[File content omitted]',
                'error-info-field': 'Error Information',
                'task-results-files': 'Task Results',
                'completed-at-time': 'Completed at',
                'no-result-files-available': 'No result files available',
                'task-may-be-processing': 'Task may still be processing or failed',
                'get-results-failed': 'Failed to get results',
                'network-error-results': 'Network error, please check connection',
                'download-file-title': 'Download file',
                'no-download-link-available': 'No download link',
                'display-molecule-structure': 'Display molecule structure',
                'get-task-results-error-console': 'Failed to get task results',
                'unknown-time': 'Unknown',
                'just-now': 'Just now',
                'files-count': 'files',
                'unknown-error': 'Unknown error',
                'status-pending': 'Pending',
                'status-running': 'Running',
                'status-completed': 'Completed',
                'status-failed': 'Failed',
                'molecule-structure': 'Molecule Structure',
                'protein-structure': 'Protein',
                'close-btn': 'Close',
                'loading-molecule-structure': 'Loading molecular structure...',
                'loading-failed': 'Loading Failed',
                'failed-to-load-molecule-structure': 'Failed to load molecular structure',
                'page-title-html': 'DiffDock - Molecular Design Platform',
                'prev-page': 'Previous',
                'next-page': 'Next',
                'load-failed': 'Load Failed',
                'download-all-files': 'Download All',
                'prepare-download': 'Prepare Download',
                'getting-file-list': 'Getting file list...',
                'no-files-to-download': 'No downloadable result files for this task',
                'download-confirm': 'Are you sure you want to download all results for this task?',
                'downloading-files': 'Downloading files...',
                'download-complete': 'Download complete!',
                'download-failed': 'Failed to get task results, please try again later',
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            
            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.lang === lang) {
                    btn.classList.add('active');
                }
            });
            
            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            // 更新文档标题
            document.title = lang === 'zh' ? 'DiffDock - 分子设计平台' : 'DiffDock - Molecular Design Platform';
            
            // 更新页面文本
            updatePageText(lang);

            // 更新登录时间显示
            const loginTime = localStorage.getItem('loginTime');

            // 重新加载任务历史以应用新语言
            loadDiffDockTaskHistory();
            
            console.log('Language switched to:', lang);
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];
            
            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t && t[key]) {
                    // 特殊处理title标签
                    if (element.tagName === 'TITLE') {
                        element.textContent = t[key];
                    }
                    // 处理包含HTML结构的文本
                    else if (t[key].includes('<strong>') || t[key].includes('<br>') || t[key].includes('<span>')) {
                        element.innerHTML = t[key];
                    } else {
                        element.textContent = t[key];
                    }
                }
            });

            // 更新placeholder属性
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (t && t[key]) {
                    element.placeholder = t[key];
                }
            });
        }

        // 检查登录状态
        function checkLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn !== 'true') {
                window.location.href = 'login';
                return;
            }

            // 显示用户信息
            const username = localStorage.getItem('username');
            const loginTime = localStorage.getItem('loginTime');
            
            const t = translations[currentLanguage];
            document.getElementById('username').textContent = username || t['unknown-user'];
            document.getElementById('userAvatar').textContent = (username || 'U').substring(0, 3).toUpperCase();
        }

        // 退出登录
        function logout() {
            fetch('/api/logout', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Server session ended');
                    } else {
                        console.error('Server logout failed:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error during server logout:', error);
                })
                .finally(() => {
                    // Always clear client-side storage and redirect
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login';
                });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 先初始化语言切换功能
            checkLoginStatus();
            switchLanguage(currentLanguage);
            initializeForm();
            loadDiffDockTaskHistory(); // 加载任务历史

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });
        });

        // 初始化表单功能
        function initializeForm() {
            // 蛋白质选项卡切换
            const proteinTabs = document.querySelectorAll('.protein-tab');
            const proteinFileUpload = document.getElementById('proteinFileUpload');
            const proteinSequenceInput = document.getElementById('proteinSequenceInput');
            const proteinSequenceTextarea = document.getElementById('proteinSequenceTextarea');

            proteinTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有活动状态
                    proteinTabs.forEach(t => t.classList.remove('active'));
                    
                    // 激活当前选项卡
                    this.classList.add('active');
                    
                    const tabType = this.getAttribute('data-tab');
                    if (tabType === 'file') {
                        proteinFileUpload.style.display = 'block';
                        proteinSequenceInput.style.display = 'none';
                        // 清空序列输入
                        proteinSequenceTextarea.value = '';
                        updateSequenceLength();
                    } else if (tabType === 'sequence') {
                        proteinFileUpload.style.display = 'none';
                        proteinSequenceInput.style.display = 'block';
                        // 清空文件选择
                        document.getElementById('proteinFile').value = '';
                        const t = translations[currentLanguage];
                        updateUploadAreaText('proteinFileUpload', t['protein-file-upload-default']);
                    }
                });
            });

            // 蛋白质序列长度监控
            proteinSequenceTextarea.addEventListener('input', function() {
                updateSequenceLength();
                validateProteinSequence();
            });

            // 配体选项卡切换
            const ligandTabs = document.querySelectorAll('.ligand-tab');
            const ligandFileUpload = document.getElementById('ligandFileUpload');
            const ligandSmilesInput = document.getElementById('ligandSmilesInput');

            ligandTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有活动状态
                    ligandTabs.forEach(t => t.classList.remove('active'));
                    
                    // 激活当前选项卡
                    this.classList.add('active');
                    
                    const tabType = this.getAttribute('data-tab');
                    if (tabType === 'file') {
                        ligandFileUpload.style.display = 'block';
                        ligandSmilesInput.style.display = 'none';
                        // 清空SMILES输入
                        document.getElementById('smilesInput').value = '';
                    } else if (tabType === 'smiles') {
                        ligandFileUpload.style.display = 'none';
                        ligandSmilesInput.style.display = 'block';
                        // 清空文件选择
                        document.getElementById('ligandFile').value = '';
                        const t = translations[currentLanguage];
                        updateUploadAreaText('ligandFileUpload', t['ligand-file-upload-default']);
                    }
                });
            });

            // 文件上传区域点击处理
            setupFileUpload('proteinFileUpload', 'proteinFile');
            setupFileUpload('ligandFileUpload', 'ligandFile');

            // 表单提交处理
            document.getElementById('diffdockForm').addEventListener('submit', handleFormSubmit);
        }

        // 更新序列长度显示
        function updateSequenceLength() {
            const textarea = document.getElementById('proteinSequenceTextarea');
            const lengthSpan = document.getElementById('sequenceLength');
            if (textarea && lengthSpan) { // 添加空值检查
                const sequence = textarea.value.replace(/\s/g, ''); // 移除空白字符
                lengthSpan.textContent = sequence.length;
            }
        }

        // 验证蛋白质序列
        function validateProteinSequence() {
            const textarea = document.getElementById('proteinSequenceTextarea');
            const sequence = textarea.value.replace(/\s/g, '').toUpperCase();
            
            // 检查是否只包含有效的氨基酸字符
            const validAminoAcids = /^[ACDEFGHIKLMNPQRSTVWY]*$/;
            
            if (sequence.length > 0 && !validAminoAcids.test(sequence)) {
                textarea.style.borderColor = '#ef4444';
                return false;
            } else if (sequence.length > 2000) {
                textarea.style.borderColor = '#ef4444';
                return false;
            } else if (sequence.length > 0 && sequence.length < 5) {
                textarea.style.borderColor = '#f59e0b';
                return false;
            } else {
                textarea.style.borderColor = '#e1e5e9';
                return true;
            }
        }

        // 设置文件上传功能
        function setupFileUpload(uploadAreaId, fileInputId) {
            const uploadArea = document.getElementById(uploadAreaId);
            const fileInput = document.getElementById(fileInputId);

            // 点击上传区域触发文件选择
            uploadArea.addEventListener('click', function() {
                fileInput.click();
            });

            // 文件选择处理
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const fileName = this.files[0].name;
                    const t = translations[currentLanguage];
                    updateUploadAreaText(uploadAreaId, `${t['file-selected']}: ${fileName}`);
                }
            });

            // 拖拽功能
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    const fileName = files[0].name;
                    const t = translations[currentLanguage];
                    updateUploadAreaText(uploadAreaId, `${t['file-selected']}: ${fileName}`);
                }
            });
        }

        // 更新上传区域文本
        function updateUploadAreaText(uploadAreaId, text) {
            const uploadArea = document.getElementById(uploadAreaId);
            const p = uploadArea.querySelector('p');
            if (p) {
                p.textContent = text;
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                max-width: 400px;
                word-wrap: break-word;
                transition: all 0.3s ease;
            `;
            
            // 根据类型设置颜色
            switch (type) {
                case 'success':
                    notification.style.background = '#10b981';
                    break;
                case 'error':
                    notification.style.background = '#ef4444';
                    break;
                case 'warning':
                    notification.style.background = '#f59e0b';
                    break;
                default:
                    notification.style.background = '#3b82f6';
            }
            
            // 添加到页面
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 表单提交处理
        function handleFormSubmit(e) {
            e.preventDefault();
            
            // 验证表单
            const proteinFile = document.getElementById('proteinFile').files[0];
            const proteinSequence = document.getElementById('proteinSequenceTextarea').value.trim();
            const ligandFile = document.getElementById('ligandFile').files[0];
            const smilesInput = document.getElementById('smilesInput').value.trim();
            
            // 检查蛋白质输入
            const t = translations[currentLanguage];
            const activeProteinTab = document.querySelector('.protein-tab.active').getAttribute('data-tab');
            if (activeProteinTab === 'file' && !proteinFile) {
                showNotification(t['upload-protein-pdb'], 'error');
                return;
            } else if (activeProteinTab === 'sequence') {
                if (!proteinSequence) {
                    showNotification(t['enter-protein-sequence'], 'error');
                    return;
                }
                
                // 验证序列长度
                const cleanSequence = proteinSequence.replace(/\s/g, '');
                if (cleanSequence.length < 5) {
                    showNotification(t['protein-sequence-min-length'], 'error');
                    return;
                } else if (cleanSequence.length > 1000) {
                    showNotification(t['protein-sequence-max-length'], 'error');
                    return;
                }
                
                // 验证序列格式
                const validAminoAcids = /^[ACDEFGHIKLMNPQRSTVWY\s]*$/i;
                if (!validAminoAcids.test(proteinSequence)) {
                    showNotification(t['protein-sequence-invalid-chars'], 'error');
                    return;
                }
            }
            
            // 检查配体输入
            const activeLigandTab = document.querySelector('.ligand-tab.active').getAttribute('data-tab');
            if (activeLigandTab === 'file' && !ligandFile) {
                showNotification(t['upload-ligand-file'], 'error');
                return;
            } else if (activeLigandTab === 'smiles' && !smilesInput) {
                showNotification(t['enter-smiles-string'], 'error');
                return;
            } else if (activeLigandTab === 'smiles' && smilesInput) {
                // 验证SMILES格式 - 确保只有单个SMILES
                if (smilesInput.includes('\n') || smilesInput.includes('\r')) {
                    showNotification(t['smiles-single-line'], 'error');
                    return;
                }
                
                // 检查是否包含多个SMILES（简单检查是否有多个明显分离的分子）
                const parts = smilesInput.split(/\s+/).filter(part => part.length > 0);
                if (parts.length > 1) {
                    showNotification(t['smiles-single-molecule'], 'error');
                    return;
                }
                
                // 基本SMILES格式验证
                if (smilesInput.length < 1 || smilesInput.length > 500) {
                    showNotification(t['smiles-length-limit'], 'error');
                    return;
                }
            }
            
            // 准备提交数据
            const formData = new FormData();
            
            // 自动生成任务名称
            const now = new Date();
            const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const jobName = `diffdock_${timestamp}`;
            formData.append('job_name', jobName);
            
            // 根据蛋白质输入方式添加相应字段
            if (activeProteinTab === 'file') {
                // 蛋白质文件上传模式
                formData.append('protein_file', proteinFile);
            } else {
                // 蛋白质序列输入模式
                formData.append('protein_sequence', proteinSequence.replace(/\s/g, '').toUpperCase());
            }
            
            // 根据配体输入方式添加相应字段
            if (activeLigandTab === 'file') {
                // 文件上传模式
                formData.append('ligand_file', ligandFile);
            } else {
                // SMILES输入模式
                formData.append('smiles', smilesInput);
            }
            
            // 添加参数
            const sampleCount = document.getElementById('sampleCount').value || '10';
            
            formData.append('sample_count', sampleCount);
            
            // 显示提交中状态
            const submitBtn = document.querySelector('.submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = t['task-submitting'];
            submitBtn.disabled = true;
            
            // 提交到/api/diffdock接口
            fetch('/api/diffdock', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('提交响应:', data);
                
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                if (data.success) {
                    showNotification(t['task-submit-success-short'], 'success');
                    
                    // 清空表单
                    document.getElementById('diffdockForm').reset();
                    updateUploadAreaText('proteinFileUpload', t['protein-file-upload-default']);
                    updateUploadAreaText('ligandFileUpload', t['ligand-file-upload-default']);
                    const proteinTextarea = document.getElementById('proteinSequenceTextarea');
                    if (proteinTextarea) {
                        proteinTextarea.value = '';
                    }
                    updateSequenceLength();
                    
                    // 刷新任务历史
                    loadDiffDockTaskHistory();
                    
                } else {
                    showNotification(t['submit-failed-prefix'] + (data.message || t['unknown-error']), 'error');
                }
            })
            .catch(error => {
                console.error('Submit error:', error);
                
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                showNotification(t['network-connection-error'], 'error');
            });
        }

        // ========== DiffDock任务历史功能 ==========


        // DiffDock分页相关变量
        let currentDiffDockPage = 1;
        let totalDiffDockPages = 1;
        let totalDiffDockItems = 0;
        const diffDockPageSize = 5; // 每页显示5条任务

        // 加载DiffDock任务历史
        function loadDiffDockTaskHistory() {
            loadDiffDockTaskHistoryPage(1); // 重置到第一页
        }


        // 加载指定页码的DiffDock任务历史
        async function loadDiffDockTaskHistoryPage(page = 1) {
            console.log(`=== loadDiffDockTaskHistoryPage called with page: ${page} ===`);
            
            // 验证页码
            if (page < 1) {
                page = 1;
            }
            if (page > totalDiffDockPages && totalDiffDockPages > 0) {
                page = totalDiffDockPages;
            }
            
            currentDiffDockPage = page;
            
            const tasksList = document.getElementById('diffdock-tasks-list');
            const t = translations[currentLanguage];
            
            // 显示加载状态
            tasksList.innerHTML = `
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>${t['loading-task-history']}</p>
                </div>
            `;
            
            try {
                const url = `/api/tasks?task_type=diffdock&page=${page}&page_size=${diffDockPageSize}&include_content=false`;
                console.log('Fetching DiffDock tasks from:', url);
                
                const response = await fetch(url);
                console.log('Response status:', response.status);
                const result = await response.json();
                
                if (result.success && result.tasks) {
                    // 更新分页信息
                    if (result.pagination) {
                        currentDiffDockPage = result.pagination.current_page;
                        totalDiffDockPages = result.pagination.total_pages;
                        totalDiffDockItems = result.pagination.total_items;
                        console.log(`DiffDock任务总数: ${totalDiffDockItems}, 当前页: ${currentDiffDockPage}/${totalDiffDockPages}`);
                    } else {
                        // 兼容没有分页信息的情况
                        totalDiffDockPages = 1;
                        totalDiffDockItems = result.tasks ? result.tasks.length : 0;
                    }
                    
                    displayDiffDockTaskHistory(result.tasks);
                    updateDiffDockPaginationControls();
                    
                } else {
                    tasksList.innerHTML = `
                        <div class="loading-placeholder">
                            <p style="color: #ef4444;">❌ ${t['load-failed']}: ${result.message || t['unknown-error']}</p>
                        </div>
                    `;
                    // 隐藏分页控件
                    const paginationContainer = document.getElementById('diffdock-tasks-pagination');
                    if (paginationContainer) {
                        paginationContainer.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Failed to load DiffDock task history:', error);
                tasksList.innerHTML = `
                    <div class="loading-placeholder">
                        <p style="color: #ef4444;">❌ ${t['network-error-check-connection']}</p>
                    </div>
                `;
                // 隐藏分页控件
                const paginationContainer = document.getElementById('diffdock-tasks-pagination');
                if (paginationContainer) {
                    paginationContainer.style.display = 'none';
                }
            }
        }


        // 更新DiffDock分页控件
        function updateDiffDockPaginationControls() {
            const paginationContainer = document.getElementById('diffdock-tasks-pagination');
            const paginationInfoText = document.getElementById('diffdock-pagination-info-text');
            const prevBtn = document.getElementById('diffdock-prev-page-btn');
            const nextBtn = document.getElementById('diffdock-next-page-btn');
            const pageNumbers = document.getElementById('diffdock-page-numbers');

            if (!paginationContainer) return;

            // 只有在有多页或有任务时显示分页控件
            if (totalDiffDockPages > 1 || totalDiffDockItems > 0) {
                paginationContainer.style.display = 'flex';
            } else {
                paginationContainer.style.display = 'none';
                return;
            }

            // 更新分页信息文本
            const startItem = (currentDiffDockPage - 1) * diffDockPageSize + 1;
            const endItem = Math.min(currentDiffDockPage * diffDockPageSize, totalDiffDockItems);
            const t = translations[currentLanguage];

            if (paginationInfoText) {
                if (currentLanguage === 'zh') {
                    paginationInfoText.textContent = `显示 ${startItem}-${endItem} 条，共 ${totalDiffDockItems} 条任务`;
                } else {
                    paginationInfoText.textContent = `Showing ${startItem}-${endItem} of ${totalDiffDockItems} tasks`;
                }
            }

            // 更新上一页按钮状态
            if (prevBtn) {
                prevBtn.disabled = currentDiffDockPage <= 1;
            }

            // 更新下一页按钮状态
            if (nextBtn) {
                nextBtn.disabled = currentDiffDockPage >= totalDiffDockPages;
            }

            // 生成页码按钮
            if (pageNumbers) {
                pageNumbers.innerHTML = generateDiffDockPageNumbers();
            }
        }


        // 生成DiffDock页码按钮HTML
        function generateDiffDockPageNumbers() {
            const maxVisiblePages = 7; // 最多显示7个页码按钮
            let startPage = 1;
            let endPage = totalDiffDockPages;

            if (totalDiffDockPages > maxVisiblePages) {
                const halfVisible = Math.floor(maxVisiblePages / 2);
                startPage = Math.max(1, currentDiffDockPage - halfVisible);
                endPage = Math.min(totalDiffDockPages, startPage + maxVisiblePages - 1);

                // 调整开始页码，确保显示完整的页码范围
                if (endPage - startPage + 1 < maxVisiblePages) {
                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }
            }

            let html = '';

            // 第一页和省略号
            if (startPage > 1) {
                html += `<button class="page-number" onclick="loadDiffDockTaskHistoryPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="page-number ellipsis">...</span>`;
                }
            }

            // 页码范围
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentDiffDockPage ? 'active' : '';
                html += `<button class="page-number ${activeClass}" onclick="loadDiffDockTaskHistoryPage(${i})">${i}</button>`;
            }

            // 省略号和最后一页
            if (endPage < totalDiffDockPages) {
                if (endPage < totalDiffDockPages - 1) {
                    html += `<span class="page-number ellipsis">...</span>`;
                }
                html += `<button class="page-number" onclick="loadDiffDockTaskHistoryPage(${totalDiffDockPages})">${totalDiffDockPages}</button>`;
            }

            return html;
        }


        // 显示DiffDock任务历史
        function displayDiffDockTaskHistory(tasks) {
            const tasksList = document.getElementById('diffdock-tasks-list');
            const t = translations[currentLanguage];
            
            if (!tasks || tasks.length === 0) {
                tasksList.innerHTML = `
                    <div class="loading-placeholder">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">📋</div>
                        <p>${t['no-diffdock-task-history']}</p>
                        <p style="font-size: 0.875rem; color: #9ca3af; margin-top: 0.5rem;">
                            ${t['submit-first-task']}
                        </p>
                    </div>
                `;
                return;
            }
            
            // 按创建时间倒序排列
            // tasks.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            
            const tasksHtml = tasks.map(task => {
                const statusText = getStatusText(task.status);
                const statusClass = task.status;
                
                return `
                    <div class="task-item" data-task-id="${task.task_id}">
                        <div class="task-header">
                            <div class="task-id">ID: ${task.task_id}</div>
                            <div class="task-status ${statusClass}">${statusText}</div>
                        </div>
                        
                        <div class="task-info">
                            <div><strong>${t['task-name-field']}:</strong> ${task.job_name || t['unnamed']}</div>
                            <div><strong>${t['created-time-field']}:</strong> ${formatDateTime(task.created_at)}</div>
                            ${task.completed_at ? `<div><strong>${t['completed-time-field']}:</strong> ${formatDateTime(task.completed_at)}</div>` : ''}
                            ${task.error_message ? `<div style="color: #ef4444;"><strong>${t['error-message-field']}:</strong> ${task.error_message}</div>` : ''}
                        </div>
                        
                        <div class="task-actions">
                            <button class="task-btn view" onclick="viewDiffDockTaskDetails('${task.task_id}')">
                                📋 ${t['view-details']}
                            </button>
                            ${task.status === 'completed' ? `
                                <button class="task-btn view" onclick="showDiffDockTaskResults('${task.task_id}')">
                                    📁 ${t['view-results']}
                                </button>
                                <button class="task-btn download-all" onclick="downloadDiffDockTaskAllResults('${task.task_id}')" style="background: #10b981; border-color: #10b981;" onmouseover="this.style.background='#059669'" onmouseout="this.style.background='#10b981'">
                                    📦 ${t['download-all-files'] || '下载全部'}
                                </button>

                            ` : ''}
                        </div>
                        
                        <!-- 结果展示区域 -->
                        <div id="results-${task.task_id}" class="task-results-container" style="display: none;"></div>
                    </div>
                `;
            }).join('');
            
            tasksList.innerHTML = tasksHtml;
        }

        // 查看DiffDock任务详情
        function viewDiffDockTaskDetails(taskId) {
            fetch(`/api/tasks/${taskId}?include_content=false`)
                .then(response => response.json())
                .then(data => {
                    if (data.task) {
                        showDiffDockTaskDetailsModal(data.task); // 移除不必要的调用，避免额外的网络请求
                                    } else {
                    const t = translations[currentLanguage];
                    showNotification(t['get-task-details-failed'] + (data.message || t['unknown-error']), 'error');
                }
            })
                            .catch(error => {
                    console.error('Failed to get task details:', error);
                const t = translations[currentLanguage];
                showNotification(t['network-error-check-connection'], 'error');
            });
        }

        // 显示DiffDock任务详情模态框
        function showDiffDockTaskDetailsModal(task) {
            const modal = document.getElementById('taskDetailsModal');
            const content = document.getElementById('taskDetailsContent');
            const t = translations[currentLanguage];
            
            // 安全地解析parameters
            let parametersData = null;
            try {
                if (task.parameters) {
                    if (typeof task.parameters === 'string') {
                        parametersData = JSON.parse(task.parameters);
                    } else {
                        parametersData = task.parameters;
                    }
                }
                            } catch (e) {
                    console.error('Failed to parse parameters:', e);
                parametersData = null;
            }
            
            // 安全地解析result，只用于提取job_id
            let jobId = null;
            try {
                if (task.result) {
                    let resultData;
                    if (typeof task.result === 'string') {
                        resultData = JSON.parse(task.result);
                    } else {
                        resultData = task.result;
                    }
                    // 提取job_id
                    jobId = resultData?.job_id || null;
                }
                            } catch (e) {
                    console.error('Failed to parse result:', e);
            }

            content.innerHTML = `
                <div style="font-family: monospace; font-size: 14px; line-height: 1.6;">
                    <h4 style="margin: 0 0 15px 0; color: #333; font-size: 16px;">${t['basic-info-field']}</h4>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 15px;">
                        <div><strong>${t['task-id-field']}:</strong> ${task.task_id}</div>
                        <div><strong>${t['task-name-field']}:</strong> ${task.job_name}</div>
                        ${jobId ? `<div><strong>${t['job-id-field']}:</strong> ${jobId}</div>` : ''}
                        <div><strong>${t['status-field']}:</strong> <span style="padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; ${getStatusStyle(task.status)}">${getStatusText(task.status)}</span></div>
                        <div><strong>${t['created-time-field']}:</strong> ${formatDateTime(task.created_at)}</div>
                        <div><strong>${t['updated-time-field']}:</strong> ${formatDateTime(task.updated_at)}</div>
                        ${task.completed_at ? `<div><strong>${t['completed-time-field']}:</strong> ${formatDateTime(task.completed_at)}</div>` : ''}
                    </div>
                    
                    ${parametersData ? `
                    <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">${t['task-params-field']}</h4>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 15px;">
                        ${Object.entries(parametersData).map(([key, value]) => {
                            if (key === 'protein_content' || key === 'ligand_content') {
                                return `<div><strong>${key}:</strong> ${t['file-content-omitted']}</div>`;
                            }


                            // 处理 ligand_file 和 protein_file 对象，隐藏其中的 content 字段
                            if ((key === 'ligand_file' || key === 'protein_file') && typeof value === 'object' && value !== null) {
                                const filteredValue = { ...value };
                                if ('content' in filteredValue) {
                                    filteredValue.content = t['file-content-omitted'];
                                }
                                return `<div><strong>${key}:</strong> ${JSON.stringify(filteredValue, null, 2)}</div>`;
                            }
                            
                            return `<div><strong>${key}:</strong> ${typeof value === 'object' ? JSON.stringify(value) : value}</div>`;
                        }).join('')}
                    </div>
                    ` : ''}
                    
                    ${task.error_message ? `
                    <h4 style="margin: 0 0 10px 0; color: #dc3545; font-size: 16px;">${t['error-info-field']}</h4>
                    <div style="background: #f8d7da; color: #721c24; padding: 12px; border-radius: 6px; border: 1px solid #f5c6cb;">
                        ${task.error_message}
                    </div>
                    ` : ''}
                </div>
            `;
            
            modal.style.display = 'block';
        }

        // 获取状态样式
        function getStatusStyle(status) {
            const styles = {
                'pending': 'background: #fef3c7; color: #92400e;',
                'running': 'background: #dbeafe; color: #1e40af;',
                'completed': 'background: #d1fae5; color: #065f46;',
                'failed': 'background: #fee2e2; color: #991b1b;'
            };
            return styles[status] || 'background: #f3f4f6; color: #374151;';
        }

        // 显示DiffDock任务结果
        function showDiffDockTaskResults(taskId) {
            const resultsContainer = document.getElementById(`results-${taskId}`);
            const t = translations[currentLanguage];
            
            // 如果结果已经显示，则隐藏
            if (resultsContainer.style.display === 'block') {
                resultsContainer.style.display = 'none';
                return;
            }
            
            // 显示加载状态
            resultsContainer.innerHTML = `
                <div class="results-loading">
                    <div class="loading-spinner"></div>
                    <p>${t['loading-results']}</p>
                </div>
            `;
            resultsContainer.style.display = 'block';
            
            // 获取任务结果
            fetch(`/api/tasks/${taskId}/results`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.result_files) {
                        let resultsHtml = `
                            <div class="task-results-header">
                                <h5>📁 ${t['task-results-files']} (${data.result_files.length} ${t['files-count']})</h5>
                                ${data.completed_at ? `<div class="results-completed-time">${t['completed-at-time']} ${formatDateTime(data.completed_at)}</div>` : ''}
                            </div>
                            <div class="task-results-content">
                        `;
                        
                        if (data.result_files.length > 0) {
                            const filesHtml = data.result_files.map(file => `
                                <div class="inline-file-item">
                                    <div class="inline-file-icon">📄</div>
                                    <div class="inline-file-info">
                                        <div class="inline-file-name">${file.name}</div>
                                        <div class="inline-file-details">
                                            <span class="inline-file-type">${file.type}</span>
                                            ${file.size ? `<span class="inline-file-size">${file.size}</span>` : ''}
                                        </div>
                                        ${file.description ? `<div class="inline-file-description">${file.description}</div>` : ''}
                                    </div>
                                    <div class="inline-file-actions">
                                        ${file.url ? `
                                            <a href="${file.url}" class="inline-download-btn" target="_blank" rel="noopener noreferrer" title="${t['download-file-title']}">
                                                📥
                                            </a>
                                        ` : `
                                            <span class="inline-no-download">${t['no-download-link-available']}</span>
                                        `}
                                        ${file.name.toLowerCase().endsWith('.sdf') && file.url ? `
                                            <button class="inline-display-btn" onclick="displayMolecule('${taskId}', '${file.url}', '${file.name}')" title="${t['display-molecule-structure']}">
                                                👁️
                                            </button>
                                        ` : ''}
                                    </div>
                                    <!-- 分子查看器容器 -->
                                    <div id="viewer-${taskId}-${file.name.replace(/[^a-zA-Z0-9]/g, '_')}" class="molecule-viewer-container" style="display: none;"></div>
                                </div>
                            `).join('');
                            
                            resultsHtml += `
                                <div class="inline-result-files">
                                    ${filesHtml}
                                </div>
                            `;
                        } else {
                            resultsHtml += `
                                <div class="inline-no-files">
                                    <div class="inline-no-files-icon">📂</div>
                                    <p>${t['no-result-files-available']}</p>
                                    <p style="font-size: 0.75rem; color: #9ca3af;">${t['task-may-be-processing']}</p>
                                </div>
                            `;
                        }
                        
                        resultsHtml += '</div>';
                        resultsContainer.innerHTML = resultsHtml;
                        
                    } else {
                        resultsContainer.innerHTML = `
                            <div class="task-results-error">
                                <div class="error-icon">❌</div>
                                <div class="error-message">${t['get-results-failed']}: ${data.message || t['unknown-error']}</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error(t['get-task-results-error-console'], error);
                    resultsContainer.innerHTML = `
                        <div class="task-results-error">
                            <div class="error-icon">🌐</div>
                            <div class="error-message">${t['network-error-results']}</div>
                        </div>
                    `;
                });
        }

        // 关闭任务详情模态框
        function closeTaskDetailsModal() {
            const modal = document.getElementById('taskDetailsModal');
            modal.style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('taskDetailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTaskDetailsModal();
            }
        });

        // 获取状态文本
        function getStatusText(status) {
            const t = translations[currentLanguage];
            const statusMap = {
                'pending': t['status-pending'],
                'running': t['status-running'],
                'completed': t['status-completed'],
                'failed': t['status-failed']
            };
            return statusMap[status] || status;
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // ========== 分子结构可视化功能 ==========

        // 存储任务的蛋白质内容
        // const taskProteinData = {};
        const taskContentCache = {};

        // 显示分子结构
        async function displayMolecule(taskId, fileUrl, fileName) {
            const viewerId = `viewer-${taskId}-${fileName.replace(/[^a-zA-Z0-9]/g, '_')}`;
            const viewerContainer = document.getElementById(viewerId);
            
            if (!viewerContainer) {
                console.error('Cannot find viewer container:', viewerId);
                return;
            }

            // 如果查看器已经显示，则隐藏
            if (viewerContainer.style.display === 'block') {
                viewerContainer.style.display = 'none';
                return;
            }

            // 显示查看器容器
            viewerContainer.style.display = 'block';
            
            // 显示加载状态  
            const t = translations[currentLanguage];
            viewerContainer.innerHTML = `
                <div class="molecule-viewer-header">
                    <h4 class="molecule-viewer-title">🧬 ${fileName} - ${t['molecule-structure']}</h4>
                    <button class="molecule-viewer-close" onclick="closeMoleculeViewer('${viewerId}')">${t['close-btn']}</button>
                </div>
                <div class="viewer-loading">
                    <div class="loading-spinner"></div>
                    <p>${t['loading-molecule-structure']}</p>
                </div>
            `;

            viewerContainer.style.display = 'block';

            try {
                // 检查3Dmol是否可用
                if (typeof $3Dmol === 'undefined') {
                    throw new Error('3Dmol.js 库未加载');
                }

                let proteinContent = null;

                // 从任务参数中获取 protein_url
                if (!taskContentCache[taskId]) {
                    try {
                        const taskResponse = await fetch(`/api/tasks/${taskId}`);
                        if (taskResponse.ok) {
                            const taskData = await taskResponse.json();
                            if (taskData.success && taskData.task) {
                                let parameters = null;
                                try {
                                    if (typeof taskData.task.parameters === 'string') {
                                        parameters = JSON.parse(taskData.task.parameters);
                                    } else {
                                        parameters = taskData.task.parameters;
                                    }
                                    console.log('Task parameters:', parameters);
                                    
                                    // 将参数保存到缓存中以便后续使用
                                    taskContentCache[taskId] = {
                                        parameters: parameters
                                    };
                                } catch (e) {
                                    console.warn('Failed to parse task parameters:', e);
                                }
                            }
                        }
                    } catch (e) {
                        console.warn('Failed to get task data:', e);
                    }
                } 


                // 如果参数中有 protein_url，则下载蛋白质内容
                if (taskContentCache[taskId] && taskContentCache[taskId].parameters && taskContentCache[taskId].parameters.protein_url) {
                    const proteinUrl = taskContentCache[taskId].parameters.protein_url;
                    
                    try {
                        console.log('Downloading protein content from:', proteinUrl);
                        
                        // 使用代理API下载蛋白质文件内容
                        const proteinProxyResponse = await fetch('/api/proxy/file', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                url: proteinUrl
                            })
                        });
                        
                        if (proteinProxyResponse.ok) {
                            const proteinProxyData = await proteinProxyResponse.json();
                            if (proteinProxyData.success) {
                                proteinContent = proteinProxyData.content;
                                console.log('Successfully downloaded protein content');
                                
                                // 将蛋白质内容也保存到缓存中
                                taskContentCache[taskId].protein_content = proteinContent;
                            } else {
                                console.warn('Failed to get protein content via proxy:', proteinProxyData.message);
                            }
                        } else {
                            console.warn('Protein proxy request failed:', proteinProxyResponse.status);
                        }
                    } catch (e) {
                        console.warn('Error downloading protein content:', e);
                    }
                } else {
                    console.log('No protein_url found in task parameters');
                }


                // 获取分子文件内容 - 使用代理API避免CORS问题
                const proxyResponse = await fetch('/api/proxy/file', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        url: fileUrl
                    })
                });
                
                if (!proxyResponse.ok) {
                    throw new Error(`代理请求失败: ${proxyResponse.status}`);
                }
                
                const proxyData = await proxyResponse.json();
                if (!proxyData.success) {
                    throw new Error(proxyData.message || '获取文件失败');
                }
                
                const sdfContent = proxyData.content;

                // 创建查看器HTML
                const viewerHtml = `
                    <div class="molecule-viewer-header">
                        <h4 class="molecule-viewer-title">🧬 ${fileName} - ${t['molecule-structure']}${proteinContent ? ` + ${t['protein-structure']}` : ''}</h4>
                        <button class="molecule-viewer-close" onclick="closeMoleculeViewer('${viewerId}')">${t['close-btn']}</button>
                    </div>
                    <div id="${viewerId}_3dmol" class="molecule-viewer"></div>
                `;
                
                viewerContainer.innerHTML = viewerHtml;

                // 获取容器实际宽度
                const containerElement = document.getElementById(`${viewerId}_3dmol`);
                
                // 等待DOM完全渲染后再获取宽度
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // 尝试从多个源获取真实的可用宽度
                let actualWidth = containerElement.offsetWidth || containerElement.clientWidth;
                
                // 如果容器宽度太小，尝试从父容器获取
                const parentContainer = containerElement.parentElement; // molecule-viewer-container
                const grandparentContainer = parentContainer ? parentContainer.parentElement : null; // task-item或其他
                
                if (actualWidth < 400) {
                    console.log('Container width too small, checking parent containers...');
                    if (parentContainer) {
                        const parentWidth = parentContainer.offsetWidth || parentContainer.clientWidth;
                        console.log('Parent container width:', parentWidth);
                        if (parentWidth > actualWidth) {
                            actualWidth = parentWidth - 40; // subtract some padding
                        }
                    }
                    
                    if (actualWidth < 400 && grandparentContainer) {
                        const grandparentWidth = grandparentContainer.offsetWidth || grandparentContainer.clientWidth;
                        console.log('Grandparent container width:', grandparentWidth);
                        if (grandparentWidth > actualWidth) {
                            actualWidth = grandparentWidth - 60; // subtract more padding and margins
                        }
                    }
                    
                    // 如果仍然太小，设置一个合理的最小宽度
                    if (actualWidth < 400) {
                        actualWidth = Math.max(600, window.innerWidth * 0.6); // 使用窗口宽度的60%或至少600px
                        console.log('Using calculated width based on window:', actualWidth);
                    }
                }
                
                console.log('Container width:', containerElement.offsetWidth);
                console.log('Parent width:', parentContainer ? parentContainer.offsetWidth : 'no parent');
                console.log('Final calculated width:', actualWidth);
                console.log('Container element:', containerElement);

                // 创建3Dmol查看器 - 不指定尺寸，让它使用默认值后再修改
                const viewer3d = $3Dmol.createViewer(containerElement, {
                    defaultcolors: $3Dmol.rasmolElementColors,
                    backgroundColor: 'black'
                });

                // 添加蛋白质结构（如果有）
                if (proteinContent) {
                    viewer3d.addModel(proteinContent, 'pdb');
                    viewer3d.setStyle({}, {cartoon: {color: 'spectrum', opacity: 1.0}});
                }

                // 添加小分子结构
                viewer3d.addModel(sdfContent, 'sdf');
                viewer3d.setStyle({model: proteinContent ? 1 : 0}, {stick: {colorscheme: 'default', radius: 0.2, opacity: 1.0}});

                // 设置视图
                viewer3d.zoomTo();
                viewer3d.render();

                // 等待渲染完成后强制设置正确的尺寸
                setTimeout(() => {
                    // 重新计算实际宽度，使用与初始化时相同的逻辑
                    let resizeWidth = containerElement.offsetWidth || containerElement.clientWidth;
                    const parentContainer = containerElement.parentElement;
                    const grandparentContainer = parentContainer ? parentContainer.parentElement : null;
                    
                    if (resizeWidth < 400) {
                        if (parentContainer) {
                            const parentWidth = parentContainer.offsetWidth || parentContainer.clientWidth;
                            if (parentWidth > resizeWidth) {
                                resizeWidth = parentWidth - 40;
                            }
                        }
                        
                        if (resizeWidth < 400 && grandparentContainer) {
                            const grandparentWidth = grandparentContainer.offsetWidth || grandparentContainer.clientWidth;
                            if (grandparentWidth > resizeWidth) {
                                resizeWidth = grandparentWidth - 60;
                            }
                        }
                        
                        if (resizeWidth < 400) {
                            resizeWidth = Math.max(600, window.innerWidth * 0.6);
                        }
                    }
                    
                    console.log('Forcing resize to width:', resizeWidth);
                    
                    // 查找canvas元素
                    const canvas = containerElement.querySelector('canvas');
                    if (canvas) {
                        console.log('Canvas found, current dimensions:', canvas.width, 'x', canvas.height);
                        console.log('Canvas style:', canvas.style.width, 'x', canvas.style.height);
                        
                        // 强制设置canvas的实际像素尺寸
                        canvas.width = resizeWidth;
                        canvas.height = 250;
                        
                        // 强制设置CSS样式
                        canvas.style.width = resizeWidth + 'px';
                        canvas.style.height = '250px';
                        canvas.style.maxWidth = 'none';
                        canvas.style.minWidth = resizeWidth + 'px';
                        
                        console.log('Canvas dimensions after force:', canvas.width, 'x', canvas.height);
                        console.log('Canvas style after force:', canvas.style.width, 'x', canvas.style.height);
                        
                        // 调用3Dmol的resize方法
                        viewer3d.resize(resizeWidth, 250);
                        
                        // 重新渲染以应用新尺寸
                        viewer3d.render();
                    } else {
                        console.error('Canvas element not found in container');
                    }
                }, 200);

                // 第二次强制调整，确保生效
                setTimeout(() => {
                    // 使用相同的宽度计算逻辑
                    let finalWidth = containerElement.offsetWidth || containerElement.clientWidth;
                    const parentContainer = containerElement.parentElement;
                    const grandparentContainer = parentContainer ? parentContainer.parentElement : null;
                    
                    if (finalWidth < 400) {
                        if (parentContainer) {
                            const parentWidth = parentContainer.offsetWidth || parentContainer.clientWidth;
                            if (parentWidth > finalWidth) {
                                finalWidth = parentWidth - 40;
                            }
                        }
                        
                        if (finalWidth < 400 && grandparentContainer) {
                            const grandparentWidth = grandparentContainer.offsetWidth || grandparentContainer.clientWidth;
                            if (grandparentWidth > finalWidth) {
                                finalWidth = grandparentWidth - 60;
                            }
                        }
                        
                        if (finalWidth < 400) {
                            finalWidth = Math.max(600, window.innerWidth * 0.6);
                        }
                    }
                    
                    const canvas = containerElement.querySelector('canvas');
                    if (canvas) {
                        canvas.width = finalWidth;
                        canvas.style.width = finalWidth + 'px';
                        viewer3d.resize(finalWidth, 250);
                        viewer3d.render();
                        console.log('Second force resize completed, final width:', finalWidth);
                    }
                }, 500);

                console.log(`Molecule structure loaded: ${fileName}`);

            } catch (error) {
                console.error('Failed to load molecular structure:', error);
                const t = translations[currentLanguage];
                viewerContainer.innerHTML = `
                    <div class="molecule-viewer-header">
                        <h4 class="molecule-viewer-title">🧬 ${fileName} - ${t['loading-failed']}</h4>
                        <button class="molecule-viewer-close" onclick="closeMoleculeViewer('${viewerId}')">${t['close-btn']}</button>
                    </div>
                    <div class="viewer-error">
                        <div class="viewer-error-icon">❌</div>
                        <p>${t['failed-to-load-molecule-structure']}</p>
                        <p style="font-size: 0.75rem; color: #9ca3af;">${error.message}</p>
                    </div>
                `;
            }
        }

        // 关闭分子查看器
        function closeMoleculeViewer(viewerId) {
            const viewerContainer = document.getElementById(viewerId);
            if (viewerContainer) {
                viewerContainer.style.display = 'none';
                viewerContainer.innerHTML = '';
            }
        }


        // 一键下载DiffDock任务的所有结果文件
        function downloadDiffDockTaskAllResults(taskId) {
            console.log('=== Download DiffDock Task All Results Debug ===');
            console.log('Task ID:', taskId);
            
            const t = translations[currentLanguage];
            
            // 显示加载提示
            const loadingNotification = document.createElement('div');
            loadingNotification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 4px;">📦 准备下载任务结果</div>
                <div style="font-size: 12px;">任务ID: ${taskId}</div>
                <div style="font-size: 12px;">正在获取文件列表...</div>
            `;
            loadingNotification.style.position = 'fixed';
            loadingNotification.style.top = '20px';
            loadingNotification.style.right = '20px';
            loadingNotification.style.background = '#3b82f6';
            loadingNotification.style.color = 'white';
            loadingNotification.style.padding = '12px 16px';
            loadingNotification.style.borderRadius = '8px';
            loadingNotification.style.zIndex = '10000';
            loadingNotification.style.fontSize = '13px';
            loadingNotification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
            loadingNotification.style.maxWidth = '300px';
            
            document.body.appendChild(loadingNotification);
            
            // 获取任务结果
            fetch(`/api/tasks/${taskId}/results`)
                .then(response => response.json())
                .then(data => {
                    // 移除加载提示
                    if (document.body.contains(loadingNotification)) {
                        document.body.removeChild(loadingNotification);
                    }
                    
                    if (data.success && data.result_files && data.result_files.length > 0) {
                        const resultFiles = data.result_files;
                        const totalFiles = resultFiles.length;
                        
                        console.log(`准备下载任务 ${taskId} 的所有结果: ${totalFiles} 个文件`);
                        
                        // 显示下载进度提示
                        const notification = document.createElement('div');
                        notification.innerHTML = `
                            <div style="font-weight: bold; margin-bottom: 4px;">📦 下载任务结果</div>
                            <div style="font-size: 12px;">任务ID: ${taskId}</div>
                            <div style="font-size: 12px;">正在下载 ${totalFiles} 个文件...</div>
                        `;
                        notification.style.position = 'fixed';
                        notification.style.top = '20px';
                        notification.style.right = '20px';
                        notification.style.background = '#f57c00';
                        notification.style.color = 'white';
                        notification.style.padding = '12px 16px';
                        notification.style.borderRadius = '8px';
                        notification.style.zIndex = '10000';
                        notification.style.fontSize = '13px';
                        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                        notification.style.maxWidth = '300px';
                        
                        document.body.appendChild(notification);
                        
                        // 显示详细的下载进度
                        const progressNotification = document.createElement('div');
                        progressNotification.innerHTML = `
                            <div style="font-size: 12px; opacity: 0.9;">下载进度: 0/${totalFiles}</div>
                            <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; margin-top: 4px;">
                                <div id="diffdock-progress-bar" style="width: 0%; height: 100%; background: white; border-radius: 2px; transition: width 0.2s;"></div>
                            </div>
                        `;
                        progressNotification.style.position = 'fixed';
                        progressNotification.style.top = '110px';
                        progressNotification.style.right = '20px';
                        progressNotification.style.background = '#e65100';
                        progressNotification.style.color = 'white';
                        progressNotification.style.padding = '8px 12px';
                        progressNotification.style.borderRadius = '6px';
                        progressNotification.style.zIndex = '10000';
                        progressNotification.style.fontSize = '12px';
                        
                        document.body.appendChild(progressNotification);
                        
                        // 模拟打包进度更新
                        let packingProgress = 0;
                        const progressInterval = setInterval(() => {
                            packingProgress++;
                            const progressPercent = (packingProgress / totalFiles) * 80; // 打包进度占80%
                            
                            // 更新进度文字和进度条
                            const progressText = progressNotification.querySelector('div');
                            const progressBar = document.getElementById('diffdock-progress-bar');
                            
                            if (progressText) {
                                progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">打包进度: ${packingProgress}/${totalFiles} 个文件</div>`;
                            }
                            if (progressBar) {
                                progressBar.style.width = `${progressPercent}%`;
                            }
                            
                            if (packingProgress >= totalFiles) {
                                clearInterval(progressInterval);
                                
                                // 开始调用压缩包API
                                if (progressText) {
                                    progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">正在生成压缩包...</div>`;
                                }
                                
                                // 调用后端API创建压缩包
                                fetch(`/api/tasks/${taskId}/download-zip`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                    }
                                    return response.json();
                                })
                                .then(zipData => {
                                    // 完成进度到100%
                                    if (progressBar) {
                                        progressBar.style.width = '100%';
                                    }
                                    if (progressText) {
                                        progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">✅ 压缩包创建完成!</div>`;
                                    }
                                    
                                    if (zipData.success && zipData.download_url) {
                                        // 更新主通知
                                        notification.innerHTML = `
                                            <div style="font-weight: bold; margin-bottom: 4px;">📦 开始下载压缩包</div>
                                            <div style="font-size: 12px;">文件: ${zipData.filename || `task_${taskId}_results.zip`}</div>
                                            <div style="font-size: 12px;">包含: ${totalFiles} 个结果文件</div>
                                            ${zipData.file_size ? `<div style="font-size: 12px;">大小: ${zipData.file_size}</div>` : ''}
                                        `;
                                        
                                        // 创建下载链接并触发下载
                                        const link = document.createElement('a');
                                        link.href = zipData.download_url;
                                        link.download = zipData.filename || `task_${taskId}_results.zip`;
                                        link.style.display = 'none';
                                        
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                        
                                        console.log('压缩包下载开始:', zipData.filename);
                                        
                                        // 显示成功通知
                                        // showNotification(`开始下载任务 ${taskId} 的结果压缩包 (${totalFiles}个文件)`, 'success');
                                        
                                    } else {
                                        throw new Error(zipData.message || '创建压缩包失败');
                                    }
                                })
                                .catch(zipError => {
                                    console.error('创建压缩包失败:', zipError);
                                    
                                    if (progressText) {
                                        progressText.innerHTML = `<div style="font-size: 12px; opacity: 0.9;">❌ 创建压缩包失败</div>`;
                                    }
                                    
                                    // 根据错误类型显示不同的提示
                                    let errorMessage = '创建压缩包失败，请稍后重试';
                                    if (zipError.message.includes('404')) {
                                        errorMessage = '该任务的结果文件不可用';
                                    } else if (zipError.message.includes('500')) {
                                        errorMessage = '服务器创建压缩包时出错';
                                    }
                                    
                                    showNotification(errorMessage, 'error');
                                });
                            }
                        }, 100); // 快速更新进度，让用户看到进展
                        
                        // 8秒后移除通知（给足够时间完成整个流程）
                        setTimeout(() => {
                            if (document.body.contains(notification)) {
                                document.body.removeChild(notification);
                            }
                            if (document.body.contains(progressNotification)) {
                                document.body.removeChild(progressNotification);
                            }
                        }, 8000);
                        
                    } else {
                        showNotification('该任务暂无可下载的结果文件', 'warning');
                    }
                })
                .catch(error => {
                    // 移除加载提示
                    if (document.body.contains(loadingNotification)) {
                        document.body.removeChild(loadingNotification);
                    }
                    
                    console.error('获取任务结果失败:', error);
                    alert('获取任务结果失败，请稍后重试');
                });
        }

</script>
{% endblock %}