<!--
    Header Component
    顶部导航栏组件

    功能: 显示Logo、语言切换、用户信息、退出登录
    依赖: navigation.css, i18n.js, navigation.js
    使用: {# include 'components/header.html' #}
-->
<header class="header">
    <!-- Logo区域 -->
    <a href="home" style="text-decoration: none;">
        <div class="logo" data-i18n="platform-name">QureGenAI 药物设计平台</div>
    </a>
    
    <!-- 用户信息区域 -->
    <div class="user-info">
        <!-- 语言切换按钮 -->
        <div class="language-switcher">
            <button class="language-btn js-language-btn" data-lang="zh">
                🇨🇳 中文
            </button>
            <button class="language-btn js-language-btn active" data-lang="en">
                🇺🇸 EN
            </button>
        </div>
        
        <!-- 用户头像 -->
        <div class="user-avatar" id="userAvatar"></div>
        
        <!-- 用户详细信息 -->
        <div class="user-details">
            <div class="username" id="username"></div>
            <div class="login-time" id="loginTime"></div>
        </div>
        
        <!-- 退出登录按钮 -->
        <button class="logout-btn js-logout-btn" data-i18n="logout-btn">退出登录</button>
    </div>
</header>
