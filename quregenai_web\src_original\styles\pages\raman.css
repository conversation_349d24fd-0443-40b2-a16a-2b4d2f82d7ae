.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page-header {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    border-left: 4px solid #87ceeb;
}

.page-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #87ceeb 0%, #5f9ea0 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.page-subtitle {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.raman-intro {
    background: #f0f8ff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e1f5fe;
    margin-bottom: 1rem;
}

.raman-intro h3 {
    color: #1976d2;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.raman-intro p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e1e5e9;
}

.section-header h2 {
    color: #333;
    margin: 0;
    font-size: 1.5rem;
}

.btn {
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-refresh-blue {
    background: #1976d2;
    color: white;
}

.btn-refresh-blue:hover {
    background: #1565c0;
    transform: translateY(-1px);
}

.task-history-container {
    min-height: 300px;
}

.tasks-list {
    display: grid;
    gap: 1rem;
}

/* 优化任务项样式，让检测ID更大更突出，整体排版更美观 */
.task-item {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    border-left: 4px solid #87ceeb;
    margin-bottom: 1rem;
}

/* .task-item:hover {
    border-color: #87ceeb;
    box-shadow: 0 4px 15px rgba(135, 206, 235, 0.2);
    transform: translateY(-2px);
} */

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e8f4f8;
}

/* 检测ID样式优化 - 更大更突出 */
.task-id {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e3a8a;
    /* background: linear-gradient(135deg, #87ceeb, #4682b4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text; */
    letter-spacing: 0.5px;
}

.task-status {
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-status.completed {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.task-status.running {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.task-status.failed {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.task-status.pending {
    background: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ffe0b2;
}

/* 任务详情区域优化 */
.task-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
    margin-top: 1rem;
}

.task-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #ffffff;
    border-radius: 0.5rem;
    border: 1px solid #e8f4f8;
    transition: all 0.2s ease;
}

/* .task-detail-item:hover {
    background: #f0f8ff;
    border-color: #87ceeb;
    transform: translateX(4px);
} */

.task-detail-label {
    color: #5a6c7d;
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 90px;
    flex-shrink: 0;
    position: relative;
}

.task-detail-label::after {
    content: ':';
    margin-left: 0.25rem;
    color: #87ceeb;
    font-weight: 700;
}

.task-detail-value {
    color: #2c3e50;
    font-weight: 500;
    font-size: 0.9rem;
    text-align: right;
    flex: 1;
    margin-left: 1rem;
    word-break: break-word;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 空值样式 */
.task-detail-value:empty::before,
.task-detail-value:contains('未知')::before {
    content: '—';
    color: #bdc3c7;
    font-style: italic;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .task-details {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .task-id {
        font-size: 1rem;
    }
    
    .task-status {
        align-self: flex-end;
    }
    
    .task-detail-item {
        padding: 0.5rem 0.75rem;
    }
    
    .task-detail-label {
        min-width: 80px;
        font-size: 0.85rem;
    }
    
    .task-detail-value {
        font-size: 0.85rem;
    }
}

/* 任务项加载动画 */
.task-item {
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 无任务时的样式 */
.no-tasks-message {
    text-align: center;
    padding: 3rem 2rem;
    color: #7f8c8d;
    font-size: 1.1rem;
    background: #f8f9fa;
    border-radius: 0.75rem;
    border: 2px dashed #bdc3c7;
}

.no-tasks-message p {
    margin: 0;
    font-weight: 500;
}

.btn-view-details {
    background: #87ceeb;
    color: white;
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

.btn-view-details:hover {
    background: #5f9ea0;
}

.btn-view-details:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.pagination-container {
    margin-top: 2rem;
    border-top: 1px solid #e1e5e9;
    padding-top: 1.5rem;
}

.pagination-info {
    text-align: center;
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #666;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #87ceeb;
    color: #87ceeb;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #666;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    min-width: 2.5rem;
    text-align: center;
    transition: all 0.2s ease;
}

.page-number-btn:hover {
    background: #e9ecef;
    border-color: #87ceeb;
    color: #87ceeb;
}

.page-number-btn.active {
    background: #87ceeb;
    border-color: #87ceeb;
    color: white;
}

.loading-tasks {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-tasks {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #87ceeb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .content {
        padding: 1rem;
    }

    .task-details {
        grid-template-columns: 1fr;
    }
}

/* 在现有CSS中添加分页省略号样式 */
.page-number-btn.ellipsis {
    background: none;
    border: none;
    color: #666;
    cursor: default;
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
}

.page-number-btn.ellipsis:hover {
    background: none;
    border: none;
    color: #666;
}


/* 检测ID容器样式 */
.task-id-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

/* 复制按钮样式 */
.copy-btn {
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 0.25rem;
    cursor: pointer;
    color: #87ceeb;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
}

.copy-btn:hover {
    opacity: 1;
    background: rgba(135, 206, 235, 0.1);
    transform: scale(1.1);
}

.copy-btn:active {
    transform: scale(0.95);
}

/* 复制成功状态 */
.copy-btn.copy-success {
    color: #2e7d32;
    background: rgba(46, 125, 50, 0.1);
}

/* 复制失败状态 */
.copy-btn.copy-error {
    color: #c62828;
    background: rgba(198, 40, 40, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .copy-btn {
        padding: 0.2rem;
    }
    
    .copy-btn svg {
        width: 14px;
        height: 14px;
    }
}