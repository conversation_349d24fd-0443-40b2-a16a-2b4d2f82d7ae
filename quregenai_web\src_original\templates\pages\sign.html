<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="page-title">Register - QureGenAI Quantum Computing & AI Drug Design Platform</title>
    <link rel="icon" type="image/png" href="/src/images/icon.png">
    <link rel="stylesheet" href="/src/styles/pages/sign.css">
    <!--1.在引入阿里云验证码JS脚本的位置之前，或者在html的head标签最前的位置添加一个script脚本，里面保存一个含有region和prefix参数的全局变量AliyunCaptchaConfig即可-->
    <script>
        window.AliyunCaptchaConfig = {
            // 必填，验证码示例所属地区，支持中国内地（cn）、新加坡（sgp）
            region: "cn",
            // 必填，身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
            prefix: "7kaa4s",
        };
    </script>
    <!--2.集成主JS-->
    <script
    type="text/javascript"
    src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"
    ></script>
</head>
<body>
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
        <button class="language-btn" data-lang="zh" onclick="switchLanguage('zh')">
            🇨🇳 中文
        </button>
        <button class="language-btn active" data-lang="en" onclick="switchLanguage('en')">
            🇺🇸 EN
        </button>
    </div>

    <div class="register-container">
        <div class="register-header">
            <h1 data-i18n="register-title">Register Account</h1>
            <p data-i18n="register-subtitle">Create your QureGenAI drug design platform account</p>
        </div>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="contactInfo" data-i18n="contact-info-label">Mobile/Email</label>
                <div style="display: flex;">
                    <input type="text" id="contactInfo" name="contactInfo" required data-i18n-placeholder="contact-info-placeholder" placeholder="Enter mobile number or email" style="flex: 1; margin-right: 10px;">
                    <div id="captcha-element"></div>
                    <button type="button" id="sendCodeBtn" class="code-btn" data-i18n="send-code-btn">Get Code</button>
                </div>
            </div>

            <div class="form-group">
                <label for="verificationCode" data-i18n="verification-code-label">Verification Code</label>
                <input type="text" id="verificationCode" name="verificationCode" required data-i18n-placeholder="verification-code-placeholder" placeholder="Enter verification code">
            </div>

            <div class="form-group">
                <label for="password" data-i18n="password-label">Password</label>
                <input type="password" id="password" name="password" required data-i18n-placeholder="password-placeholder" placeholder="Enter password">
                <div class="password-requirements">
                    <div class="requirement neutral" id="req-length">
                        <div class="requirement-icon">?</div>
                        <span data-i18n="req-length">At least 6 characters</span>
                    </div>
                    <div class="requirement neutral" id="req-uppercase">
                        <div class="requirement-icon">?</div>
                        <span data-i18n="req-uppercase">Contains uppercase letter</span>
                    </div>
                    <div class="requirement neutral" id="req-lowercase">
                        <div class="requirement-icon">?</div>
                        <span data-i18n="req-lowercase">Contains lowercase letter</span>
                    </div>
                    <div class="requirement neutral" id="req-number">
                        <div class="requirement-icon">?</div>
                        <span data-i18n="req-number">Contains number</span>
                    </div>
                    <div class="requirement neutral" id="req-special">
                        <div class="requirement-icon">?</div>
                        <span data-i18n="req-special">Contains special character (!@#$%^&* etc.)</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="confirmPassword" data-i18n="confirm-password-label">Confirm Password</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required data-i18n-placeholder="confirm-password-placeholder" placeholder="Enter password again">
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage" data-i18n="register-success">Registration successful! Redirecting to homepage...</div>

            <!-- 用户协议选择框 -->
            <div class="agreement-container">
                <input type="checkbox" id="agreementCheckbox" class="agreement-checkbox">
                <label for="agreementCheckbox" class="agreement-text">
                    <span data-i18n="agreement-text">I have read and agree to the</span>
                    <a href="/qau-cloud/terms_of_service" target="_blank" class="agreement-link" data-i18n="user-agreement">User Agreement</a>
                </label>
            </div>

            <button type="submit" class="register-btn" id="registerBtn" disabled data-i18n="register-btn">Register</button>

            <div class="auth-divider">
                <span data-i18n="already-have-account">Already have an account?</span>
            </div>

            <button type="button" class="back-btn" onclick="goToLogin()" data-i18n="back-to-login">Back to Login</button>
        </form>
    </div>


    <!--3.新建一个<script>标签，用于调用验证码初始化函数initAliyunCaptcha-->
    <script type="text/javascript">

    </script>
    <script>
        // 多语言文本配置
        const translations = {
            zh: {
                'page-title': '注册 - QureGenAI量子计算 & AI药物设计平台',
                'register-title': '注册账户',
                'register-subtitle': '创建您的QureGenAI药物设计平台账户',
                'contact-info-label': '手机号码/邮箱',
                'contact-info-placeholder': '请输入手机号码或邮箱',
                'send-code-btn': '获取验证码',
                'verification-code-label': '验证码',
                'verification-code-placeholder': '请输入验证码',
                'password-label': '密码',
                'password-placeholder': '请输入密码',
                'confirm-password-label': '确认密码',
                'confirm-password-placeholder': '请再次输入密码',
                'req-length': '至少6位字符',
                'req-uppercase': '包含大写字母',
                'req-lowercase': '包含小写字母',
                'req-number': '包含数字',
                'req-special': '包含特殊符号(!@#$%^&*等)',
                'register-success': '注册成功！正在跳转到首页...',
                'register-btn': '注册',
                'already-have-account': '已有账户？',
                'back-to-login': '返回登录',
                'send-code-retry': '秒后重试',
                'invalid-contact': '请输入有效的手机号码或邮箱',
                'code-sent': '验证码已发送',
                'code-send-failed': '验证码发送失败',
                'network-error': '网络错误，请检查连接后重试',
                'password-not-match': '两次输入的密码不一致',
                'password-invalid': '密码不符合要求',
                'enter-code': '请输入验证码',
                'register-failed': '注册失败，请重试',
                'agreement-text': '我已阅读并同意',
                'user-agreement': '用户协议',
                'user-agreement-title': '用户协议',
                'user-agreement-content': '用户协议内容',
                'modal-close-btn': '关闭',
                'agreement-required': '请先同意用户协议'
            },
            en: {
                'page-title': 'Register - QureGenAI Quantum Computing & AI Drug Design Platform',
                'register-title': 'Register Account',
                'register-subtitle': 'Create your QureGenAI drug design platform account',
                'contact-info-label': 'Mobile/Email',
                'contact-info-placeholder': 'Enter mobile number or email',
                'send-code-btn': 'Get Code',
                'verification-code-label': 'Verification Code',
                'verification-code-placeholder': 'Enter verification code',
                'password-label': 'Password',
                'password-placeholder': 'Enter password',
                'confirm-password-label': 'Confirm Password',
                'confirm-password-placeholder': 'Enter password again',
                'req-length': 'At least 6 characters',
                'req-uppercase': 'Contains uppercase letter',
                'req-lowercase': 'Contains lowercase letter',
                'req-number': 'Contains number',
                'req-special': 'Contains special character (!@#$%^&* etc.)',
                'register-success': 'Registration successful! Redirecting to homepage...',
                'register-btn': 'Register',
                'already-have-account': 'Already have an account?',
                'back-to-login': 'Back to Login',
                'send-code-retry': 'seconds to retry',
                'invalid-contact': 'Please enter a valid mobile number or email',
                'code-sent': 'Verification code sent',
                'code-send-failed': 'Failed to send verification code',
                'network-error': 'Network error, please check connection and try again',
                'password-not-match': 'Passwords do not match',
                'password-invalid': 'Password does not meet requirements',
                'enter-code': 'Please enter verification code',
                'register-failed': 'Registration failed, please try again',
                'agreement-text': 'I have read and agree to the',
                'user-agreement': 'User Agreement',
                'user-agreement-title': 'User Agreement',
                'user-agreement-content': 'User Agreement Content',
                'modal-close-btn': 'Close',
                'agreement-required': 'Please agree to the User Agreement first'
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });

            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';

            // 更新页面文本
            updatePageText(lang);
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];

            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t && t[key]) {
                    if (element.tagName === 'TITLE') {
                        element.textContent = t[key];
                    } else {
                        element.textContent = t[key];
                    }
                }
            });

            // 更新placeholder属性
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (t && t[key]) {
                    element.placeholder = t[key];
                }
            });

            // 更新验证码按钮文本（如果正在倒计时）
            if (sendCodeBtn.disabled && countdown > 0) {
                sendCodeBtn.textContent = `${countdown}${t['send-code-retry']}`;
            }
        }

        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const contactInfoInput = document.getElementById('contactInfo');
        const verificationCodeInput = document.getElementById('verificationCode');
        const registerBtn = document.getElementById('registerBtn');
        const sendCodeBtn = document.getElementById('sendCodeBtn');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const agreementCheckbox = document.getElementById('agreementCheckbox');

        // 验证码计时器
        let countdown = 60;
        let timer = null;

        // 联系方式验证（手机号或邮箱）
        function validateContactInfo(contactInfo) {
            const mobileRegex = /^1[3-9]\d{9}$/;
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return mobileRegex.test(contactInfo) || emailRegex.test(contactInfo);
        }
        
        // 判断联系方式类型
        function getContactType(contactInfo) {
            const mobileRegex = /^1[3-9]\d{9}$/;
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            
            if (mobileRegex.test(contactInfo)) {
                return 'mobile';
            } else if (emailRegex.test(contactInfo)) {
                return 'email';
            } else {
                return null;
            }
        }

        // 验证码发送逻辑
        sendCodeBtn.addEventListener('click', async function() {
            const contactInfo = contactInfoInput.value.trim();
            const contactType = getContactType(contactInfo);
            
            // 验证联系方式
            if (!validateContactInfo(contactInfo)) {
                showError(translations[currentLanguage]['invalid-contact']);
                return;
            }
            
            try {
                // 发送验证码请求
                const response = await fetch('/api/send_verification_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        contactInfo: contactInfo,
                        method: contactType 
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    console.log(data.rescode, typeof data.rescode);
                    if (data.rescode === '200') {
                        showSuccess(translations[currentLanguage]['code-sent']);

                        startCountdown();
                    } else {
                        showError(data.notice || translations[currentLanguage]['code-send-failed']);
                    }
                } else {
                    showError(data.message || translations[currentLanguage]['code-send-failed']);
                }
            } catch (error) {
                console.error('发送验证码错误:', error);
                showError(translations[currentLanguage]['network-error']);
            }
        });

        function startCountdown() {
            sendCodeBtn.disabled = true;
            countdown = 60;

            sendCodeBtn.textContent = `${countdown}${translations[currentLanguage]['send-code-retry']}`;

            timer = setInterval(() => {
                countdown--;
                sendCodeBtn.textContent = `${countdown}${translations[currentLanguage]['send-code-retry']}`;

                if (countdown <= 0) {
                    clearInterval(timer);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = translations[currentLanguage]['send-code-btn'];
                }
            }, 1000);
        }

        // 密码要求检查
        const requirements = {
            length: /^.{6,}$/,
            uppercase: /[A-Z]/,
            lowercase: /[a-z]/,
            number: /[0-9]/,
            special: /[!@#$%^&*(),.?":{}|<>_+=\-\[\]\\;'`~]/ // 包含特殊符号
        };

        function checkPasswordRequirements(password) {
            const checks = {
                length: requirements.length.test(password),
                uppercase: requirements.uppercase.test(password),
                lowercase: requirements.lowercase.test(password),
                number: requirements.number.test(password),
                special: requirements.special.test(password)
            };

            // 更新UI
            Object.keys(checks).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                const icon = element.querySelector('.requirement-icon');
                
                if (checks[req]) {
                    element.className = 'requirement valid';
                    icon.textContent = '✓';
                } else {
                    element.className = 'requirement invalid';
                    icon.textContent = '✗';
                }
            });

            return Object.values(checks).every(check => check);
        }

        function validateForm() {
            const contactInfo = contactInfoInput.value.trim();
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            const verificationCode = verificationCodeInput.value.trim();
            const isAgreementChecked = agreementCheckbox.checked;

            const isPasswordValid = checkPasswordRequirements(password);
            const isPasswordMatch = password === confirmPassword && password.length > 0;
            const isContactInfoValid = validateContactInfo(contactInfo);
            const isCodeValid = verificationCode.length > 0;

            // 更新密码输入框样式
            if (password.length > 0) {
                passwordInput.className = isPasswordValid ? 'valid' : 'invalid';
            } else {
                passwordInput.className = '';
            }

            // 更新确认密码输入框样式
            if (confirmPassword.length > 0) {
                confirmPasswordInput.className = isPasswordMatch ? 'valid' : 'invalid';
            } else {
                confirmPasswordInput.className = '';
            }

            // 更新联系方式输入框样式
            if (contactInfo.length > 0) {
                contactInfoInput.className = isContactInfoValid ? 'valid' : 'invalid';
            } else {
                contactInfoInput.className = '';
            }
            
            // 更新验证码输入框样式
            if (verificationCode.length > 0) {
                verificationCodeInput.className = 'valid';
            } else {
                verificationCodeInput.className = '';
            }

            // 启用/禁用注册按钮
            registerBtn.disabled = !(isPasswordValid && isPasswordMatch && isContactInfoValid && isCodeValid && isAgreementChecked);
        }

        // 事件监听
        passwordInput.addEventListener('input', validateForm);
        confirmPasswordInput.addEventListener('input', validateForm);
        contactInfoInput.addEventListener('input', validateForm);
        verificationCodeInput.addEventListener('input', validateForm);
        agreementCheckbox.addEventListener('change', validateForm);

        // 表单提交
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const contactInfo = contactInfoInput.value.trim();
            const contactType = getContactType(contactInfo);
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            const verificationCode = verificationCodeInput.value.trim();

            // 最终验证
            if (!checkPasswordRequirements(password)) {
                showError(translations[currentLanguage]['password-invalid']);
                return;
            }

            if (password !== confirmPassword) {
                showError(translations[currentLanguage]['password-not-match']);
                return;
            }

            if (!validateContactInfo(contactInfo)) {
                showError(translations[currentLanguage]['invalid-contact']);
                return;
            }

            if (!verificationCode) {
                showError(translations[currentLanguage]['enter-code']);
                return;
            }

            if (!agreementCheckbox.checked) {
                showError(translations[currentLanguage]['agreement-required']);
                return;
            }

            try {
                // 发送注册请求到后端
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contactInfo: contactInfo,
                        method: contactType,
                        password: password,
                        verificationCode: verificationCode
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess(translations[currentLanguage]['register-success']);
                    setTimeout(() => {
                        window.location.href = 'home';
                    }, 2000);
                } else {
                    // 注册失败 - 显示错误信息并跳转回注册页面
                    console.log('❌ 注册失败:', data.message);
                    showError(data.message || translations[currentLanguage]['register-failed']);

                    // 3秒后跳转回注册页面（刷新当前页面）
                    setTimeout(() => {
                        console.log('注册失败，1秒后刷新注册页面');
                        // window.location.href = 'sign';  // 跳转回注册页面
                    }, 1000);
                }
            } catch (error) {
                console.error('注册错误:', error);
                showError(translations[currentLanguage]['network-error']);
            }
        });

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 5000);
        }

        function goToLogin() {
            window.location.href = 'login';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化语言
            switchLanguage(currentLanguage);
        });

        // 检查是否已经登录
        if (localStorage.getItem('isLoggedIn') === 'true') {
            window.location.href = 'home';
        }
    </script>
</body>
</html> 