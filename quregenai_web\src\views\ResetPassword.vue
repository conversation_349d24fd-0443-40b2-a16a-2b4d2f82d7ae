<template>
  <div class="reset-password-page">
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'zh' }" 
        @click="switchLanguage('zh')"
      >
        🇨🇳 中文
      </button>
      <button 
        class="language-btn" 
        :class="{ active: currentLanguage === 'en' }" 
        @click="switchLanguage('en')"
      >
        🇺🇸 EN
      </button>
    </div>

    <div class="reset-password-container">
      <div class="reset-password-header">
        <h1>{{ t('reset.title') }}</h1>
        <p>{{ t('reset.subtitle') }}</p>
      </div>
      
      <form @submit.prevent="submitForm">
        <div id="resetContent" v-show="!showSuccessMessage">
          <div class="form-group">
            <label for="contactInfo">{{ t('reset.contact-info-label') }}</label>
            <div class="input-with-button">
              <input 
                type="text" 
                id="contactInfo" 
                v-model="formData.contactInfo" 
                required 
                :placeholder="t('reset.contact-info-placeholder')"
                :class="{ 'valid': isContactValid && formData.contactInfo, 'invalid': !isContactValid && formData.contactInfo }"
                @input="validateContact"
              >
              <button 
                type="button" 
                class="verification-btn" 
                :disabled="!isContactValid || countdown > 0" 
                @click="getVerificationCode"
              >
                {{ countdown > 0 ? `${countdown}${t('reset.send-code-retry')}` : t('reset.send-code-btn') }}
              </button>
            </div>
          </div>
          
          <div class="form-group">
            <label for="verificationCode">{{ t('reset.verification-code-label') }}</label>
            <input 
              type="text" 
              id="verificationCode" 
              v-model="formData.verificationCode" 
              required 
              :placeholder="t('reset.verification-code-placeholder')"
              :class="{ 'valid': formData.verificationCode.length > 0 }"
              @input="validateForm"
            >
          </div>
          
          <div class="form-group">
            <label for="newPassword">{{ t('reset.new-password-label') }}</label>
            <input 
              type="password" 
              id="newPassword" 
              v-model="formData.newPassword" 
              required 
              :placeholder="t('reset.new-password-placeholder')"
              :class="{ 'valid': isPasswordValid && formData.newPassword, 'invalid': !isPasswordValid && formData.newPassword }"
              @input="validatePassword"
            >
            <div class="password-requirements" v-if="formData.newPassword">
              <div :class="['requirement', passwordChecks.length ? 'valid' : 'invalid']">
                <div class="requirement-icon">{{ passwordChecks.length ? '✓' : '✗' }}</div>
                <span>{{ t('reset.req-length') }}</span>
              </div>
              <div :class="['requirement', passwordChecks.uppercase ? 'valid' : 'invalid']">
                <div class="requirement-icon">{{ passwordChecks.uppercase ? '✓' : '✗' }}</div>
                <span>{{ t('reset.req-uppercase') }}</span>
              </div>
              <div :class="['requirement', passwordChecks.lowercase ? 'valid' : 'invalid']">
                <div class="requirement-icon">{{ passwordChecks.lowercase ? '✓' : '✗' }}</div>
                <span>{{ t('reset.req-lowercase') }}</span>
              </div>
              <div :class="['requirement', passwordChecks.number ? 'valid' : 'invalid']">
                <div class="requirement-icon">{{ passwordChecks.number ? '✓' : '✗' }}</div>
                <span>{{ t('reset.req-number') }}</span>
              </div>
              <div :class="['requirement', passwordChecks.special ? 'valid' : 'invalid']">
                <div class="requirement-icon">{{ passwordChecks.special ? '✓' : '✗' }}</div>
                <span>{{ t('reset.req-special') }}</span>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label for="confirmPassword">{{ t('reset.confirm-new-password-label') }}</label>
            <input 
              type="password" 
              id="confirmPassword" 
              v-model="formData.confirmPassword" 
              required 
              :placeholder="t('reset.confirm-new-password-placeholder')"
              :class="{ 'valid': isPasswordMatch && formData.confirmPassword, 'invalid': !isPasswordMatch && formData.confirmPassword }"
              @input="validateForm"
            >
          </div>
          
          <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
          
          <button type="submit" class="reset-btn" :disabled="!isFormValid">{{ t('reset.reset-password-btn') }}</button>
          
          <div class="auth-divider">
            <span>{{ t('reset.remembered-password') }}</span>
          </div>
          
          <button type="button" class="back-btn" @click="goToLogin">{{ t('reset.back-to-login') }}</button>
        </div>

        <!-- 成功消息 -->
        <div class="success-message" v-show="showSuccessMessage">
          <p>{{ t('reset.reset-success') }}</p>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getFindPwdCode, updatePassword } from '../api/login'
import { t, switchLanguage, currentLanguage } from '../utils/i18n'

const router = useRouter()

// 响应式数据
const formData = reactive({
  contactInfo: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
})

const isContactValid = ref(false)
const passwordChecks = reactive({
  length: false,
  uppercase: false,
  lowercase: false,
  number: false,
  special: false
})
const countdown = ref(0)
const errorMessage = ref('')
const showSuccessMessage = ref(false)

// 计算属性
const isPasswordValid = computed(() => {
  return Object.values(passwordChecks).every(check => check)
})

const isPasswordMatch = computed(() => {
  return formData.newPassword === formData.confirmPassword && formData.newPassword.length > 0
})

const isFormValid = computed(() => {
  return isContactValid.value && 
         isPasswordValid.value &&
         isPasswordMatch.value && 
         formData.verificationCode.length > 0
})

// 联系方式验证（手机号或邮箱）
const validateContact = () => {
  const mobileRegex = /^1[3-9]\d{9}$/
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  isContactValid.value = mobileRegex.test(formData.contactInfo) || emailRegex.test(formData.contactInfo)
}

// 判断联系方式类型
const getContactType = (contactInfo) => {
  const mobileRegex = /^1[3-9]\d{9}$/
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  
  if (mobileRegex.test(contactInfo)) {
    return 'mobile'
  } else if (emailRegex.test(contactInfo)) {
    return 'email'
  } else {
    return null
  }
}

// 密码验证
const validatePassword = () => {
  const requirements = {
    length: /^.{6,}$/,
    uppercase: /[A-Z]/,
    lowercase: /[a-z]/,
    number: /[0-9]/,
    special: /[!@#$%^&*(),.?":{}|<>_+=\-\[\]\\;'`~]/
  }

  passwordChecks.length = requirements.length.test(formData.newPassword)
  passwordChecks.uppercase = requirements.uppercase.test(formData.newPassword)
  passwordChecks.lowercase = requirements.lowercase.test(formData.newPassword)
  passwordChecks.number = requirements.number.test(formData.newPassword)
  passwordChecks.special = requirements.special.test(formData.newPassword)
}

// 表单验证
const validateForm = () => {
  // 表单验证逻辑在computed属性中已经实现
}

// 获取验证码
const getVerificationCode = async () => {
  if (!isContactValid.value || countdown.value > 0) {
    return
  }

  const contactType = getContactType(formData.contactInfo)
  
  if (!contactType) {
    showError(t('reset.invalid-contact'))
    return
  }

  try {
    // 启动倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    // 调用找回密码验证码API
    const response = await getFindPwdCode(formData.contactInfo)
    
    if (response.rescode === '200') {
      showSuccess(t('reset.code-sent'))
    } else {
      countdown.value = 0
      clearInterval(timer)
      showError(response.notice || t('reset.code-send-failed'))
    }
  } catch (error) {
    countdown.value = 0
    showError(t('reset.network-error'))
    console.error('验证码错误:', error)
  }
}

// 提交表单
const submitForm = async () => {
  if (!isFormValid.value) {
    return
  }

  const contactType = getContactType(formData.contactInfo)
  
  if (!contactType) {
    showError(t('reset.invalid-contact'))
    return
  }

  if (!formData.verificationCode) {
    showError(t('reset.enter-code'))
    return
  }

  if (!isPasswordValid.value) {
    showError(t('reset.password-invalid'))
    return
  }

  if (!isPasswordMatch.value) {
    showError(t('reset.password-not-match'))
    return
  }

  try {
    // 调用修改密码API
    const response = await updatePassword({
      mobile: formData.contactInfo,
      password: formData.newPassword,
      vcode: formData.verificationCode
    })
    
    if (response.notice === "密码修改成功！请重新登录！" || response.success) {
      showSuccessMessage.value = true
      setTimeout(() => {
        router.push('/login')
      }, 2000)
    } else {
      showError(response.notice || t('reset.reset-failed'))
    }
  } catch (error) {
    console.error('重置密码错误:', error)
    showError(error.response || t('reset.reset-failed'))
  }
}

// 显示错误信息
const showError = (message) => {
  errorMessage.value = message
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

// 显示成功信息
const showSuccess = (message) => {
  // 成功信息通过showSuccessMessage控制显示
  console.log(message)
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}

// 生命周期
onMounted(() => {
  // 页面初始化逻辑
})
</script>

<style scoped>
/* Reset Password Page Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.reset-password-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* 语言切换按钮样式 */
.language-switcher {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 1000;
}

.language-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.language-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.language-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border-color: rgba(255, 255, 255, 0.9);
}

.reset-password-container {
  background: #ffffff;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  margin: 1rem;
  overflow-y: auto;
  max-height: calc(100vh - 2rem);
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.reset-password-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.reset-password-header {
  text-align: center;
  margin-bottom: 2rem;
}

.reset-password-header h1 {
  color: #333;
  font-size: 1.875rem;
  margin-bottom: 0.5rem;
}

.reset-password-header p {
  color: #666;
  font-size: 0.875rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 500;
  text-align: left;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input.valid {
  border-color: #27ae60;
}

.form-group input.invalid {
  border-color: #e74c3c;
}

.input-with-button {
  display: flex;
  gap: 0.5rem;
}

.input-with-button input {
  flex: 1;
}

.verification-btn {
  white-space: nowrap;
  padding: 0 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: auto;
  align-self: stretch;
}

.verification-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.verification-btn:active {
  transform: translateY(0);
}

.verification-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.password-requirements {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.requirement {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}

.requirement.neutral .requirement-icon {
  background: #bdc3c7;
  color: white;
}

.requirement-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

.requirement.valid .requirement-icon {
  background: #27ae60;
  color: white;
}

.requirement.invalid .requirement-icon {
  background: #e74c3c;
  color: white;
}

.reset-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1rem;
}

.reset-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.reset-btn:active {
  transform: translateY(0);
}

.reset-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.back-btn {
  width: 100%;
  padding: 0.75rem;
  background: #ffffff;
  color: #667eea;
  border: 2px solid #667eea;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.back-btn:active {
  transform: translateY(0);
}

.error-message, .success-message {
  font-size: 0.875rem;
  margin-top: 0.5rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.error-message {
  background: #fdf2f2;
  color: #e74c3c;
  border: 1px solid #fbb6ce;
}

.success-message {
  background: #f0fff4;
  color: #27ae60;
  border: 1px solid #9ae6b4;
  text-align: center;
}

.auth-divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.auth-divider span {
  background: #ffffff;
  padding: 0 1rem;
  color: #666;
  font-size: 0.875rem;
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .language-switcher {
    top: 0.5rem;
    right: 0.5rem;
  }
  
  .language-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .reset-password-container {
    margin: 0.5rem;
    padding: 1.5rem;
  }
  
  .reset-password-header h1 {
    font-size: 1.5rem;
  }
}
</style> 