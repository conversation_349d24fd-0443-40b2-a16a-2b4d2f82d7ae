<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="page-title">Reset Password - Molecular Design Platform</title>
    <link rel="icon" type="image/png" href="/src/images/icon.png">
    <link rel="stylesheet" href="/src/styles/pages/reset_password.css">
</head>
<body>
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
        <button class="language-btn" data-lang="zh" onclick="switchLanguage('zh')">
            🇨🇳 中文
        </button>
        <button class="language-btn active" data-lang="en" onclick="switchLanguage('en')">
            🇺🇸 EN
        </button>
    </div>

    <div class="reset-container">
        <div class="reset-header">
            <h1 data-i18n="reset-title">Reset Password</h1>
            <p data-i18n="reset-subtitle">Please fill in the information to reset your password</p>
        </div>
        
        <form id="resetPasswordForm">
            <div id="resetContent">
                <div class="form-group">
                    <label for="contactInfo" data-i18n="contact-info-label">Mobile/Email</label>
                    <div style="display: flex;">
                        <input type="text" id="contactInfo" name="contactInfo" required data-i18n-placeholder="contact-info-placeholder" placeholder="Enter mobile number/email" style="flex: 1; margin-right: 10px;">
                        <button type="button" id="sendCodeBtn" class="code-btn" data-i18n="send-code-btn">Get Code</button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="verificationCode" data-i18n="verification-code-label">Verification Code</label>
                    <input type="text" id="verificationCode" name="verificationCode" required data-i18n-placeholder="verification-code-placeholder" placeholder="Enter verification code">
                </div>

                <div class="form-group">
                    <label for="password" data-i18n="new-password-label">New Password</label>
                    <input type="password" id="password" name="password" required data-i18n-placeholder="new-password-placeholder" placeholder="Enter new password">
                    <div class="password-requirements">
                        <div class="requirement neutral" id="req-length">
                            <div class="requirement-icon">?</div>
                            <span data-i18n="req-length">At least 6 characters</span>
                        </div>
                        <div class="requirement neutral" id="req-uppercase">
                            <div class="requirement-icon">?</div>
                            <span data-i18n="req-uppercase">Contains uppercase letter</span>
                        </div>
                        <div class="requirement neutral" id="req-lowercase">
                            <div class="requirement-icon">?</div>
                            <span data-i18n="req-lowercase">Contains lowercase letter</span>
                        </div>
                        <div class="requirement neutral" id="req-number">
                            <div class="requirement-icon">?</div>
                            <span data-i18n="req-number">Contains number</span>
                        </div>
                        <div class="requirement neutral" id="req-special">
                            <div class="requirement-icon">?</div>
                            <span data-i18n="req-special">Contains special character (!@#$%^&* etc.)</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword" data-i18n="confirm-new-password-label">Confirm New Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required data-i18n-placeholder="confirm-new-password-placeholder" placeholder="Enter new password again">
                </div>

                <button type="button" class="reset-btn" id="resetPasswordBtn" disabled data-i18n="reset-password-btn">Reset Password</button>
                <button type="button" class="back-btn" onclick="goToLogin()" data-i18n="back-to-login">Back to Login</button>
            </div>

            <!-- 成功消息 -->
            <div class="success-message" id="successMessage" style="display: none;">
                <p data-i18n="reset-success">Your password has been reset successfully! Redirecting to login page...</p>
            </div>

            <div class="error-message" id="errorMessage"></div>
        </form>
    </div>

    <script>
        // 多语言文本配置
        const translations = {
            zh: {
                'page-title': '找回密码 - 分子设计平台',
                'reset-title': '找回密码',
                'reset-subtitle': '请填写信息重置您的密码',
                'contact-info-label': '手机号码/邮箱',
                'contact-info-placeholder': '请输入手机号码/邮箱',
                'send-code-btn': '获取验证码',
                'verification-code-label': '验证码',
                'verification-code-placeholder': '请输入验证码',
                'new-password-label': '新密码',
                'new-password-placeholder': '请输入新密码',
                'confirm-new-password-label': '确认新密码',
                'confirm-new-password-placeholder': '请再次输入新密码',
                'req-length': '至少6位字符',
                'req-uppercase': '包含大写字母',
                'req-lowercase': '包含小写字母',
                'req-number': '包含数字',
                'req-special': '包含特殊符号(!@#$%^&*等)',
                'reset-password-btn': '重置密码',
                'back-to-login': '返回登录',
                'reset-success': '您的密码已重置成功！即将跳转到登录页面...',
                'send-code-retry': '秒后重试',
                'invalid-contact': '请输入有效的手机号码或邮箱',
                'code-sent': '验证码已发送',
                'code-send-failed': '验证码发送失败',
                'network-error': '网络错误，请检查连接后重试',
                'enter-code': '请输入验证码',
                'password-invalid': '密码不符合要求',
                'password-not-match': '两次输入的密码不一致',
                'reset-failed': '密码重置失败'
            },
            en: {
                'page-title': 'Reset Password - Molecular Design Platform',
                'reset-title': 'Reset Password',
                'reset-subtitle': 'Please fill in the information to reset your password',
                'contact-info-label': 'Mobile/Email',
                'contact-info-placeholder': 'Enter mobile number/email',
                'send-code-btn': 'Get Code',
                'verification-code-label': 'Verification Code',
                'verification-code-placeholder': 'Enter verification code',
                'new-password-label': 'New Password',
                'new-password-placeholder': 'Enter new password',
                'confirm-new-password-label': 'Confirm New Password',
                'confirm-new-password-placeholder': 'Enter new password again',
                'req-length': 'At least 6 characters',
                'req-uppercase': 'Contains uppercase letter',
                'req-lowercase': 'Contains lowercase letter',
                'req-number': 'Contains number',
                'req-special': 'Contains special character (!@#$%^&* etc.)',
                'reset-password-btn': 'Reset Password',
                'back-to-login': 'Back to Login',
                'reset-success': 'Your password has been reset successfully! Redirecting to login page...',
                'send-code-retry': 'seconds to retry',
                'invalid-contact': 'Please enter a valid mobile number or email',
                'code-sent': 'Verification code sent',
                'code-send-failed': 'Failed to send verification code',
                'network-error': 'Network error, please check connection and try again',
                'enter-code': 'Please enter verification code',
                'password-invalid': 'Password does not meet requirements',
                'password-not-match': 'Passwords do not match',
                'reset-failed': 'Password reset failed'
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.lang === lang);
            });

            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';

            // 更新页面文本
            updatePageText(lang);
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];

            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t && t[key]) {
                    if (element.tagName === 'TITLE') {
                        element.textContent = t[key];
                    } else {
                        element.textContent = t[key];
                    }
                }
            });

            // 更新placeholder属性
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (t && t[key]) {
                    element.placeholder = t[key];
                }
            });

            // 更新验证码按钮文本（如果正在倒计时）
            if (sendCodeBtn.disabled && countdown > 0) {
                sendCodeBtn.textContent = `${countdown}${t['send-code-retry']}`;
            }
        }

        // DOM元素
        const contactInfoInput = document.getElementById('contactInfo');
        const verificationCodeInput = document.getElementById('verificationCode');
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const sendCodeBtn = document.getElementById('sendCodeBtn');
        const resetPasswordBtn = document.getElementById('resetPasswordBtn');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const resetContent = document.getElementById('resetContent');
        
        // 验证码计时器
        let countdown = 60;
        let timer = null;
        
        // 联系方式验证（手机号或邮箱）
        function validateContactInfo(contactInfo) {
            const mobileRegex = /^1[3-9]\d{9}$/;
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return mobileRegex.test(contactInfo) || emailRegex.test(contactInfo);
        }
        
        // 判断联系方式类型
        function getContactType(contactInfo) {
            const mobileRegex = /^1[3-9]\d{9}$/;
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            
            if (mobileRegex.test(contactInfo)) {
                return 'mobile';
            } else if (emailRegex.test(contactInfo)) {
                return 'email';
            } else {
                return null;
            }
        }
        
        // 密码要求检查
        const requirements = {
            length: /^.{6,}$/,
            uppercase: /[A-Z]/,
            lowercase: /[a-z]/,
            number: /[0-9]/,
            special: /[!@#$%^&*(),.?":{}|<>_+=\-\[\]\\;'`~]/
        };
        
        // 检查密码要求
        function checkPasswordRequirements(password) {
            const checks = {
                length: requirements.length.test(password),
                uppercase: requirements.uppercase.test(password),
                lowercase: requirements.lowercase.test(password),
                number: requirements.number.test(password),
                special: requirements.special.test(password)
            };

            // 更新UI
            Object.keys(checks).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                const icon = element.querySelector('.requirement-icon');
                
                if (checks[req]) {
                    element.className = 'requirement valid';
                    icon.textContent = '✓';
                } else {
                    element.className = 'requirement invalid';
                    icon.textContent = '✗';
                }
            });

            return Object.values(checks).every(check => check);
        }
        
        // 表单验证
        function validatePasswordForm() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            const verificationCode = verificationCodeInput.value.trim();
            
            const isPasswordValid = checkPasswordRequirements(password);
            const isPasswordMatch = password === confirmPassword && password.length > 0;
            const isCodeValid = verificationCode.length > 0;
            
            // 更新密码输入框样式
            if (password.length > 0) {
                passwordInput.className = isPasswordValid ? 'valid' : 'invalid';
            } else {
                passwordInput.className = '';
            }

            // 更新确认密码输入框样式
            if (confirmPassword.length > 0) {
                confirmPasswordInput.className = isPasswordMatch ? 'valid' : 'invalid';
            } else {
                confirmPasswordInput.className = '';
            }
            
            // 更新验证码输入框样式
            if (verificationCode.length > 0) {
                verificationCodeInput.className = 'valid';
            } else {
                verificationCodeInput.className = '';
            }
            
            // 启用/禁用重置密码按钮
            resetPasswordBtn.disabled = !(isPasswordValid && isPasswordMatch && isCodeValid);
        }
        
        // 发送验证码
        sendCodeBtn.addEventListener('click', async function() {
            const contactInfo = contactInfoInput.value.trim();
            const contactType = getContactType(contactInfo);
            
            // 验证联系方式
            if (!validateContactInfo(contactInfo)) {
                showError(translations[currentLanguage]['invalid-contact']);
                return;
            }
            
            try {
                // 发送验证码请求
                const response = await fetch('/api/findpwd', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        contactInfo: contactInfo,
                        method: contactType
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    if (data.rescode === '200') {
                        showSuccess(translations[currentLanguage]['code-sent']);
                        startCountdown();
                    } else {
                        showError(data.notice || translations[currentLanguage]['code-send-failed']);
                    }
                } else {
                    showError(data.message || translations[currentLanguage]['code-send-failed']);
                }
            } catch (error) {
                console.error('发送验证码错误:', error);
                showError(translations[currentLanguage]['network-error']);
            }
        });
        
        // 验证码倒计时
        function startCountdown() {
            sendCodeBtn.disabled = true;
            countdown = 60;

            sendCodeBtn.textContent = `${countdown}${translations[currentLanguage]['send-code-retry']}`;

            timer = setInterval(() => {
                countdown--;
                sendCodeBtn.textContent = `${countdown}${translations[currentLanguage]['send-code-retry']}`;

                if (countdown <= 0) {
                    clearInterval(timer);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = translations[currentLanguage]['send-code-btn'];
                }
            }, 1000);
        }
        
        // 重置密码
        resetPasswordBtn.addEventListener('click', async function() {
            const contactInfo = contactInfoInput.value.trim();
            const contactType = getContactType(contactInfo);
            const verificationCode = verificationCodeInput.value.trim();
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            if (!validateContactInfo(contactInfo)) {
                showError(translations[currentLanguage]['invalid-contact']);
                return;
            }

            if (!verificationCode) {
                showError(translations[currentLanguage]['enter-code']);
                return;
            }

            if (!checkPasswordRequirements(password)) {
                showError(translations[currentLanguage]['password-invalid']);
                return;
            }

            if (password !== confirmPassword) {
                showError(translations[currentLanguage]['password-not-match']);
                return;
            }
            
            try {
                // 发送重置密码请求
                const response = await fetch('/api/reset_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contactInfo: contactInfo,
                        method: contactType,
                        password: password,
                        verificationCode: verificationCode
                    })
                });
                
                const data = await response.json();
                console.log(data)
                if (data.success) {
                    showSuccess(data.message)
                    // 显示成功消息
                    resetContent.style.display = 'none';
                    successMessage.style.display = 'block';
                    
                    // 两秒后跳转到登录页
                    setTimeout(() => {
                        window.location.href = 'login';
                    }, 2000);
                } else {
                    showError(data.message || translations[currentLanguage]['reset-failed']);
                }
            } catch (error) {
                console.error('密码重置错误:', error);
                showError(translations[currentLanguage]['network-error']);
            }
        });
        
        // 显示错误信息
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        // 显示成功信息
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 3000);
        }
        
        // 跳转到登录页面
        function goToLogin() {
            window.location.href = 'login';
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化语言
            switchLanguage(currentLanguage);

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });
        });

        // 事件监听器
        passwordInput.addEventListener('input', validatePasswordForm);
        confirmPasswordInput.addEventListener('input', validatePasswordForm);
        verificationCodeInput.addEventListener('input', validatePasswordForm);
    </script>
</body>
</html> 