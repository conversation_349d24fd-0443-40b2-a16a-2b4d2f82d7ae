// SMILES 批量处理 Composable
import { ref, computed } from 'vue'

export function useSmilesProcessor() {
  // 状态管理
  const parsedMolecules = ref([])
  const selectedMolecule = ref(null)
  const isProcessing = ref(false)
  const processingError = ref('')
  
  // 计算属性
  const moleculeCount = computed(() => parsedMolecules.value.length)
  
  // 处理 SMILES 文本输入
  const processSmilesText = (smilesText) => {
    if (!smilesText || !smilesText.trim()) {
      return { success: false, message: '请输入 SMILES 字符串' }
    }
    
    try {
      isProcessing.value = true
      processingError.value = ''
      
      // 解析 SMILES 输入（支持多行和逗号分隔）
      const smilesLines = smilesText
        .split(/[\n,]/)
        .map(line => line.trim())
        .filter(line => line.length > 0)
      
      if (smilesLines.length === 0) {
        throw new Error('未找到有效的 SMILES 字符串')
      }
      
      // 创建分子对象
      const newMolecules = smilesLines.map((smiles, index) => ({
        id: `smiles_text_${Date.now()}_${index}`,
        name: `Molecule_${parsedMolecules.value.length + index + 1}`,
        smiles: smiles,
        type: 'smiles',
        source: 'text_input',
        created: new Date().toISOString()
      }))
      
      // 验证 SMILES 格式
      const validMolecules = newMolecules.filter(mol => validateSmiles(mol.smiles))
      
      if (validMolecules.length === 0) {
        throw new Error('未找到有效的 SMILES 格式')
      }
      
      // 添加到分子列表
      parsedMolecules.value.push(...validMolecules)
      
      return { 
        success: true, 
        message: `成功添加 ${validMolecules.length} 个 SMILES 分子`,
        count: validMolecules.length,
        molecules: validMolecules
      }
    } catch (error) {
      processingError.value = error.message
      return { success: false, message: error.message }
    } finally {
      isProcessing.value = false
    }
  }
  
  // 处理 CSV 文件
  const processCsvFile = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          isProcessing.value = true
          processingError.value = ''
          
          const csvContent = e.target.result
          const result = parseCsvContent(csvContent)
          
          if (result.success) {
            parsedMolecules.value.push(...result.molecules)
          }
          
          resolve(result)
        } catch (error) {
          processingError.value = error.message
          reject(error)
        } finally {
          isProcessing.value = false
        }
      }
      
      reader.onerror = () => {
        isProcessing.value = false
        const error = new Error('文件读取失败')
        processingError.value = error.message
        reject(error)
      }
      
      reader.readAsText(file)
    })
  }
  
  // 解析 CSV 内容
  const parseCsvContent = (csvContent) => {
    try {
      const lines = csvContent.split('\n').filter(line => line.trim())
      
      if (lines.length === 0) {
        throw new Error('CSV 文件为空')
      }
      
      // 检测分隔符
      const firstLine = lines[0]
      const separators = [',', '\t', ';', '|']
      let separator = ','
      
      for (const sep of separators) {
        if (firstLine.includes(sep)) {
          separator = sep
          break
        }
      }
      
      // 解析数据
      const molecules = []
      let hasHeader = false
      
      // 检测是否有标题行
      const firstRowCells = lines[0].split(separator)
      if (firstRowCells.some(cell => 
        cell.toLowerCase().includes('smiles') || 
        cell.toLowerCase().includes('name') ||
        cell.toLowerCase().includes('id')
      )) {
        hasHeader = true
      }
      
      const dataLines = hasHeader ? lines.slice(1) : lines
      
      dataLines.forEach((line, index) => {
        const cells = line.split(separator).map(cell => cell.trim().replace(/['"]/g, ''))
        
        if (cells.length > 0 && cells[0]) {
          // 假设第一列是 SMILES，第二列是名称（如果存在）
          const smiles = cells[0]
          const name = cells[1] || `CSV_Molecule_${index + 1}`
          
          // 验证 SMILES 格式
          if (validateSmiles(smiles)) {
            molecules.push({
              id: `csv_smiles_${Date.now()}_${index}`,
              name: name,
              smiles: smiles,
              type: 'smiles',
              source: 'csv_file',
              created: new Date().toISOString()
            })
          }
        }
      })
      
      if (molecules.length === 0) {
        throw new Error('未找到有效的 SMILES 数据')
      }
      
      return {
        success: true,
        message: `从 CSV 文件中成功导入 ${molecules.length} 个 SMILES 分子`,
        count: molecules.length,
        molecules: molecules
      }
    } catch (error) {
      throw new Error('解析 CSV 文件失败: ' + error.message)
    }
  }
  
  // 验证 SMILES 格式
  const validateSmiles = (smiles) => {
    if (!smiles || typeof smiles !== 'string') return false
    
    // 基本的 SMILES 格式验证
    // 允许的字符：字母、数字、括号、方括号、等号、井号、加号、减号、@、/、\、点
    const smilesPattern = /^[A-Za-z0-9\[\]()=#+\-@\/\\\.]+$/
    
    return smiles.length > 0 && 
           smiles.length <= 1000 && // 合理的长度限制
           smilesPattern.test(smiles)
  }
  
  // 选择分子
  const selectMolecule = (moleculeId) => {
    const molecule = parsedMolecules.value.find(m => m.id === moleculeId)
    if (molecule) {
      selectedMolecule.value = molecule
      return molecule
    }
    return null
  }
  
  // 删除分子
  const deleteMolecule = (moleculeId) => {
    const index = parsedMolecules.value.findIndex(m => m.id === moleculeId)
    if (index !== -1) {
      const deletedMolecule = parsedMolecules.value.splice(index, 1)[0]
      
      // 如果删除的是当前选中的分子，清除选择
      if (selectedMolecule.value && selectedMolecule.value.id === moleculeId) {
        selectedMolecule.value = null
      }
      
      return deletedMolecule
    }
    return null
  }
  
  // 清空所有分子
  const clearAllMolecules = () => {
    parsedMolecules.value = []
    selectedMolecule.value = null
    processingError.value = ''
  }
  
  // 搜索分子
  const searchMolecules = (searchTerm) => {
    if (!searchTerm || !searchTerm.trim()) {
      return parsedMolecules.value
    }
    
    const term = searchTerm.toLowerCase()
    return parsedMolecules.value.filter(molecule => 
      molecule.name.toLowerCase().includes(term) ||
      molecule.smiles.toLowerCase().includes(term)
    )
  }
  
  // 获取分子统计信息
  const getMoleculeStats = () => {
    const sources = {}
    parsedMolecules.value.forEach(mol => {
      sources[mol.source] = (sources[mol.source] || 0) + 1
    })
    
    return {
      total: parsedMolecules.value.length,
      sources: sources,
      hasSelection: !!selectedMolecule.value
    }
  }
  
  // 导出分子数据
  const exportMolecules = (format = 'csv') => {
    if (parsedMolecules.value.length === 0) {
      throw new Error('没有可导出的分子数据')
    }
    
    if (format === 'csv') {
      const headers = ['Name', 'SMILES', 'Source', 'Created']
      const rows = parsedMolecules.value.map(mol => [
        mol.name,
        mol.smiles,
        mol.source,
        mol.created
      ])
      
      const csvContent = [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n')
      
      return {
        content: csvContent,
        filename: `smiles_molecules_${new Date().toISOString().split('T')[0]}.csv`,
        mimeType: 'text/csv'
      }
    } else if (format === 'smi') {
      // SMILES 格式：每行一个 SMILES，后跟名称
      const smiContent = parsedMolecules.value
        .map(mol => `${mol.smiles}\t${mol.name}`)
        .join('\n')
      
      return {
        content: smiContent,
        filename: `molecules_${new Date().toISOString().split('T')[0]}.smi`,
        mimeType: 'text/plain'
      }
    }
    
    throw new Error('不支持的导出格式')
  }
  
  // 批量重命名分子
  const batchRenameMolecules = (prefix = 'Molecule') => {
    parsedMolecules.value.forEach((mol, index) => {
      mol.name = `${prefix}_${index + 1}`
    })
  }
  
  return {
    // 状态
    parsedMolecules,
    selectedMolecule,
    isProcessing,
    processingError,
    
    // 计算属性
    moleculeCount,
    
    // 方法
    processSmilesText,
    processCsvFile,
    parseCsvContent,
    validateSmiles,
    selectMolecule,
    deleteMolecule,
    clearAllMolecules,
    searchMolecules,
    getMoleculeStats,
    exportMolecules,
    batchRenameMolecules
  }
}
