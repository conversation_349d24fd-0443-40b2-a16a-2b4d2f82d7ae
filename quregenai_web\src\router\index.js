import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue')
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: () => import('../views/ResetPassword.vue')
  },
  {
    path: '/terms-of-service',
    name: 'TermsOfService',
    component: () => import('../views/TermsOfService.vue')
  },
  {
    path: '/autodock',
    name: 'AutoDock',
    component: () => import('../views/AutoDock.vue')
  },
  // {
  //   path: '/diffdock',
  //   name: 'DiffDock',
  //   component: () => import('../views/DiffDock.vue')
  // },
  // {
  //   path: '/protenix',
  //   name: 'Protenix',
  //   component: () => import('../views/Protenix.vue')
  // },
  // {
  //   path: '/quantum_tasks',
  //   name: 'QuantumTasks',
  //   component: () => import('../views/QuantumTasks.vue')
  // },
  // {
  //   path: '/molmap',
  //   name: 'MolMap',
  //   component: () => import('../views/MolMap.vue')
  // },
  // {
  //   path: '/pocketvina',
  //   name: 'PocketVina',
  //   component: () => import('../views/PocketVina.vue')
  // },
  // {
  //   path: '/raman',
  //   name: 'Raman',
  //   component: () => import('../views/Raman.vue')
  // },
  // {
  //   path: '/userprofile',
  //   name: 'UserProfile',
  //   component: () => import('../views/UserProfile.vue')
  // }
]

const router = createRouter({
  history: createWebHistory(''), 
  routes
})

export default router 