/* Register Page Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.register-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Language Switcher */
.language-switcher {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 1000;
}

.language-btn {
  padding: 8px 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.language-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.language-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-color: white;
}

.register-container {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  margin: 1rem;
  overflow-y: auto;
  max-height: calc(100vh - 2rem);
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.register-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.register-header {
  text-align: center;
  margin-bottom: 2rem;
}

.register-header h1 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.register-header p {
  color: #666;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 500;
  text-align: left;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input.valid {
  border-color: #27ae60;
}

.form-group input.invalid {
  border-color: #e74c3c;
}

.input-with-button {
  display: flex;
  gap: 0.5rem;
  align-items: stretch;
}

.input-with-button input {
  flex: 1;
}

.input-with-captcha {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.input-with-captcha input {
  flex: 1;
  margin-right: 10px;
}

#captcha-element {
  display: inline-block;
}

.code-btn, .verification-btn {
  white-space: nowrap;
  padding: 0 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: auto;
  align-self: stretch;
}

.code-btn:hover:not(:disabled),
.verification-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.code-btn:active,
.verification-btn:active {
  transform: translateY(0);
}

.code-btn:disabled,
.verification-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.password-requirements {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.requirement {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}

.requirement-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

.requirement.valid .requirement-icon {
  background: #27ae60;
  color: white;
}

.requirement.invalid .requirement-icon {
  background: #e74c3c;
  color: white;
}

.requirement.neutral .requirement-icon {
  background: #bdc3c7;
  color: white;
}

/* Agreement Container */
.agreement-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 0.5rem;
}

.agreement-checkbox {
  margin-top: 0.2rem;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.agreement-text {
  font-size: 0.875rem;
  color: #666;
  line-height: 1.4;
  cursor: pointer;
}

.agreement-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.agreement-link:hover {
  text-decoration: underline;
}

.register-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1rem;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.register-btn:active {
  transform: translateY(0);
}

.register-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.back-btn {
  width: 100%;
  padding: 0.75rem;
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.back-btn:active {
  transform: translateY(0);
}

.error-message, .success-message {
  font-size: 0.875rem;
  margin-top: 0.5rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.error-message {
  background: #fdf2f2;
  color: #e74c3c;
  border: 1px solid #fbb6ce;
}

.success-message {
  background: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
}

.auth-divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.auth-divider span {
  background: white;
  padding: 0 1rem;
  color: #666;
  font-size: 0.875rem;
  position: relative;
  z-index: 1;
}