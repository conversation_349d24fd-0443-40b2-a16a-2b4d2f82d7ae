{% extends "layout_with_nav.html" %}

{% block title %}Raman任务详情 - QureGenAI 药物设计平台{% endblock %}

{% block page_css %}
<script src="/src/scripts/chart.js"></script>
<link rel="stylesheet" href="/src/styles/pages/raman_detail.css">
{% endblock %}

{% block content %}
<!-- 添加返回按钮到页面内容中 -->
<div style="margin-bottom: 1rem;">
    <a href="javascript:history.back()" class="back-btn">
        ← 返回
    </a>
</div>
        <div class="page-header">
            <h1 class="page-title">
                <span class="page-icon">⚡</span>
                <span>Raman任务详情</span>
            </h1>
            <div class="detection-id" id="detectionId">检测ID: 加载中...</div>
        </div>

        <div class="task-info">
            <h3>📋 任务基本信息</h3>
            <div class="info-grid" id="basicInfo">
                <div class="loading">加载中...</div>
            </div>
        </div>

        <div class="task-info">
            <h3>🧪 样本信息</h3>
            <div class="info-grid" id="sampleInfo">
                <div class="loading">加载中...</div>
            </div>
        </div>

        <div class="task-info">
            <h3>⚙️ 检测参数</h3>
            <div class="info-grid" id="specParameter">
                <div class="loading">加载中...</div>
            </div>
        </div>

        <div class="spectrum-chart">
            <h3>📊 Raman光谱图</h3>
            <div class="chart-container">
                <canvas id="spectrumChart"></canvas>
            </div>
        </div>
{% endblock %}

{% block page_js %}
<script>
        let spectrumChart = null;

        // 页面加载时获取任务详情
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const detectionId = urlParams.get('task_id');
            
            if (detectionId) {
                document.getElementById('detectionId').textContent = `检测ID: ${detectionId}`;
                loadTaskDetail(detectionId);
            } else {
                showError('未找到任务ID参数');
            }
        });

        // 加载任务详情
        async function loadTaskDetail(detectionId) {
            try {
                const response = await fetch(`/api/raman-ai/tasks/${detectionId}/detail`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.success) {
                    displayTaskDetail(result.task_detail);
                } else {
                    showError(result.message || '获取任务详情失败');
                }
            } catch (error) {
                console.error('加载任务详情失败:', error);
                showError('加载任务详情失败，请稍后重试');
            }
        }

        // 显示任务详情
        function displayTaskDetail(taskDetail) {
            const rawData = taskDetail.raw_data;
            
            // 显示基本信息
            displayBasicInfo(taskDetail);
            
            // 显示样本信息
            displaySampleInfo(rawData.SampleInfo || {});
            
            // 显示检测参数
            displaySpecParameter(rawData.SpecParameter || {});
            
            // 绘制光谱图
            if (rawData.Spectrum) {
                drawSpectrumChart(rawData.Spectrum);
            }
        }

        // 显示基本信息
        function displayBasicInfo(taskDetail) {
            const basicInfo = document.getElementById('basicInfo');
            basicInfo.innerHTML = `
                <div class="info-section">
                    <h4>🔍 检测信息</h4>
                    <div class="info-item">
                        <span class="info-label">检测ID:</span>
                        <span class="info-value">${taskDetail.detection_id}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">用户ID:</span>
                        <span class="info-value">${taskDetail.user_id}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">机器ID:</span>
                        <span class="info-value">${taskDetail.machine_id}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">检测时间:</span>
                        <span class="info-value">${taskDetail.detection_time}</span>
                    </div>
                </div>
                <div class="info-section">
                    <h4>📅 时间信息</h4>
                    <div class="info-item">
                        <span class="info-label">检测时间:</span>
                        <span class="info-value">${taskDetail.raw_data.DetectionTime || '未知'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">更新时间:</span>
                        <span class="info-value">${taskDetail.raw_data.UpdateTime || '未知'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">模型代号:</span>
                        <span class="info-value">${taskDetail.raw_data.ProductModel || '未知'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">设备ID:</span>
                        <span class="info-value">${taskDetail.raw_data.SpectrometerID || taskDetail.raw_data.SpecID || '未知'}</span>
                    </div>
                </div>
            `;
        }

        // 显示样本信息
        function displaySampleInfo(sampleInfo) {
            const sampleInfoDiv = document.getElementById('sampleInfo');
            sampleInfoDiv.innerHTML = `
                <div class="info-section">
                    <h4>🧪 样本属性</h4>
                    <div class="info-item">
                        <span class="info-label">样本名称:</span>
                        <span class="info-value">${sampleInfo.name || '未知'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">CAS号:</span>
                        <span class="info-value">${sampleInfo.CAS || '未知'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">物理状态:</span>
                        <span class="info-value">${sampleInfo.state || '未知'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">容器类型:</span>
                        <span class="info-value">${sampleInfo.container || '未知'}</span>
                    </div>
                </div>
                <div class="info-section">
                    <h4>🏭 生产信息</h4>
                    <div class="info-item">
                        <span class="info-label">纯度:</span>
                        <span class="info-value">${sampleInfo.Purity || '未知'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">生产厂家:</span>
                        <span class="info-value">${sampleInfo.Manufacturer || '未知'}</span>
                    </div>
                </div>
            `;
        }

        // 显示检测参数
        function displaySpecParameter(specParameter) {
            const specParameterDiv = document.getElementById('specParameter');
            specParameterDiv.innerHTML = `
                <div class="info-section">
                    <h4>🔬 激光参数</h4>
                    <div class="info-item">
                        <span class="info-label">激光功率:</span>
                        <span class="info-value">${specParameter['LaserPower(mW)'] || '未知'} mW</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">累积时间:</span>
                        <span class="info-value">${specParameter['AccumulationTime(ms)'] || '未知'} ms</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">平均次数:</span>
                        <span class="info-value">${specParameter['Average'] || '未知'}</span>
                    </div>
                </div>
                <div class="info-section">
                    <h4>🌍 环境参数</h4>
                    <div class="info-item">
                        <span class="info-label">时间间隔:</span>
                        <span class="info-value">${specParameter['Interval(s)'] || '未知'} s</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">检测环境:</span>
                        <span class="info-value">${specParameter['DetectionEnv'] || '未知'}</span>
                    </div>
                </div>
            `;
        }

        // 修改 drawSpectrumChart 函数中的图表配置
        function drawSpectrumChart(spectrum) {
            const ctx = document.getElementById('spectrumChart').getContext('2d');
            
            // 销毁现有图表
            if (spectrumChart) {
                spectrumChart.destroy();
            }

            // 创建新图表
            spectrumChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: spectrum['RamanShift(cm-1)'] || spectrum['RamanShift'] || [],
                    datasets: [{
                        label: 'Raman强度',
                        data: spectrum.Intensity || [],
                        borderColor: '#5f9ea0', // 更深的蓝色
                        backgroundColor: 'rgba(95, 158, 160, 0.1)', // 对应的半透明背景
                        borderWidth: 1, // 更细的线条
                        fill: true,
                        tension: 0.4,
                        pointRadius: 1, // 更小的点
                        pointHoverRadius: 3 // 更小的悬停点
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Raman光谱图',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Raman位移 (cm⁻¹)',
                                font: {
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '强度 (a.u.)',
                                font: {
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('basicInfo').innerHTML = `<div class="error">${message}</div>`;
            document.getElementById('sampleInfo').innerHTML = `<div class="error">${message}</div>`;
            document.getElementById('specParameter').innerHTML = `<div class="error">${message}</div>`;
        }
    </script>
{% endblock %}