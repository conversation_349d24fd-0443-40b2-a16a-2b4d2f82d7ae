# QureGenAI 项目启动与配置指南

## 项目概述
QureGenAI 药物设计平台是基于 Vue3 + Vite 构建的现代化前端项目，提供药物设计、分子对接、量子计算等功能。

## 1. 环境准备

### 1.1 必需软件安装

#### Node.js 安装
- **版本要求**：Node.js 16.0+ (推荐 20.0+)
- **下载地址**：[https://nodejs.org/](https://nodejs.org/)
- **验证安装**：
```bash
node --version
npm --version
```

#### 包管理器选择
推荐使用以下任一包管理器：
- **npm**（Node.js 自带）
- **yarn**：`npm install -g yarn`
- **pnpm**：`npm install -g pnpm`

### 1.2 开发工具推荐
- **IDE**：Visual Studio Code
- **浏览器**：Chrome/Edge（支持 Vue DevTools）
- **Git**：版本控制工具

## 2. 项目安装与启动

### 2.1 克隆项目
```bash
git clone [项目地址]
cd quregenai_web
```

### 2.2 安装依赖
选择以下任一命令：

```bash
# 使用 npm
npm install

# 使用 yarn
yarn install

# 使用 pnpm
pnpm install
```

### 2.3 启动开发服务器
```bash
# 使用 npm
npm run dev

# 使用 yarn
yarn dev

# 使用 pnpm
pnpm dev
```

启动成功后，访问：[http://localhost:8080](http://localhost:8080)

### 2.4 启动带 CORS 支持的开发服务器
如果需要特殊的 CORS 配置：
```bash
# 使用 npm
npm run dev:cors

# 使用 yarn
yarn dev:cors

# 使用 pnpm
pnpm dev:cors
```

## 3. 项目构建与部署

### 3.1 生产环境构建
```bash
# 使用 npm
npm run build

# 使用 yarn
yarn build

# 使用 pnpm
pnpm build
```

构建完成后，生成的文件位于 `dist/` 目录。

### 3.2 预览构建结果
```bash
# 使用 npm
npm run preview

# 使用 yarn
yarn preview

# 使用 pnpm
pnpm preview
```

## 4. Vite 配置文件说明

### 4.1 配置文件位置
```
项目根目录/vite.config.js
```

### 4.2 当前配置解析
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],                    // Vue3 插件支持
  base: '',                           // 基础路径配置
  build: {
    outDir: 'dist'                    // 构建输出目录
  },
  server: {
    port: 8080,                       // 开发服务器端口
    cors: true,                       // 启用 CORS
    proxy: {                          // API 代理配置
      '/api/login': {
        target: 'http://*************',
        changeOrigin: true,
        secure: false,
        ws: true
      },
      '/api': {
        target: 'http://*************',
        changeOrigin: true,
        secure: false,
        ws: true
      }
    }
  }
})
```

## 5. 本地开发配置修改

### 5.1 修改开发服务器端口
```javascript
server: {
  port: 3000,  // 修改为你想要的端口号
  // 其他配置...
}
```

### 5.2 配置本地 API 代理

#### 场景1：连接本地后端服务
```javascript
proxy: {
  '/api/login': {
    target: 'http://127.0.0.1:8083',    // 本地登录服务
    changeOrigin: true,
    secure: false,
    ws: true
  },
  '/api': {
    target: 'http://127.0.0.1:33322',   // 本地主服务
    changeOrigin: true,
    secure: false,
    ws: true
  }
}
```

#### 场景2：连接远程测试环境
```javascript
proxy: {
  '/api/login': {
    target: 'http://test.example.com',   // 测试环境地址
    changeOrigin: true,
    secure: false,
    ws: true
  },
  '/api': {
    target: 'http://test.example.com',
    changeOrigin: true,
    secure: false,
    ws: true
  }
}
```

#### 场景3：HTTPS 环境配置
```javascript
proxy: {
  '/api': {
    target: 'https://api.example.com',
    changeOrigin: true,
    secure: true,                       // HTTPS 需要设置为 true
    ws: true,
    headers: {
      'Origin': 'https://api.example.com'
    }
  }
}
```

### 5.3 配置路径别名
```javascript
import { resolve } from 'path'

export default defineConfig({
  // 其他配置...
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@api': resolve(__dirname, 'src/api'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  }
})
```

### 5.4 配置环境变量
创建环境变量文件：

#### .env.development（开发环境）
```env
VITE_API_BASE_URL=http://127.0.0.1:33322
VITE_LOGIN_API_URL=http://127.0.0.1:8083
VITE_APP_TITLE=QureGenAI 开发环境
```

#### .env.production（生产环境）
```env
VITE_API_BASE_URL=http://*************
VITE_LOGIN_API_URL=http://*************
VITE_APP_TITLE=QureGenAI 药物设计平台
```

#### 在代码中使用环境变量
```javascript
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
const appTitle = import.meta.env.VITE_APP_TITLE
```

## 6. 常见配置场景

### 6.1 开发环境快速切换配置
```javascript
// vite.config.js
const isDev = process.env.NODE_ENV === 'development'

export default defineConfig({
  // 其他配置...
  server: {
    port: 8080,
    cors: true,
    proxy: {
      '/api/login': {
        target: isDev ? 'http://127.0.0.1:8083' : 'http://*************',
        changeOrigin: true,
        secure: false,
        ws: true
      },
      '/api': {
        target: isDev ? 'http://127.0.0.1:33322' : 'http://*************',
        changeOrigin: true,
        secure: false,
        ws: true
      }
    }
  }
})
```

### 6.2 多环境配置管理
```javascript
// config/environments.js
export const environments = {
  development: {
    apiUrl: 'http://127.0.0.1:33322',
    loginUrl: 'http://127.0.0.1:8083'
  },
  testing: {
    apiUrl: 'http://test.example.com',
    loginUrl: 'http://test.example.com'
  },
  production: {
    apiUrl: 'http://*************',
    loginUrl: 'http://*************'
  }
}

// vite.config.js 中使用
import { environments } from './config/environments.js'

const env = process.env.NODE_ENV || 'development'
const config = environments[env]

export default defineConfig({
  // 其他配置...
  server: {
    proxy: {
      '/api/login': {
        target: config.loginUrl,
        changeOrigin: true,
        secure: false,
        ws: true
      },
      '/api': {
        target: config.apiUrl,
        changeOrigin: true,
        secure: false,
        ws: true
      }
    }
  }
})
```

## 7. 故障排除

### 7.1 常见问题及解决方案

#### 端口被占用
```bash
# 查找占用端口的进程
netstat -ano | findstr :8080

# 杀死进程（Windows）
taskkill /PID [进程ID] /F

# 或者修改 vite.config.js 中的端口号
```

#### 依赖安装失败
```bash
# 清除缓存
npm cache clean --force

# 删除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install
```

#### API 请求跨域问题
1. 检查 `vite.config.js` 中的 proxy 配置
2. 确认后端服务是否正常运行
3. 检查网络连接和防火墙设置

#### 热更新不生效
```javascript
// vite.config.js
export default defineConfig({
  server: {
    hmr: {
      overlay: false  // 禁用错误覆盖层
    },
    watch: {
      usePolling: true  // 启用轮询模式
    }
  }
})
```

### 7.2 性能优化建议
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          elementPlus: ['element-plus']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
})
```

## 8. 开发工作流建议

### 8.1 日常开发流程
1. 拉取最新代码：`git pull`
2. 安装/更新依赖：`npm install`
3. 启动开发服务器：`npm run dev`
4. 开发完成后构建测试：`npm run build`
5. 提交代码前预览：`npm run preview`

### 8.2 团队协作建议
- 统一 Node.js 版本（建议使用 .nvmrc 文件）
- 统一包管理器（建议团队内统一使用 npm/yarn/pnpm）
- 定期更新依赖版本
- 遵循项目重构指南中的开发规范

---

**注意**：
1. 首次启动项目前，请确保已安装所有必需的软件和依赖
2. 修改配置文件后需要重启开发服务器
3. 生产环境部署前请务必进行构建测试
4. 如遇到问题，请参考故障排除章节或联系团队技术负责人