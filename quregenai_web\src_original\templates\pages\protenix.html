{% extends "layout_with_nav.html" %}

{% block title %}Protenix - QureGenAI 药物设计平台{% endblock %}

{% block inline_styles %}
<link rel="stylesheet" href="/src/styles/pages/protenix.css">
{% endblock %}

{% block content %}
            <div class="page-header">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%;">
                    <div style="flex: 1;">
                        <h1 class="page-title">
                            <span class="page-icon"><img src="/src/images/protenix-protein.svg" alt="Protenix"></span>
                            <span data-i18n="page-title">Protenix 分子结构预测</span>
                            <a href="https://mp.weixin.qq.com/s/J4yswdhwv3Vni0IhO9Bkuw" target="_blank" class="tutorial-btn">
                                <span>📚</span>
                                <span data-i18n="tutorial-btn">平台教程</span>
                            </a>
                        </h1>
                        <p class="page-subtitle" data-i18n="page-subtitle">Protenix是先进的蛋白质结构预测平台，能够从氨基酸序列或结构文件预测蛋白质的三维结构和相互作用。支持多种输入格式，提供高精度的结构预测服务。</p>
                    </div>
                </div>
            </div>

            <form id="protenixForm">
                <!-- Global参数区域 -->
                <section class="section">
                    <h2 class="section-title" data-i18n="global-params-title">
                        🌐 全局参数
                    </h2>
                    
                    <div class="form-group">
                        <label for="jobName" data-i18n="job-name-label">任务名</label>
                        <input type="text" id="jobName" name="jobName" data-i18n-placeholder="job-name-placeholder" placeholder="protenix_job_5c714d32">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label data-i18n="use-msa-label">Use msa <span class="info-tooltip" onclick="toggleTooltip(this)">?
                                <div class="tooltip-content" data-i18n="use-msa-tooltip">
                                    Enabling this option activates the MSA (Multiple Sequence Alignment) features, leveraging evolutionary information to improve folding accuracy. 当前版本只支持true。
                                </div>
                            </span></label>
                            <div class="toggle-group">
                                <div class="toggle-option">
                                    <input type="radio" id="msaTrue" name="useMsa" value="true" checked>
                                    <label for="msaTrue">true</label>
                                </div>
                                <div class="toggle-option">
                                    <input type="radio" id="msaFalse" name="useMsa" value="false" disabled>
                                    <label for="msaFalse" style="opacity: 0.5; cursor: not-allowed;">false</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label data-i18n="use-esm-label">Use esm <span class="info-tooltip" onclick="toggleTooltip(this)">?
                                <div class="tooltip-content" data-i18n="use-esm-tooltip">
                                    Enabling this option activates the features of ESM-2, a state-of-the-art protein language model, to enhance folding speed (with use_msa disabled). 当前版本只支持true。
                                </div>
                            </span></label>
                            <div class="toggle-group">
                                <div class="toggle-option">
                                    <input type="radio" id="esmTrue" name="useEsm" value="true" checked>
                                    <label for="esmTrue">true</label>
                                </div>
                                <div class="toggle-option">
                                    <input type="radio" id="esmFalse" name="useEsm" value="false" disabled>
                                    <label for="esmFalse" style="opacity: 0.5; cursor: not-allowed;">false</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Sequences区域 -->
                <section class="section">
                    <h2 class="section-title" data-i18n="sequences-title">
                        🧬 序列输入
                    </h2>
                    
                    <div id="sequenceContainer">
                        <div class="sequence-item">
                            <div class="sequence-header">
                                <div class="sequence-title" data-i18n="sequence-title">Sequence 1</div>
                                <button type="button" class="delete-sequence" onclick="deleteSequence(this)">🗑️</button>
                            </div>
                            
                            <div class="sequence-controls">
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label data-i18n="sequence-type-label">序列类型</label>
                                    <select name="moduleType">
                                        <option value="proteinChain" selected data-i18n="protein-option">Protein</option>
                                        <option value="dnaSequence" data-i18n="dna-option">DNA</option>
                                        <option value="rnaSequence" data-i18n="rna-option">RNA</option>
                                        <option value="ligand" data-i18n="ligand-option">Ligand smiles</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label class="required" data-i18n="copy-label">重复次数 <span class="info-tooltip" onclick="toggleTooltip(this)">?
                                        <div class="tooltip-content" data-i18n="copy-tooltip">
                                            指定该序列的拷贝数量（1-5）。
                                        </div>
                                    </span></label>
                                    <input type="number" name="copy" value="1" min="1" max="5" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="required" data-i18n="sequence-label">Sequence</label>
                                <textarea name="sequence" data-i18n-placeholder="sequence-placeholder" placeholder="输入蛋白、DNA、RNA序列或者分子smiles..." required minlength="1"></textarea>
                            </div>

                            <!-- Modifications 区域 -->

                        </div>
                    </div>

                    <button type="button" class="add-sequence-btn" onclick="addSequence()" data-i18n="add-sequence-btn">
                        ➕ 增加序列
                    </button>
                </section>

                <button type="submit" class="submit-btn" data-i18n="submit-btn">提交任务</button>
            </form>

            <!-- Protenix任务历史区域 -->
            <section class="section" id="protenix-history-section" style="margin-top: 2rem;">
                <h2 class="section-title" data-i18n="task-history-title">
                    📋 Protenix任务历史
                </h2>

                
                <div class="tasks-container">
                    <div class="tasks-header">
                        <h3 data-i18n="my-protenix-tasks">我的Protenix任务</h3>
                        <button class="refresh-btn" onclick="loadProtenixTaskHistory()" data-i18n="refresh-btn">
                            刷新
                        </button>
                    </div>
                    
                    <div id="protenix-tasks-list" class="tasks-list">
                        <!-- 任务列表将在这里动态加载 -->
                        <div class="loading-placeholder">
                            <div class="loading-spinner"></div>
                            <p data-i18n="loading-task-history">加载任务历史中...</p>
                        </div>
                    </div>

                    <!-- 添加分页控件 -->
                    <div id="protenix-tasks-pagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="protenix-pagination-info-text"></span>
                        </div>
                        <div class="pagination-controls">
                            <button id="protenix-prev-page-btn" class="pagination-btn" onclick="loadProtenixTaskHistoryPage(currentProtenixPage - 1)">
                                <span data-i18n="prev-page">上一页</span>
                            </button>
                            <div id="protenix-page-numbers" class="page-numbers">
                                <!-- 页码按钮将在这里动态生成 -->
                            </div>
                            <button id="protenix-next-page-btn" class="pagination-btn" onclick="loadProtenixTaskHistoryPage(currentProtenixPage + 1)">
                                <span data-i18n="next-page">下一页</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 任务详情模态框 -->
            <div id="taskDetailsModal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 data-i18n="task-details-title">Protenix任务详情</h3>
                        <span class="close" onclick="closeTaskDetailsModal()">&times;</span>
                    </div>
                    <div class="modal-body" id="taskDetailsContent">
                        <!-- 任务详情内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
{% endblock %}

{% block page_js %}
{{ super() }}
<script src="/src/scripts/3dmol.js"></script>
<script>
        // 多语言文本配置
        const translations = {
            zh: {
                'page-title': 'Protenix 蛋白质结构预测',
                'page-subtitle': 'Protenix是先进的蛋白质结构预测平台，能够从氨基酸序列或结构文件预测蛋白质的三维结构和相互作用。支持多种输入格式，提供高精度的结构预测服务。',
                'tutorial-btn': '平台教程',
                'global-params-title': '🌐 全局参数',
                'job-name-label': '任务名',
                'job-name-placeholder': 'protenix_job_5c714d32',
                'use-msa-label': 'Use msa',
                'use-msa-tooltip': 'Enabling this option activates the MSA (Multiple Sequence Alignment) features, leveraging evolutionary information to improve folding accuracy. 当前版本只支持true。',
                'use-esm-label': 'Use esm',
                'use-esm-tooltip': 'Enabling this option activates the features of ESM-2, a state-of-the-art protein language model, to enhance folding speed (with use_msa disabled). 当前版本只支持true。',
                'sequences-title': '🧬 序列输入',
                'sequence-title': 'Sequence 1',
                'sequence-type-label': '序列类型',
                'protein-option': 'Protein',
                'dna-option': 'DNA',
                'rna-option': 'RNA',
                'ligand-option': 'Ligand smiles',
                'copy-label': '重复次数',
                'copy-tooltip': '指定该序列的拷贝数量（1-5）。',
                'sequence-label': 'Sequence',
                'sequence-placeholder': '输入蛋白、DNA、RNA序列或者分子smiles...',
                'add-sequence-btn': '➕ 增加序列',
                'submit-btn': '提交任务',
                'task-history-title': '📋 Protenix任务历史',
                'my-protenix-tasks': '我的Protenix任务',
                'refresh-btn': '刷新',
                'loading-task-history': '加载任务历史中...',
                'task-details-title': 'Protenix任务详情',
                'pending': '等待中',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败',
                'task-id-label': '任务ID',
                'task-name-label': '任务名称',
                'created-time-label': '创建时间',
                'updated-time-label': '更新时间',
                'completed-time-label': '完成时间',
                'view-details-btn': '查看详情',
                'view-results-btn': '查看结果',
                'delete-task-btn': '删除',
                'basic-info-title': '基本信息',
                'params-info-title': '参数信息',
                'result-info-title': '结果信息',
                'error-info-title': '错误信息',
                'no-params': '无参数信息',
                'no-results': '暂无结果',
                'result-processing': '结果处理中...',
                'task-results-title': '任务结果',
                'files-count': '个文件',
                'no-result-files': '暂无结果文件',
                'download-file': '下载文件',
                'display-structure': '显示结构',
                'no-download-link': '无链接',
                'molecular-structure-title': '分子结构',
                'close-viewer': '关闭',
                'loading-structure': '正在加载分子结构...',
                'structure-load-success': '分子结构加载成功',
                'structure-load-failed': '加载分子结构失败',
                'load-failed': '加载失败',
                'collapse-results': '收起结果',
                'expand-results': '查看结果',
                'task-submit-success': 'Protenix任务已成功提交！',
                'task-id': '任务ID',
                'status': '状态',
                'task-submit-failed': '提交失败',
                'task-submitting': '正在提交任务...',
                'network-error': '提交出错，请检查网络连接',
                'task-deleted-success': '任务删除成功',
                'task-delete-failed': '删除任务失败',
                'confirm-delete': '确定要删除这个任务吗？此操作不可撤销。',
                'no-protenix-tasks': '暂无Protenix任务',
                'add-modification-btn': '➕ 添加修饰',
                'unknown-error': '未知错误',
                'completed-at': '完成于',
                'get-task-results-error': '获取任务结果出错',
                'prev-page': '上一页',
                'next-page': '下一页',
                'load-failed': '加载失败',
                'download-all-files': '打包下载',
                'prepare-download': '准备下载',
                'getting-file-list': '正在获取文件列表...',
                'no-files-to-download': '该任务暂无可下载的结果文件',
                'download-confirm': '确定要打包下载任务的所有结果吗？',
                'downloading-files': '正在准备下载包...',
                'download-complete': '下载完成!',
                'download-failed': '获取任务结果失败，请稍后重试',
                'creating-download-package': '正在创建下载包...',
            },
            en: {
                'page-title': 'Protenix Protein Structure Prediction',
                'page-subtitle': 'Protenix is an advanced protein structure prediction platform that can predict three-dimensional structures and interactions of proteins from amino acid sequences or structure files. It supports multiple input formats and provides high-precision structure prediction services.',
                'tutorial-btn': 'Platform Tutorial',
                'global-params-title': '🌐 Global Parameters',
                'job-name-label': 'Job Name',
                'job-name-placeholder': 'protenix_job_5c714d32',
                'use-msa-label': 'Use msa',
                'use-msa-tooltip': 'Enabling this option activates the MSA (Multiple Sequence Alignment) features, leveraging evolutionary information to improve folding accuracy. Currently only supports true.',
                'use-esm-label': 'Use esm',
                'use-esm-tooltip': 'Enabling this option activates the features of ESM-2, a state-of-the-art protein language model, to enhance folding speed (with use_msa disabled). Currently only supports true.',
                'sequences-title': '🧬 Sequence Input',
                'sequence-title': 'Sequence 1',
                'sequence-type-label': 'Sequence Type',
                'protein-option': 'Protein',
                'dna-option': 'DNA',
                'rna-option': 'RNA',
                'ligand-option': 'Ligand smiles',
                'copy-label': 'Copy Count',
                'copy-tooltip': 'Specify the number of copies of this sequence (1-5).',
                'sequence-label': 'Sequence',
                'sequence-placeholder': 'Enter protein, DNA, RNA sequences or molecular smiles...',
                'add-sequence-btn': '➕ Add Sequence',
                'submit-btn': 'Submit Task',
                'task-history-title': '📋 Protenix Task History',
                'my-protenix-tasks': 'My Protenix Tasks',
                'refresh-btn': 'Refresh',
                'loading-task-history': 'Loading task history...',
                'task-details-title': 'Protenix Task Details',
                'pending': 'Pending',
                'running': 'Running',
                'completed': 'Completed',
                'failed': 'Failed',
                'task-id-label': 'Task ID',
                'task-name-label': 'Task Name',
                'created-time-label': 'Created Time',
                'updated-time-label': 'Updated Time',
                'completed-time-label': 'Completed Time',
                'view-details-btn': 'View Details',
                'view-results-btn': 'View Results',
                'delete-task-btn': 'Delete',
                'basic-info-title': 'Basic Information',
                'params-info-title': 'Parameter Information',
                'result-info-title': 'Result Information',
                'error-info-title': 'Error Information',
                'no-params': 'No parameter information',
                'no-results': 'No results available',
                'result-processing': 'Processing results...',
                'task-results-title': 'Task Results',
                'files-count': 'files',
                'no-result-files': 'No result files',
                'download-file': 'Download',
                'display-structure': 'Display Structure',
                'no-download-link': 'No link',
                'molecular-structure-title': 'Molecular Structure',
                'close-viewer': 'Close',
                'loading-structure': 'Loading molecular structure...',
                'structure-load-success': 'Molecular structure loaded successfully',
                'structure-load-failed': 'Failed to load molecular structure',
                'load-failed': 'Load Failed',
                'collapse-results': 'Collapse Results',
                'expand-results': 'View Results',
                'task-submit-success': 'Protenix task submitted successfully!',
                'task-id': 'Task ID',
                'status': 'Status',
                'task-submit-failed': 'Submission failed',
                'task-submitting': 'Submitting task...',
                'network-error': 'Submission error, please check network connection',
                'task-deleted-success': 'Task deleted successfully',
                'task-delete-failed': 'Failed to delete task',
                'confirm-delete': 'Are you sure you want to delete this task? This operation cannot be undone.',
                'no-protenix-tasks': 'No Protenix tasks',
                'add-modification-btn': '➕ Add Modification',
                'unknown-error': 'Unknown error',
                'completed-at': 'Completed at',
                'get-task-results-error': 'Error getting task results',
                'prev-page': 'Previous',
                'next-page': 'Next', 
                'load-failed': 'Load Failed',
                'download-all-files': 'Download ZIP',
                'prepare-download': 'Prepare Download',
                'getting-file-list': 'Getting file list...',
                'no-files-to-download': 'No downloadable result files for this task',
                'download-confirm': 'Are you sure you want to download all results for this task?',
                'downloading-files': 'Preparing download package...',
                'download-complete': 'Download complete!',
                'download-failed': 'Failed to get task results, please try again later',
                'creating-download-package': 'Creating download package...',
            }
        };

        // 当前语言
        let currentLanguage = localStorage.getItem('language') || 'en';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            
            // 更新语言按钮状态
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.lang === lang) {
                    btn.classList.add('active');
                }
            });
            
            // 更新页面语言
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            // 更新文档标题
            document.title = lang === 'zh' ? 'Protenix - 分子设计平台' : 'Protenix - Molecular Design Platform';
            
            // 更新页面文本
            updatePageText(lang);

            // 更新登录时间显示
            const loginTime = localStorage.getItem('loginTime');

            // 重新加载任务历史以应用新语言
            loadProtenixTaskHistory();
            
            console.log('Language switched to:', lang);
        }

        // 将函数暴露到全局，确保事件监听器能够访问到最新的函数
        window.switchLanguage = switchLanguage;
        if (window.I18n) {
            window.I18n.switchLanguage = switchLanguage;
        }

        // 更新页面文本
        function updatePageText(lang) {
            const t = translations[lang];
            
            // 更新带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (t && t[key]) {
                    // 处理包含HTML结构的文本
                    if (t[key].includes('<strong>') || t[key].includes('<br>')) {
                        element.innerHTML = t[key];
                    } else {
                        element.textContent = t[key];
                    }
                }
            });

            // 更新placeholder属性
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (t && t[key]) {
                    element.placeholder = t[key];
                }
            });
        }

        let sequenceCount = 1;
        let refreshInterval = null; // 添加全局变量来管理刷新间隔

        // Protenix分页相关变量
        let currentProtenixPage = 1;
        let totalProtenixPages = 1;
        let totalProtenixItems = 0;
        const protenixPageSize = 6; // 每页显示5条任务

        // Tooltip功能
        function toggleTooltip(element) {
            event.stopPropagation(); // 阻止事件冒泡
            
            // 隐藏所有其他tooltip
            const allTooltips = document.querySelectorAll('.tooltip-content');
            allTooltips.forEach(tooltip => {
                if (tooltip !== element.querySelector('.tooltip-content')) {
                    tooltip.classList.remove('show');
                }
            });
            
            // 切换当前tooltip的显示状态
            const tooltip = element.querySelector('.tooltip-content');
            tooltip.classList.toggle('show');
        }

        // 检查登录状态
        async function checkLoginStatus() {
            console.log('Protenix: Checking login status...');
            
            try {
                // 首先检查后端认证状态
                const response = await fetch('/api/auth/check', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                console.log('Protenix: Backend auth check result:', data);
                
                if (data.logged_in) {
                    // 后端确认已登录，更新localStorage
                    localStorage.setItem('isLoggedIn', 'true');
                    if (data.user && data.user.username) {
                        localStorage.setItem('username', data.user.username);
                    }
                    if (data.user && data.user.loginTime) {
                        localStorage.setItem('loginTime', data.user.loginTime);
                    }
                    
                    // 显示用户信息
                    const username = data.user?.username || localStorage.getItem('username');
                    
                    if (username) {
                        document.getElementById('username').textContent = username;
                        document.getElementById('userAvatar').textContent = username.substring(0,3).toUpperCase();
                    }
                    
                    // 更新登录时间
                    const loginTime = data.user?.loginTime || localStorage.getItem('loginTime');
                    
                    console.log('Protenix: Login status verified, user logged in');
                    return true;
                } else {
                    // 后端确认未登录，清除localStorage并跳转
                    console.log('Protenix: Backend confirms user not logged in:', data.message);
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login';
                    return false;
                }
            } catch (error) {
                console.error('Protenix: Error checking login status:', error);
                
                // 网络错误时，回退到localStorage检查
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                if (isLoggedIn !== 'true') {
                    console.log('Protenix: Fallback - localStorage indicates not logged in');
                    window.location.href = 'login';
                    return false;
                }
                
                // 显示localStorage中的用户信息
                const username = localStorage.getItem('username');
                
                if (username) {
                    document.getElementById('username').textContent = username;
                    document.getElementById('userAvatar').textContent = username.substring(0,3).toUpperCase();
                }
                
                // 更新登录时间
                const loginTime = localStorage.getItem('loginTime');
                
                console.log('Protenix: Fallback - using localStorage, assuming logged in');
                return true;
            }
        }

        // 退出登录
        function logout() {
            // 清除定期刷新
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
            
            fetch('/api/logout', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Server session ended');
                    } else {
                        console.error('Server logout failed:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error during server logout:', error);
                })
                .finally(() => {
                    // Always clear client-side storage and redirect
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login';
                });
        }

        // 启动定期刷新
        function startPeriodicRefresh() {
            // 如果已经有定期刷新在运行，先清除
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            // 设置定期刷新任务历史（每30秒）
            refreshInterval = setInterval(function() {
                loadProtenixTaskHistory();
            }, 30000); // 30秒刷新一次
            
            console.log('已启动Protenix任务定期刷新');
        }

        // 停止定期刷新
        function stopPeriodicRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
                console.log('已停止Protenix任务定期刷新');
        }
        }

        // 页面初始化
        let isInitialized = false;
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Protenix: Page loading...');
            // 防止重复初始化
            if (isInitialized) {
                console.log('Protenix: Already initialized, skipping...');
                return;
            }
            isInitialized = true;
            
            // 先初始化语言切换功能
            currentLanguage = localStorage.getItem('language') || 'en';
            switchLanguage(currentLanguage);
            console.log('current language:', currentLanguage);
            
            // 然后执行其他初始化
            checkLoginStatus().then(loggedIn => {
                if (loggedIn) {
                    // 重要：在这里再次确保语言设置，然后更新登录时间
                    const savedLanguage = localStorage.getItem('language') || 'zh';
                    if (currentLanguage !== savedLanguage) {
                        console.log('Protenix: Language mismatch detected, correcting...');
                        currentLanguage = savedLanguage;
                    }
                    const loginTime = localStorage.getItem('loginTime');
                    if (loginTime) {
                        console.log('Protenix: Updating login time after language initialization');
                        // 使用setTimeout确保DOM完全加载
                        setTimeout(() => {
                            const finalLanguage = localStorage.getItem('language') || 'zh';
                            if (currentLanguage !== finalLanguage) {
                                console.log('Protenix: Final language correction from', currentLanguage, 'to', finalLanguage);
                                currentLanguage = finalLanguage;
                            }
                        }, 600);
                    }
                    
                    // 加载Protenix任务历史
                    loadProtenixTaskHistory();
                    
                    // 启动定期刷新
                    startPeriodicRefresh();
                    
                    // 初始化验证
                    addValidationListeners();
                }
            });

            // 确保语言切换按钮事件正确绑定
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    if (lang) {
                        switchLanguage(lang);
                    }
                });
            });

            // 点击其他地方隐藏tooltip
            document.addEventListener('click', function(event) {
                const allTooltips = document.querySelectorAll('.tooltip-content');
                allTooltips.forEach(tooltip => {
                    tooltip.classList.remove('show');
                });
            });
        });

        // Protenix任务历史管理函数
        
        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                max-width: 400px;
                word-wrap: break-word;
                transition: all 0.3s ease;
            `;
            
            // 根据类型设置颜色
            switch (type) {
                case 'success':
                    notification.style.background = '#10b981';
                    break;
                case 'error':
                    notification.style.background = '#ef4444';
                    break;
                case 'warning':
                    notification.style.background = '#f59e0b';
                    break;
                default:
                    notification.style.background = '#3b82f6';
            }
            
            // 添加到页面
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 加载Protenix任务历史
        async function loadProtenixTaskHistory() {
            loadProtenixTaskHistoryPage(1); // 重置到第一页
        }


        // 加载指定页码的Protenix任务历史
        async function loadProtenixTaskHistoryPage(page = 1) {
            console.log(`=== loadProtenixTaskHistoryPage called with page: ${page} ===`);
            
            // 验证页码
            if (page < 1) {
                page = 1;
            }
            if (page > totalProtenixPages && totalProtenixPages > 0) {
                page = totalProtenixPages;
            }
            
            currentProtenixPage = page;
            
            const tasksList = document.getElementById('protenix-tasks-list');
            const t = translations[currentLanguage];
            
            // 显示加载状态
            tasksList.innerHTML = `
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>${t['loading-task-history']}</p>
                </div>
            `;
            
            try {
                const url = `/api/tasks?task_type=protenix&page=${page}&page_size=${protenixPageSize}&include_content=false`;
                console.log('Fetching Protenix tasks from:', url);
                
                const response = await fetch(url);
                console.log('Response status:', response.status);
                const result = await response.json();
                
                if (result.success && result.tasks) {
                    // 更新分页信息
                    if (result.pagination) {
                        currentProtenixPage = result.pagination.current_page;
                        totalProtenixPages = result.pagination.total_pages;
                        totalProtenixItems = result.pagination.total_items;
                        console.log(`Protenix任务总数: ${totalProtenixItems}, 当前页: ${currentProtenixPage}/${totalProtenixPages}`);
                    } else {
                        // 兼容没有分页信息的情况
                        totalProtenixPages = 1;
                        totalProtenixItems = result.tasks ? result.tasks.length : 0;
                    }
                    
                    displayProtenixTaskHistory(result.tasks);
                    updateProtenixPaginationControls();
                    
                } else {
                    tasksList.innerHTML = `
                        <div class="loading-placeholder">
                            <p style="color: #ef4444;">❌ ${t['load-failed']}: ${result.message || t['unknown-error']}</p>
                        </div>
                    `;
                    // 隐藏分页控件
                    const paginationContainer = document.getElementById('protenix-tasks-pagination');
                    if (paginationContainer) {
                        paginationContainer.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Failed to load Protenix task history:', error);
                tasksList.innerHTML = `
                    <div class="loading-placeholder">
                        <p style="color: #ef4444;">❌ ${t['network-error-check-connection'] || '网络错误，请检查连接'}</p>
                    </div>
                `;
                // 隐藏分页控件
                const paginationContainer = document.getElementById('protenix-tasks-pagination');
                if (paginationContainer) {
                    paginationContainer.style.display = 'none';
                }
            }
        }


        // 更新Protenix分页控件
        function updateProtenixPaginationControls() {
            const paginationContainer = document.getElementById('protenix-tasks-pagination');
            const paginationInfoText = document.getElementById('protenix-pagination-info-text');
            const prevBtn = document.getElementById('protenix-prev-page-btn');
            const nextBtn = document.getElementById('protenix-next-page-btn');
            const pageNumbers = document.getElementById('protenix-page-numbers');

            if (!paginationContainer) return;

            // 只有在有多页或有任务时显示分页控件
            if (totalProtenixPages > 1 || totalProtenixItems > 0) {
                paginationContainer.style.display = 'flex';
            } else {
                paginationContainer.style.display = 'none';
                return;
            }

            // 更新分页信息文本
            const startItem = (currentProtenixPage - 1) * protenixPageSize + 1;
            const endItem = Math.min(currentProtenixPage * protenixPageSize, totalProtenixItems);
            const t = translations[currentLanguage];

            if (paginationInfoText) {
                if (currentLanguage === 'zh') {
                    paginationInfoText.textContent = `显示 ${startItem}-${endItem} 条，共 ${totalProtenixItems} 条任务`;
                } else {
                    paginationInfoText.textContent = `Showing ${startItem}-${endItem} of ${totalProtenixItems} tasks`;
                }
            }

            // 更新上一页按钮状态
            if (prevBtn) {
                prevBtn.disabled = currentProtenixPage <= 1;
            }

            // 更新下一页按钮状态
            if (nextBtn) {
                nextBtn.disabled = currentProtenixPage >= totalProtenixPages;
            }

            // 生成页码按钮
            if (pageNumbers) {
                pageNumbers.innerHTML = generateProtenixPageNumbers();
            }
        }


        // 生成Protenix页码按钮HTML
        function generateProtenixPageNumbers() {
            const maxVisiblePages = 7; // 最多显示7个页码按钮
            let startPage = 1;
            let endPage = totalProtenixPages;

            if (totalProtenixPages > maxVisiblePages) {
                const halfVisible = Math.floor(maxVisiblePages / 2);
                startPage = Math.max(1, currentProtenixPage - halfVisible);
                endPage = Math.min(totalProtenixPages, startPage + maxVisiblePages - 1);

                // 调整开始页码，确保显示完整的页码范围
                if (endPage - startPage + 1 < maxVisiblePages) {
                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }
            }

            let html = '';

            // 第一页和省略号
            if (startPage > 1) {
                html += `<button class="page-number" onclick="loadProtenixTaskHistoryPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="page-number ellipsis">...</span>`;
                }
            }

            // 页码范围
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentProtenixPage ? 'active' : '';
                html += `<button class="page-number ${activeClass}" onclick="loadProtenixTaskHistoryPage(${i})">${i}</button>`;
            }

            // 省略号和最后一页
            if (endPage < totalProtenixPages) {
                if (endPage < totalProtenixPages - 1) {
                    html += `<span class="page-number ellipsis">...</span>`;
                }
                html += `<button class="page-number" onclick="loadProtenixTaskHistoryPage(${totalProtenixPages})">${totalProtenixPages}</button>`;
            }

            return html;
        }


        // 显示Protenix任务历史
        function displayProtenixTaskHistory(tasks) {
            const tasksList = document.getElementById('protenix-tasks-list');
            const t = translations[currentLanguage];
            
            if (!tasks || tasks.length === 0) {
                tasksList.innerHTML = `
                    <div class="loading-placeholder">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">📋</div>
                        <p>${t['no-protenix-tasks'] || '暂无Protenix任务'}</p>
                        <p style="font-size: 0.875rem; color: #9ca3af; margin-top: 0.5rem;">
                            ${currentLanguage === 'zh' ? '提交第一个Protenix任务开始使用' : 'Submit your first Protenix task to get started'}
                        </p>
                    </div>
                `;
                return;
            }
            
            // 检查是否有等待中或运行中的任务
            const hasPendingOrRunningTasks = tasks.some(task => 
                task.status === 'pending' || task.status === 'running'
            );
            
            // 根据任务状态决定是否需要定期刷新
            if (hasPendingOrRunningTasks) {
                // 如果有等待中或运行中的任务，确保定期刷新正在运行
                if (!refreshInterval) {
                    startPeriodicRefresh();
                }
                } else {
                // 如果没有等待中或运行中的任务，停止定期刷新
                stopPeriodicRefresh();
            }
            
            tasksList.innerHTML = tasks.map(task => `
                <div class="task-item" data-task-id="${task.task_id}">
                    <div class="task-header">
                        <span class="task-id">${task.task_id}</span>
                        <span class="task-status ${task.status}">${getStatusText(task.status)}</span>
                    </div>
                    <div class="task-info">
                        <div><strong>${t['task-name-label']}:</strong> ${task.job_name}</div>
                        <div><strong>${t['created-time-label']}:</strong> ${formatDateTime(task.created_at)}</div>
                        <div><strong>${t['updated-time-label']}:</strong> ${formatDateTime(task.updated_at)}</div>
                        ${task.completed_at ? `<div data-field="completed_at"><strong>${t['completed-time-label']}:</strong> ${formatDateTime(task.completed_at)}</div>` : ''}
                    </div>
                    <div class="task-actions">
                        <button class="task-btn view" onclick="viewProtenixTaskDetails('${task.task_id}')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            ${t['view-details-btn']}
                        </button>
                        ${(task.status === 'completed' || task.status === 'running') ? `
                            <button class="task-btn view" onclick="toggleProtenixTaskResults('${task.task_id}')" id="results-btn-${task.task_id}">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7,10 12,15 17,10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                                ${t['view-results-btn']}
                            </button>
                        ` : ''}

                        ${task.status === 'completed' ? `
                            <button class="task-btn view" onclick="downloadProtenixTaskResultsZip('${task.task_id}')" style="background: #10b981;" id="download-btn-${task.task_id}">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <path d="M12 18v-6"></path>
                                    <path d="M9 15l3 3 3-3"></path>
                                </svg>
                                ${t['download-all-files']}
                            </button>
                        ` : ''}

                    </div>
                    
                    <!-- 结果展示区域 -->
                    <div class="task-results-container" id="results-${task.task_id}" style="display: none;">
                        <div class="results-loading">
                            <div class="loading-spinner"></div>
                            <p>加载结果中...</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const t = translations[currentLanguage];
            const statusMap = {
                'pending': t['pending'],
                'running': t['running'],
                'completed': t['completed'],
                'failed': t['failed']
            };
            return statusMap[status] || status;
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 查看Protenix任务详情
        async function viewProtenixTaskDetails(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}`);
                const result = await response.json();
                
                if (result.task) {
                    showProtenixTaskDetailsModal(result.task);
                } else {
                    showNotification('获取任务详情失败: ' + (result.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('获取Protenix任务详情出错:', error);
                showNotification('获取任务详情出错', 'error');
            }
        }

        // 显示Protenix任务详情模态框
        function showProtenixTaskDetailsModal(task) {
            const modal = document.getElementById('taskDetailsModal');
            const content = document.getElementById('taskDetailsContent');
            const t = translations[currentLanguage];
            
            // 安全地解析parameters，可能是字符串或对象
            let parametersData = null;
            try {
                if (task.parameters) {
                    if (typeof task.parameters === 'string') {
                        parametersData = JSON.parse(task.parameters);
                } else {
                        parametersData = task.parameters;
                    }
                }
            } catch (e) {
                console.error('解析parameters失败:', e);
                parametersData = null;
            }
            
            const parametersHtml = parametersData ? 
                Object.entries(parametersData).map(([key, value]) => 
                    `<div><strong>${key}:</strong> ${JSON.stringify(value, null, 2)}</div>`
                ).join('') : t['no-params'];

            // 安全地解析result，可能是字符串或对象
            let resultData = null;
            try {
                if (task.result) {
                    if (typeof task.result === 'string') {
                        resultData = JSON.parse(task.result);
                } else {
                        resultData = task.result;
                    }
                }
            } catch (e) {
                console.error('解析result失败:', e);
                resultData = null;
            }

            const resultHtml = resultData ? 
                `<pre>${JSON.stringify(resultData, null, 2)}</pre>` : 
                (task.status === 'completed' ? t['result-processing'] : t['no-results']);

            content.innerHTML = `
                <h3>${t['task-details-title']}</h3>
                <div class="task-detail-section">
                    <h4>${t['basic-info-title']}</h4>
                    <div><strong>${t['task-id-label']}:</strong> ${task.task_id}</div>
                    <div><strong>${t['task-name-label']}:</strong> ${task.job_name}</div>
                    <div><strong>${t['status']}:</strong> <span class="status-badge ${task.status}">${getStatusText(task.status)}</span></div>
                    <div><strong>${t['created-time-label']}:</strong> ${formatDateTime(task.created_at)}</div>
                    <div><strong>${t['updated-time-label']}:</strong> ${formatDateTime(task.updated_at)}</div>
                    ${task.completed_at ? `<div><strong>${t['completed-time-label']}:</strong> ${formatDateTime(task.completed_at)}</div>` : ''}
                </div>
                
                <div class="task-detail-section">
                    <h4>${t['params-info-title']}</h4>
                    <div class="parameters-content">${parametersHtml}</div>
                </div>
                
                ${resultData ? `
                <div class="task-detail-section">
                    <h4>${t['result-info-title']}</h4>
                    <div class="result-content">${resultHtml}</div>
                </div>
                ` : ''}
                
                ${task.error_message ? `
                <div class="task-detail-section">
                    <h4>${t['error-info-title']}</h4>
                    <div class="error-content">${task.error_message}</div>
                </div>
                ` : ''}
            `;
            
            modal.style.display = 'block';
        }

        // 切换显示Protenix任务结果
        async function toggleProtenixTaskResults(taskId) {
            const resultsContainer = document.getElementById(`results-${taskId}`);
            const resultsBtn = document.getElementById(`results-btn-${taskId}`);
            
            if (!resultsContainer || !resultsBtn) return;
            
            const t = translations[currentLanguage];
            
            // 如果结果区域已显示，则隐藏
            if (resultsContainer.style.display !== 'none') {
                resultsContainer.style.display = 'none';
                resultsBtn.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    ${t['view-results-btn']}
                `;
                return;
            }
            
            // 显示结果区域
            resultsContainer.style.display = 'block';
            resultsBtn.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M6 9l6 6 6-6"></path>
                </svg>
                ${t['collapse-results']}
            `;
            
            // 检查是否已经加载过结果
            if (resultsContainer.dataset.loaded === 'true') {
                return;
            }
            
            try {
                const response = await fetch(`/api/tasks/${taskId}/results`);
                const result = await response.json();
                
                if (result.success) {
                    renderTaskResults(taskId, result);
                    resultsContainer.dataset.loaded = 'true';
                    
                    // 检查任务状态是否发生了变化（从running变为completed）
                    const taskItem = document.querySelector(`[data-task-id="${taskId}"]`);
                    if (taskItem) {
                        const statusElement = taskItem.querySelector('.task-status');
                        const currentStatus = statusElement.textContent.trim();
                        
                        // 如果状态发生变化，更新显示
                        if (result.status === 'completed' && currentStatus !== t['completed']) {
                            statusElement.textContent = t['completed'];
                            statusElement.className = 'task-status completed';
                            
                            // 更新任务信息中的完成时间
                            const taskInfo = taskItem.querySelector('.task-info');
                            if (taskInfo && result.completed_at) {
                                // 检查是否已经有完成时间显示
                                const completedTimeDiv = taskInfo.querySelector('[data-field="completed_at"]');
                                if (!completedTimeDiv) {
                                    const completedTimeHtml = `<div data-field="completed_at"><strong>${t['completed-time-label']}:</strong> ${formatDateTime(result.completed_at)}</div>`;
                                    taskInfo.insertAdjacentHTML('beforeend', completedTimeHtml);
                                }
                            }
                            
                            // 显示状态更新通知
                            // showNotification('任务状态已更新为已完成', 'success');
                            
                            // 重新评估是否需要继续定期刷新
                            // 检查页面上是否还有其他等待中或运行中的任务
                            const allTaskItems = document.querySelectorAll('.task-item');
                            const hasOtherPendingOrRunningTasks = Array.from(allTaskItems).some(item => {
                                const status = item.querySelector('.task-status');
                                return status && (status.classList.contains('pending') || status.classList.contains('running'));
                            });
                            
                            if (!hasOtherPendingOrRunningTasks) {
                                // 如果没有其他等待中或运行中的任务，停止定期刷新
                                stopPeriodicRefresh();
                            }
                        }
                    }
                } else {
                    renderTaskResultsError(taskId, result.message);
                }
            } catch (error) {
                console.error('获取Protenix任务结果出错:', error);
                renderTaskResultsError(taskId, t['get-task-results-error']);
            }
        }

        // 渲染任务结果
        function renderTaskResults(taskId, resultData) {
            const resultsContainer = document.getElementById(`results-${taskId}`);
            if (!resultsContainer) return;
            
            const t = translations[currentLanguage];
            const resultFiles = resultData.result_files || [];
            
            let filesHtml = '';
            if (resultFiles.length > 0) {
                filesHtml = `
                    <div class="inline-result-files">
                        ${resultFiles.map(file => `
                            <div class="inline-file-item">
                                <div class="inline-file-icon">
                                    ${getFileIcon(file.type)}
                                </div>
                                <div class="inline-file-info">
                                    <div class="inline-file-name">${file.name}</div>
                                    <div class="inline-file-details">
                                        <span class="inline-file-type">${file.type}</span>
                                    </div>
                                    ${file.description ? `<div class="inline-file-description">${file.description}</div>` : ''}
                                </div>
                                <div class="inline-file-actions">
                                    ${file.url ? `
                                        <a href="${file.url}" target="_blank" class="inline-download-btn" title="${t['download-file']}">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                <polyline points="7,10 12,15 17,10"></polyline>
                                                <line x1="12" y1="15" x2="12" y2="3"></line>
                                            </svg>
                                        </a>
                                        ${file.type && file.type.toLowerCase().includes('cif') ? `
                                            <button class="inline-display-btn" onclick="displayMolecularStructure('${file.url}', '${file.name}', '${taskId}', this)" title="${t['display-structure']}">
                                                👁
                                            </button>
                                        ` : ''}
                                    ` : `
                                        <span class="inline-no-download">${t['no-download-link']}</span>
                                    `}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                filesHtml = `
                    <div class="inline-no-files">
                        <span class="inline-no-files-icon">📁</span>
                        <span>${t['no-result-files']}</span>
                    </div>
                `;
            }

            resultsContainer.innerHTML = `
                <div class="task-results-header">
                    <h5>${t['task-results-title']} (${resultFiles.length} ${t['files-count']})</h5>
                    ${resultData.completed_at ? `<span class="results-completed-time">${t['completed-at']} ${formatDateTime(resultData.completed_at)}</span>` : ''}
                </div>
                <div class="task-results-content">
                    ${filesHtml}
                </div>
            `;
        }

        // 渲染任务结果错误
        function renderTaskResultsError(taskId, errorMessage) {
            const resultsContainer = document.getElementById(`results-${taskId}`);
            if (!resultsContainer) return;
            
            resultsContainer.innerHTML = `
                <div class="task-results-error">
                    <span class="error-icon">⚠️</span>
                    <span class="error-message">${errorMessage}</span>
                </div>
            `;
        }

        // 获取文件图标
        function getFileIcon(fileType) {
            const iconMap = {
                'cif': '🔬',
                'json': '📄',
                'a3m': '🧬',
                'tar.gz': '📦',
                'm8': '📊',
                'fasta': '🧬',
                'shell脚本': '⚙️',
                'pdb': '🧬',
                'zip': '📦',
                'txt': '📝',
                'log': '📋',
                '压缩包': '📦',
                '结果文件': '📊',
                '链接': '🔗',
                '未知类型': '📄'
            };
            
            // 检查文件类型
            const type = fileType.toLowerCase();
            for (const [key, icon] of Object.entries(iconMap)) {
                if (type.includes(key)) {
                    return icon;
                }
            }
            
            return iconMap['未知类型'];
        }


        // 打包下载Protenix任务的所有结果文件
        async function downloadProtenixTaskResultsZip(taskId) {
            const t = translations[currentLanguage];
            const downloadBtn = document.getElementById(`download-btn-${taskId}`);
            
            // 显示确认对话框
            if (!confirm(t['download-confirm'])) {
                return;
            }
            
            // 禁用下载按钮并显示加载状态
            if (downloadBtn) {
                downloadBtn.disabled = true;
                downloadBtn.innerHTML = `
                    <div class="loading-spinner" style="width: 16px; height: 16px; border-width: 2px; border-top-color: white; margin-right: 0.5rem;"></div>
                    ${t['creating-download-package']}
                `;
            }
            
            try {
                // 显示下载准备状态
                showNotification(t['prepare-download'], 'info');
                
                // 首先获取任务结果，检查是否有文件
                const resultResponse = await fetch(`/api/tasks/${taskId}/results`);
                const resultData = await resultResponse.json();
                
                if (!resultData.success) {
                    throw new Error(resultData.message || 'Failed to get task results');
                }
                
                const resultFiles = resultData.result_files || [];
                if (resultFiles.length === 0) {
                    showNotification(t['no-files-to-download'], 'warning');
                    return;
                }
                
                // 过滤出有下载链接的文件
                const downloadableFiles = resultFiles.filter(file => file.url);
                if (downloadableFiles.length === 0) {
                    showNotification(t['no-files-to-download'], 'warning');
                    return;
                }
                
                // 显示创建下载包状态
                showNotification(t['creating-download-package'], 'info');
                
                // 调用打包下载API
                const downloadResponse = await fetch(`/api/tasks/${taskId}/download-zip`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (!downloadResponse.ok) {
                    const errorData = await downloadResponse.json().catch(() => ({ message: 'Network error' }));
                    throw new Error(errorData.message || `HTTP ${downloadResponse.status}`);
                }
                
                const downloadData = await downloadResponse.json();
                
                if (!downloadData.success) {
                    throw new Error(downloadData.message || 'Failed to create download package');
                }
                
                // 显示下载开始状态
                showNotification(t['downloading-files'], 'info');
                
                // 触发文件下载
                if (downloadData.download_url) {
                    const link = document.createElement('a');
                    link.href = downloadData.download_url;
                    link.download = downloadData.filename || `protenix_results_${taskId}.zip`;
                    
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    // 显示下载成功状态  
                    setTimeout(() => {
                        showNotification(
                            currentLanguage === 'zh' 
                                ? `${t['download-complete']} 文件: ${downloadData.filename}` 
                                : `${t['download-complete']} File: ${downloadData.filename}`,
                            'success'
                        );
                    }, 1000);
                } else {
                    throw new Error('No download URL provided');
                }
                
            } catch (error) {
                console.error('下载Protenix任务结果包失败:', error);
                showNotification(t['download-failed'] + ': ' + error.message, 'error');
            } finally {
                // 恢复下载按钮状态
                if (downloadBtn) {
                    downloadBtn.disabled = false;
                    downloadBtn.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <path d="M12 18v-6"></path>
                            <path d="M9 15l3 3 3-3"></path>
                        </svg>
                        ${t['download-all-files']}
                    `;
                }
            }
        }


        // 关闭任务详情模态框
        function closeTaskDetailsModal() {
            const modal = document.getElementById('taskDetailsModal');
            modal.style.display = 'none';
        }

        // 序列管理函数
        function addSequence() {
            sequenceCount++;
            const container = document.getElementById('sequenceContainer');
            const t = translations[currentLanguage];
            
            const sequenceHtml = `
                <div class="sequence-item">
                <div class="sequence-header">
                    <div class="sequence-title">${t['sequence-title'].replace('1', sequenceCount)}</div>
                    <button type="button" class="delete-sequence" onclick="deleteSequence(this)">🗑️</button>
                </div>
                
                <div class="sequence-controls">
                    <div class="form-group" style="margin-bottom: 0;">
                        <label>${t['sequence-type-label']}</label>
                        <select name="moduleType">
                            <option value="proteinChain" selected>${t['protein-option']}</option>
                            <option value="dnaSequence">${t['dna-option']}</option>
                            <option value="rnaSequence">${t['rna-option']}</option>
                            <option value="ligand">${t['ligand-option']}</option>
                        </select>
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                            <label class="required">${t['copy-label']} <span class="info-tooltip" onclick="toggleTooltip(this)">?
                                <div class="tooltip-content">
                                    ${t['copy-tooltip']}
                                </div>
                            </span></label>
                        <input type="number" name="copy" value="1" min="1" max="5" required>
                    </div>
                </div>

                <div class="form-group">
                    <label class="required">${t['sequence-label']}</label>
                        <textarea name="sequence" placeholder="${t['sequence-placeholder']}" required minlength="1"></textarea>
                </div>

                <!-- Modifications 区域 -->
                <div class="modifications-container">
                    <!-- 这里将动态添加modification项 -->
                </div>

                <button type="button" class="add-modification-btn" onclick="addModification(this)">
                    ${t['add-modification-btn'] || '➕ Add modification'}
                </button>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', sequenceHtml);
            
            // 为新添加的元素添加验证监听器
            addValidationListeners();
        }

        function deleteSequence(button) {
            const sequenceItem = button.closest('.sequence-item');
            const container = document.getElementById('sequenceContainer');
            
            // 确保至少保留一个序列
            if (container.children.length > 1) {
                sequenceItem.remove();
                } else {
                alert(currentLanguage === 'zh' ? '至少需要保留一个序列' : 'At least one sequence must be kept');
            }
        }

        function addModification(button) {
            const sequenceItem = button.closest('.sequence-item');
            const modificationsContainer = sequenceItem.querySelector('.modifications-container');
            
            const modificationHtml = `
                <div class="modification-item">
                <div class="modification-header">
                        <div class="modification-title">Modification</div>
                    <button type="button" class="delete-sequence" onclick="deleteModification(this)">🗑️</button>
                </div>
                
                <div class="modification-controls">
                    <div class="form-group" style="margin-bottom: 0;">
                            <label>PTM Position</label>
                            <input type="text" name="ptmPosition" placeholder="Position">
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                            <label>PTM Type</label>
                            <input type="text" name="ptmType" placeholder="Type">
                        </div>
                    </div>
                </div>
            `;
            
            modificationsContainer.insertAdjacentHTML('beforeend', modificationHtml);
        }

        function deleteModification(button) {
            const modificationItem = button.closest('.modification-item');
            modificationItem.remove();
        }

        // 实时验证功能
        function addValidationListeners() {
            // 验证name输入
            const nameInput = document.getElementById('jobName');
            // if (nameInput) {
            //    nameInput.addEventListener('input', function() {
            //        if (this.value.trim().length === 0) {
            //            this.setCustomValidity('任务名称不能为空');
            //        } else {
            //            this.setCustomValidity('');
            //        }
            //    });
            //}

            // 验证所有copy输入
            document.querySelectorAll('input[name="copy"]').forEach(input => {
                input.addEventListener('input', function() {
                    const value = parseInt(this.value);
                    if (isNaN(value) || value < 1 || value > 5) {
                        this.setCustomValidity('Copy值必须在1-5之间');
            } else {
                        this.setCustomValidity('');
                    }
                });
            });

            // 验证所有sequence输入
            document.querySelectorAll('textarea[name="sequence"]').forEach(textarea => {
                textarea.addEventListener('input', function() {
                    if (this.value.trim().length === 0) {
                        this.setCustomValidity('序列不能为空');
                } else {
                        this.setCustomValidity('');
                }
                });
            });
        }

        // 表单提交处理
        document.getElementById('protenixForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证name字段，如果没有填写则给默认值
            const jobNameElement = document.getElementById('jobName');
            let jobName = jobNameElement ? jobNameElement.value.trim() : '';
            
            // 如果没有填写任务名，使用默认值
            if (!jobName) {
                jobName = `protenix_${Date.now().toString(36)}`;
            }
            // if (!jobName) {
            //    jobName = `protenix_job_${Date.now().toString(36)}`;
            //}
            // if (jobName.length === 0) {
            //    alert(currentLanguage === 'zh' ? '任务名称不能为空，请输入至少一个字符。' : 'Job name cannot be empty, please enter at least one character.');
            //    document.getElementById('jobName').focus();
            //    return;
            //}
            
            const formData = new FormData(this);
            const sequences = [];
            let validationError = false;
            let validSequenceCount = 0; // 计算合法sequence-item的数量
            
            // 收集和验证序列数据
            const sequenceItems = document.querySelectorAll('.sequence-item');
            sequenceItems.forEach((item, index) => {
                if (validationError) return;
                
                const moduleType = item.querySelector('select[name="moduleType"]').value;
                const copyInput = item.querySelector('input[name="copy"]');
                const copyValue = parseInt(copyInput.value);
                const sequenceTextarea = item.querySelector('textarea[name="sequence"]');
                const sequenceValue = sequenceTextarea.value.trim();
                
                // 验证copy字段
                if (isNaN(copyValue) || copyValue < 1 || copyValue > 5) {
                    alert(currentLanguage === 'zh' ? `序列 ${index + 1} 的Copy值必须在1-5之间。` : `Copy value for sequence ${index + 1} must be between 1-5.`);
                    copyInput.focus();
                    validationError = true;
                    return;
                }
                
                // 验证sequence字段
                if (sequenceValue.length === 0) {
                    alert(currentLanguage === 'zh' ? `序列 ${index + 1} 的内容不能为空，请输入至少一个字符。` : `Sequence ${index + 1} content cannot be empty, please enter at least one character.`);
                    sequenceTextarea.focus();
                    validationError = true;
                    return;
                }

                // 验证序列格式
                const sequenceValidation = validateSequence(sequenceValue, moduleType);
                if (!sequenceValidation.valid) {
                    alert(currentLanguage === 'zh' ? 
                        `序列 ${index + 1} 格式不正确：${sequenceValidation.message}` : 
                        `Sequence ${index + 1} format is incorrect: ${sequenceValidation.message}`);
                    sequenceTextarea.focus();
                    validationError = true;
                    return;
                }
                
                // 如果序列验证通过，增加合法序列计数
                validSequenceCount++;
                
                // 收集modifications数据
                const modifications = [];
                const modificationItems = item.querySelectorAll('.modification-item');
                modificationItems.forEach(modItem => {
                    const ptmPosition = modItem.querySelector('input[name="ptmPosition"]').value;
                    const ptmType = modItem.querySelector('input[name="ptmType"]').value;
                    
                    if (ptmPosition && ptmType) {
                        modifications.push({
                            ptmPosition,
                            ptmType
                        });
                    }
                });
                
                sequences.push({
                    moduleType,
                    copy: copyValue,
                    sequence: sequenceValue,
                    modifications
                });
            });

            if (validationError) return;

            // 验证至少有一个序列
            if (validSequenceCount === 0) {
                alert(currentLanguage === 'zh' ? '至少需要添加一个序列。' : 'At least one sequence is required.');
                return;
            }
            
            // 准备提交数据
            const submitData = {
                name: jobName,
                sequences: sequences
            };

            console.log('提交的数据:', submitData);

            // 显示提交中状态
            const submitBtn = document.querySelector('.submit-btn');
            const originalText = submitBtn.textContent;
            const t = translations[currentLanguage];
            submitBtn.textContent = t['task-submitting'];
            submitBtn.disabled = true;

            // 提交到后端
            fetch('/api/protenix', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(submitData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('提交响应:', data);
                
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                if (data.success) {
                    let message = `${t['task-submit-success']}\n${t['task-id']}: ${data.task_id}\n${t['status']}: ${t['pending']}`;
                    
                    // alert(message);
                    showNotification(currentLanguage === 'zh' ? '任务已提交，正在后台处理中...' : 'Task submitted, processing in background...', 'success');
                    
                    // 只清空表单内容，不重置整个表单结构
                    // 清空任务名称
                    document.getElementById('jobName').value = '';
                    
                    // 清空所有序列内容，但保持序列结构
                    const sequenceItems = document.querySelectorAll('.sequence-item');
                    sequenceItems.forEach(item => {
                        // 重置module type为默认值
                        const moduleTypeSelect = item.querySelector('select[name="moduleType"]');
                        if (moduleTypeSelect) {
                            moduleTypeSelect.value = 'proteinChain';
                        }
                        
                        // 重置copy为默认值
                        const copyInput = item.querySelector('input[name="copy"]');
                        if (copyInput) {
                            copyInput.value = '1';
                        }
                        
                        // 清空序列内容
                        const sequenceTextarea = item.querySelector('textarea[name="sequence"]');
                        if (sequenceTextarea) {
                            sequenceTextarea.value = '';
                        }
                        
                        // 清空所有modifications
                        const modificationsContainer = item.querySelector('.modifications-container');
                        if (modificationsContainer) {
                            modificationsContainer.innerHTML = '';
                        }
                    });
                    
                    // 重新加载任务历史
                    loadProtenixTaskHistory();
                    
                } else {
                    alert(`${t['task-submit-failed']}: ` + (data.message || t['unknown-error'] || 'Unknown error'));
                    showNotification(`${t['task-submit-failed']}: ` + (data.message || t['unknown-error'] || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                console.error('提交出错:', error);
                
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                alert(t['network-error']);
                showNotification(t['network-error'], 'error');
            });
        });

        // 关闭任务详情模态框
        function closeTaskDetailsModal() {
            const modal = document.getElementById('taskDetailsModal');
            modal.style.display = 'none';
        }

        // 分子结构显示功能
        let currentViewer = null;
        let currentViewerContainer = null;

        // 显示分子结构
        async function displayMolecularStructure(fileUrl, fileName, taskId, buttonElement) {
            const t = translations[currentLanguage];
            try {
                // 显示加载状态
                // showNotification(t['loading-structure'], 'info');
                
                // 通过后端代理获取CIF文件内容，避免CORS问题
                const response = await fetch('/api/proxy/file', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: fileUrl
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`无法下载文件: ${response.statusText}`);
                }
                
                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.message || '获取文件失败');
                }
                
                const cifContent = result.content;
                
                // 创建或更新分子显示区域
                createMolecularViewer(taskId, fileName, buttonElement);
                
                // 初始化3Dmol查看器
                initializeMolecularViewer(cifContent, fileName);
                
                showNotification(t['structure-load-success'], 'success');
                
            } catch (error) {
                console.error('显示分子结构出错:', error);
                showNotification(`${t['structure-load-failed']}: ` + error.message, 'error');
                
                // 显示错误信息
                if (currentViewerContainer) {
                    currentViewerContainer.innerHTML = `
                        <div class="molecular-viewer-error">
                            <div>❌ ${t['load-failed']}</div>
                            <div style="margin-top: 0.5rem; font-size: 0.875rem;">${error.message}</div>
                        </div>
                    `;
                }
            }
        }

        // 创建分子显示区域
        function createMolecularViewer(taskId, fileName, buttonElement) {
            // 找到点击的文件项
            const fileItem = buttonElement.closest('.inline-file-item');
            if (!fileItem) {
                throw new Error('找不到文件项');
            }
            
            // 移除现有的分子显示区域
            const existingViewer = document.querySelector('.molecular-viewer-container');
            if (existingViewer) {
                existingViewer.remove();
            }
            
            // 创建新的分子显示区域
            const t = translations[currentLanguage];
            const viewerHtml = `
                <div class="molecular-viewer-container">
                    <div class="molecular-viewer-header">
                        <h6 class="molecular-viewer-title">${t['molecular-structure-title']}: ${fileName}</h6>
                        <button class="molecular-viewer-close" onclick="closeMolecularViewer()" title="${t['close-viewer']}">
                            ✕
                        </button>
                    </div>
                    <div class="molecular-viewer-content" id="viewer-${taskId}">
                        <div class="molecular-viewer-loading">
                            <div>🔄 ${t['loading-structure']}</div>
                        </div>
                    </div>
                </div>
            `;
            
            // 在文件项后面插入显示区域
            fileItem.insertAdjacentHTML('afterend', viewerHtml);
            
            // 设置当前查看器容器
            currentViewerContainer = document.getElementById(`viewer-${taskId}`);
        }

        // 初始化分子查看器
        function initializeMolecularViewer(cifContent, fileName) {
            if (!currentViewerContainer) {
                throw new Error('查看器容器未找到');
            }
            
            // 清空容器
            currentViewerContainer.innerHTML = '';
            
            // 检查3Dmol是否可用
            if (typeof $3Dmol === 'undefined') {
                throw new Error('3Dmol.js 库未加载');
            }
            
            // 创建3Dmol查看器
            currentViewer = $3Dmol.createViewer(currentViewerContainer, {
                defaultcolors: $3Dmol.rasmolElementColors,
                backgroundColor: 'black'
            });
            
            if (!currentViewer) {
                throw new Error('无法创建3Dmol查看器');
            }
            
            // 加载CIF数据
            currentViewer.addModel(cifContent, 'cif');
            
            // 设置分子样式
            setMolecularStyles();
            
            // 渲染
            currentViewer.render();
            
            // 自动缩放到合适大小
            currentViewer.zoomTo();
        }

        // 设置分子样式
        function setMolecularStyles() {
            if (!currentViewer) return;
            
            // 首先清除所有样式
            currentViewer.setStyle({}, {});
            
            // 设置所有蛋白质原子为cartoon模型
            currentViewer.setStyle({}, {
                cartoon: {
                    color: 'spectrum'
                }
            });
            
            // 设置配体分子(resname为l01或L01)为球棍模型，覆盖cartoon样式
            currentViewer.setStyle({resn: ['L01', 'l01']}, {
                stick: {
                    colorscheme: 'default',
                    radius: 0.3
                },
                sphere: {
                    colorscheme: 'default',
                    radius: 0.4
                }
            });
            
            // 也处理其他常见的配体残基名称
            const otherLigandResNames = ['LIG', 'HET', 'UNL', 'MOL', 'DRG'];
            otherLigandResNames.forEach(resName => {
                currentViewer.setStyle({resn: resName}, {
                    stick: {
                        colorscheme: 'default',
                        radius: 0.3
                    },
                    sphere: {
                        colorscheme: 'default',
                        radius: 0.4
                    }
                });
            });
        }

        // 关闭分子显示区域
        function closeMolecularViewer() {
            const viewerContainer = document.querySelector('.molecular-viewer-container');
            if (viewerContainer) {
                // 清理查看器
                if (currentViewer) {
                    currentViewer.clear();
                    currentViewer = null;
                }
                currentViewerContainer = null;
                
                // 移除容器
                viewerContainer.remove();
            }
        }

        // 序列验证函数
        function validateSequence(sequence, moduleType) {
            const cleanSequence = sequence.toUpperCase().replace(/\s+/g, ''); // 转大写并去除空格
            
            switch (moduleType) {
                case 'proteinChain':
                    // 蛋白质序列：只能包含20种标准氨基酸字母
                    const proteinPattern = /^[ARNDCEQGHILKMFPSTWYV]+$/;
                    return {
                        valid: proteinPattern.test(cleanSequence),
                        message: currentLanguage === 'zh' ? 
                            '蛋白质序列只能包含标准氨基酸字母 (A, R, N, D, C, E, Q, G, H, I, L, K, M, F, P, S, T, W, Y, V)' :
                            'Protein sequence can only contain standard amino acid letters (A, R, N, D, C, E, Q, G, H, I, L, K, M, F, P, S, T, W, Y, V)'
                    };
                    
                case 'dnaSequence':
                    // DNA序列：只能包含A, T, G, C
                    const dnaPattern = /^[ATGC]+$/;
                    return {
                        valid: dnaPattern.test(cleanSequence),
                        message: currentLanguage === 'zh' ? 
                            'DNA序列只能包含碱基字母 (A, T, G, C)' :
                            'DNA sequence can only contain base letters (A, T, G, C)'
                    };
                    
                case 'rnaSequence':
                    // RNA序列：只能包含A, U, G, C
                    const rnaPattern = /^[AUGC]+$/;
                    return {
                        valid: rnaPattern.test(cleanSequence),
                        message: currentLanguage === 'zh' ? 
                            'RNA序列只能包含碱基字母 (A, U, G, C)' :
                            'RNA sequence can only contain base letters (A, U, G, C)'
                    };
                    
                case 'ligand':
                    // SMILES格式：基本的SMILES字符验证
                    const smilesPattern = /^[A-Za-z0-9@+\-\[\]()=#\/\\\.:%]+$/;
                    // 确保至少包含一个表示原子的字母
                    const hasAtomLetter = /[A-Za-z]/.test(cleanSequence);
                    // 不能只有数字
                    const notOnlyNumbers = !/^\d+$/.test(cleanSequence);
                    // 不能只有特殊字符
                    const notOnlySpecialChars = !/^[@+\-\[\]()=#\/\\\.:%]+$/.test(cleanSequence);
                    
                    return {
                        valid: smilesPattern.test(cleanSequence) && 
                            hasAtomLetter && 
                            notOnlyNumbers && 
                            notOnlySpecialChars && 
                            cleanSequence.length > 0,
                        message: currentLanguage === 'zh' ? 
                            'SMILES格式不正确，必须包含表示原子的字母，不能只有数字或特殊字符' :
                            'Invalid SMILES format, must contain atom letters, cannot be only numbers or special characters'
                    };
                    
                default:
                    return { valid: true, message: '' };
            }
        }

</script>
{% endblock %}