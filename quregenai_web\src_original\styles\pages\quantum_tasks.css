/* 页面特定样式 - 量子计算任务列表 */

.container {
    max-width: 1400px;
    margin: 0 auto;
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid rgba(102, 126, 234, 0.1);
}

h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 2.5em;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #00d4ff 0%, #667eea 50%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}
.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}
.btn-secondary {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    color: white;
    margin-right: 10px;
}
.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.stats-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 35px;
}
.stat-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}
.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}
.stat-label {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}
.loading {
    text-align: center;
    padding: 40px;
    font-size: 18px;
    color: #666;
}
.error {
    background-color: #fee;
    border: 2px solid #fcc;
    color: #c33;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    text-align: center;
}
.tasks-list {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-top: 20px;
}
.task-item {
    display: flex;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 100px;
    position: relative;
}

.task-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.task-item:last-child {
    border-bottom: none;
}
.task-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.task-item:hover::before {
    opacity: 1;
}
.task-main {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.task-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}
.task-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}
.task-header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}
.task-id {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #667eea;
    font-weight: bold;
    background-color: #f1f3f4;
    padding: 6px 12px;
    border-radius: 8px;
    border: 2px solid #667eea;
}
.task-id a {
    text-decoration: none;
    color: inherit;
}
.task-id a:hover {
    text-decoration: none;
    color: inherit;
}
.task-status {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.status-completed {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #003d1a;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
}
.status-pending {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    color: #664400;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}
.status-running {
    background: linear-gradient(135deg, #00d4ff, #0099cc);
    color: #003344;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
    animation: pulse 2s infinite;
}
.status-failed {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: #330000;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
.task-device {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9em;
    background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
    padding: 6px 12px;
    border-radius: 8px;
    border: 2px solid #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}
.task-details {
    display: flex;
    align-items: center;
    gap: 20px;
    margin: 6px 0;
    font-size: 0.85em;
    color: #666;
    flex-wrap: wrap;
}
.task-detail-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}
.task-timing {
    font-size: 0.85em;
    color: #666;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}
.task-timing-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}
.task-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-left: 20px;
}
.task-result-mini {
    font-size: 0.75em;
    color: #e74c3c;
    font-weight: 500;
}

.no-tasks {
    text-align: center;
    padding: 60px;
    color: #666;
    font-size: 18px;
}
.pagination {
    margin-top: 40px;
    text-align: center;
}
.pagination button {
    margin: 0 5px;
    padding: 10px 15px;
    border: 2px solid #667eea;
    background-color: white;
    color: #667eea;
    cursor: pointer;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}
.pagination button:hover {
    background-color: #667eea;
    color: white;
}
.pagination button.active {
    background-color: #667eea;
    color: white;
}
.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}
.timestamp {
    font-size: 0.8em;
    color: #999;
    text-align: center;
    margin-top: 20px;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 18px;
    color: #666;
}
.error {
    background-color: #fee;
    border: 2px solid #fcc;
    color: #c33;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    text-align: center;
}

/* 平台教程按钮样式 */
.tutorial-btn {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.2);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tutorial-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    text-decoration: none;
    color: #667eea;
}