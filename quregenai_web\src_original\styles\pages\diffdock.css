/* 页面特定样式 - DiffDock */

/* 内容区域样式已在navigation.css中定义，这里只添加页面特定的样式 */

.page-header {
    background: linear-gradient(135deg, #ffcc80 0%, #ffb74d 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 25px rgba(255, 183, 77, 0.2);
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-icon {
    font-size: 3rem;
}

.page-icon img {
    width: 48px;
    height: 48px;
    object-fit: contain;
}

.page-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.feature-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e1e5e9;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature-card {
    background: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border-left: 4px solid #f57c00;
    transition: transform 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
    color: #f57c00;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.feature-badge {
    background: #fff3e0;
    color: #f57c00;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-primary {
    background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 124, 0, 0.3);
}

.btn-secondary {
    background: white;
    color: #f57c00;
    border: 2px solid #f57c00;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #f57c00;
    color: white;
    transform: translateY(-2px);
}

/* 表单样式 */
.section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.required::after {
    content: ' *';
    color: #e74c3c;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #f57c00;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.upload-area {
    border: 2px dashed #f57c00;
    border-radius: 0.5rem;
    padding: 1.25rem;
    text-align: center;
    background: #fff8f0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    background: #fff3e0;
    border-color: #e65100;
}

.upload-area.dragover {
    background: #fff3e0;
    border-color: #e65100;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 2rem;
    color: #f57c00;
    margin-bottom: 0.75rem;
}

.file-input {
    display: none;
}

.ligand-tabs {
    display: flex;
    margin-bottom: 1rem;
    gap: 0.5rem;
    border: none;
}

.ligand-tab {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.ligand-tab.active {
    background: #f57c00;
    color: white;
    border-color: #f57c00;
}

.ligand-tab:hover {
    background: #fff3e0;
    border-color: #f57c00;
}

.ligand-tab.active:hover {
    background: #e65100;
}

.protein-tabs {
    display: flex;
    margin-bottom: 1rem;
    gap: 0.5rem;
    border: none;
}

.protein-tab {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.protein-tab.active {
    background: #f57c00;
    color: white;
    border-color: #f57c00;
}

.protein-tab:hover {
    background: #fff3e0;
    border-color: #f57c00;
}

.protein-tab.active:hover {
    background: #e65100;
}

#proteinSequenceTextarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 0.5rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
}

#proteinSequenceTextarea:focus {
    outline: none;
    border-color: #f57c00;
    box-shadow: 0 0 0 3px rgba(245, 124, 0, 0.1);
}

.input-hint {
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.5rem;
    text-align: right;
}

.submit-btn {
    background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    margin-top: 2rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 124, 0, 0.3);
}

.submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 任务历史样式 */
.tasks-container {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
}

.tasks-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.tasks-header h3 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f57c00;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.refresh-btn:hover {
    background: #e65100;
    transform: translateY(-1px);
}

.tasks-list {
    max-height: 600px;
    overflow-y: auto;
    padding-top: 0.5rem;
}

.task-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.task-item:first-child {
    margin-top: 0;
}

.task-item:hover {
    background: #f1f5f9;
    border-color: #f57c00;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(245, 124, 0, 0.1);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.task-id {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: #64748b;
    background: #e2e8f0;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.task-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-status.pending {
    background: #fef3c7;
    color: #92400e;
}

.task-status.running {
    background: #dbeafe;
    color: #1e40af;
}

.task-status.completed {
    background: #d1fae5;
    color: #065f46;
}

.task-status.failed {
    background: #fee2e2;
    color: #991b1b;
}

.task-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #64748b;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.task-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.task-btn.view {
    background: #f57c00;
    color: white;
}

.task-btn.view:hover {
    background: #e65100;
}


.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: #64748b;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f1f5f9;
    border-top: 4px solid #f57c00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 1rem;
    max-width: 900px;
    width: 85%;
    max-height: 65vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 - 小屏幕上的模态框 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-width: none;
        max-height: 80vh;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: #1e293b;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #64748b;
    transition: color 0.2s ease;
}

.close:hover {
    color: #ef4444;
}

.modal-body {
    padding: 1.5rem;
}

.detail-section {
    margin-bottom: 1.5rem;
}

.detail-section h4 {
    color: #1e293b;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #f57c00;
}

.detail-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.detail-value {
    color: #6b7280;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
}

/* 结果文件样式 */
.result-files-grid {
    display: grid;
    gap: 1rem;
}

.result-file-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1rem;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
    align-items: center;
    transition: all 0.2s ease;
}

.result-file-item:hover {
    background: #f1f5f9;
    border-color: #f57c00;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(245, 124, 0, 0.1);
}

.file-icon {
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.file-info {
    min-width: 0;
}

.file-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
    word-break: break-all;
}

.file-details {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 0.25rem;
}

.file-type {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.125rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.file-size {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.125rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
}

.file-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.4;
}

.file-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.download-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f57c00;
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.download-btn:hover {
    background: #e65100;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 124, 0, 0.3);
}

.no-download {
    color: #9ca3af;
    font-size: 0.875rem;
    font-style: italic;
}

.no-files {
    text-align: center;
    padding: 3rem 2rem;
    color: #6b7280;
}

.no-files-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-files p {
    margin: 0.5rem 0;
}

.no-files-hint {
    font-size: 0.875rem;
    color: #9ca3af;
}

/* 任务结果内联显示样式 */
.task-results-container {
    margin-top: 1rem;
    border-top: 2px solid #f1f5f9;
    padding-top: 1rem;
    background: #fafbfc;
    border-radius: 0 0 0.75rem 0.75rem;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding-top: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
        padding-top: 1rem;
    }
}

.task-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0 1rem;
}

.task-results-header h5 {
    margin: 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
}

.results-completed-time {
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
}

.task-results-content {
    padding: 0 1rem 1rem;
}

.inline-result-files {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.inline-file-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.75rem;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 0.75rem;
    align-items: center;
    transition: all 0.2s ease;
}

.inline-file-item:hover {
    border-color: #f57c00;
    box-shadow: 0 2px 8px rgba(245, 124, 0, 0.1);
}

.inline-file-icon {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: #f9fafb;
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
}

.inline-file-info {
    min-width: 0;
}

.inline-file-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    word-break: break-all;
}

.inline-file-details {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.inline-file-type {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.inline-file-size {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.inline-file-description {
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.3;
}

.inline-file-actions {
    display: flex;
    align-items: center;
}

.inline-download-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f57c00;
    color: white;
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.inline-download-btn:hover {
    background: #e65100;
    transform: scale(1.05);
}

.inline-no-download {
    color: #9ca3af;
    font-size: 0.75rem;
    font-style: italic;
}

.inline-no-files {
    text-align: center;
    padding: 2rem 1rem;
    color: #6b7280;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.inline-no-files-icon {
    font-size: 2rem;
    opacity: 0.5;
}

.task-results-error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    color: #991b1b;
}

.error-icon {
    font-size: 1.25rem;
}

.error-message {
    font-size: 0.875rem;
}

.results-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem 1rem;
    color: #6b7280;
}

.results-loading .loading-spinner {
    width: 24px;
    height: 24px;
    margin-bottom: 0.5rem;
}

.results-loading p {
    margin: 0;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .result-file-item, .inline-file-item {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 0.5rem;
    }

    .file-details, .inline-file-details {
        justify-content: center;
    }

    .file-actions, .inline-file-actions {
        align-items: center;
    }

    .task-results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* 分子查看器样式 */
.molecule-viewer-container {
    margin-top: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    /* 让查看器跨越整个grid容器的所有列 */
    grid-column: 1 / -1;
}

.molecule-viewer-header {
    background: #f9fafb;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.molecule-viewer-title {
    font-weight: 600;
    color: #374151;
    margin: 0;
    font-size: 0.875rem;
}

.molecule-viewer-close {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: background 0.2s;
}

.molecule-viewer-close:hover {
    background: #dc2626;
}

.molecule-viewer {
    width: 100% !important;
    height: 250px;
    position: relative;
    min-width: 100% !important;
    overflow: hidden;
}

/* 强制3Dmol查看器使用完整宽度 */
.molecule-viewer canvas {
    width: 100% !important;
    max-width: none !important;
    min-width: 100% !important;
}

/* 更具体的3Dmol容器样式 */
.molecule-viewer > div {
    width: 100% !important;
    height: 100% !important;
}

/* 针对3Dmol生成的所有子元素 */
.molecule-viewer * {
    max-width: none !important;
}

.inline-display-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 0.5rem;
    font-size: 0.875rem;
}

.inline-display-btn:hover {
    background: #059669;
    transform: scale(1.05);
}

.inline-display-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

.viewer-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 250px;
    color: #6b7280;
}

.viewer-loading .loading-spinner {
    width: 32px;
    height: 32px;
    margin-bottom: 1rem;
}

.viewer-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 250px;
    color: #ef4444;
    text-align: center;
    padding: 1rem;
}

.viewer-error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}



/* 分页容器样式 */
.pagination-container {
    background: white;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info {
    color: #6b7280;
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #374151;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
    background: #fff3e0;
    border-color: #f57c00;
    color: #e65100;
}

.pagination-btn:disabled {
    background: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    border-color: #e5e7eb;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    color: #374151;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.page-number:hover {
    background: #fff3e0;
    border-color: #f57c00;
    color: #e65100;
}

.page-number.active {
    background: #f57c00;
    border-color: #f57c00;
    color: white;
}

.page-number.ellipsis {
    background: transparent;
    border: none;
    cursor: default;
    color: #9ca3af;
}

.page-number.ellipsis:hover {
    background: transparent;
    border: none;
    color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .page-numbers {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* 平台教程按钮样式（更醒目，并与标题同排） */
.page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.tutorial-btn {
    background: #f86e6e;
    color: #3d2f00;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.2s ease;
    border: 1px solid #ed9696;
    margin-left: 0.75rem;
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
}

.tutorial-btn:hover {
    background: #ed9696;
    color: #3d2f00;
    text-decoration: none;
    transform: translateY(-1px);
}