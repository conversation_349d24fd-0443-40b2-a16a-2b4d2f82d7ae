import axios from 'axios'

// 创建一个用于检查CORS配置的预检请求拦截器
export function configureCORS() {
  // 设置全局默认值
  axios.defaults.withCredentials = true; // 允许跨域请求携带凭证

  // 添加全局请求拦截器
  axios.interceptors.request.use(
    config => {
      // 为每个请求添加CORS相关头信息
      config.headers['Access-Control-Allow-Origin'] = '*';
      config.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
      config.headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, Authorization, X-Requested-With';
      config.headers['Access-Control-Allow-Credentials'] = 'true';
      return config;
    },
    error => {
      return Promise.reject(error);
    }
  );
} 