* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.register-container {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    margin: 1rem;
}

.register-header {
    text-align: center;
    margin-bottom: 2rem;
}

.register-header h1 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.register-header p {
    color: #666;
    font-size: 0.9rem;
}

.tab-switcher {
    display: flex;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #e1e5e9;
}

.tab-option {
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 1rem;
    position: relative;
}

.tab-option.active {
    color: #667eea;
}

.tab-option.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #667eea;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-group input.valid {
    border-color: #27ae60;
}

.form-group input.invalid {
    border-color: #e74c3c;
}

.password-requirements {
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.5rem;
    line-height: 1.4;
}

.requirement {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.requirement-icon {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
}

.requirement.valid .requirement-icon {
    background: #27ae60;
    color: white;
}

.requirement.invalid .requirement-icon {
    background: #e74c3c;
    color: white;
}

.requirement.neutral .requirement-icon {
    background: #bdc3c7;
    color: white;
}

.register-btn {
    width: 100%;
    padding: 0.75rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    margin-bottom: 1rem;
}

.register-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.register-btn:active {
    transform: translateY(0);
}

.register-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.back-btn {
    width: 100%;
    padding: 0.75rem;
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.back-btn:active {
    transform: translateY(0);
}

.code-btn {
    padding: 0.75rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.code-btn:hover:not(:disabled) {
    background: #5a6fd5;
}

.code-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.error-message, .success-message {
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.75rem;
    border-radius: 0.375rem;
    display: none;
}

.error-message {
    background: #fdf2f2;
    color: #e74c3c;
    border: 1px solid #fbb6ce;
}

.success-message {
    background: #f0fff4;
    color: #38a169;
    border: 1px solid #9ae6b4;
}

.auth-divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: #666;
    font-size: 0.875rem;
}

/* 语言切换按钮样式 */
.language-switcher {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
}

.language-btn {
    padding: 0.5rem 0.75rem;
    border: 2px solid #e1e5e9;
    background: white;
    color: #666;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.language-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.language-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    font-weight: 600;
}

/* 用户协议样式 */
.agreement-container {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.agreement-checkbox {
    margin-top: 0.25rem;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.agreement-text {
    font-size: 0.875rem;
    color: #666;
    line-height: 1.4;
}

.agreement-link {
    color: #667eea;
    text-decoration: underline;
    cursor: pointer;
    transition: color 0.2s ease;
}

.agreement-link:hover {
    color: #5a6fd5;
}