// 任务管理 Composable
import { ref, computed } from 'vue'

export function useTaskManager() {
  // 状态管理
  const tasks = ref([])
  const currentTask = ref(null)
  const isTasksLoading = ref(false)
  const errorTaskMessage = ref('')
  const refreshInterval = ref(null)
  
  // 结果显示相关
  const resultsVisible = ref(false)
  const isResultsLoading = ref(false)
  const resultsStatusText = ref('等待结果...')
  const humanReadableResults = ref('')
  const scores = ref([])
  const resultFiles = ref([])
  const detailsExpanded = ref(true)
  const activeResultTab = ref('scores')
  
  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(5)
  const totalPages = ref(1)
  
  // 任务详情模态框
  const showTaskDetailsModal = ref(false)
  const selectedTask = ref(null)
  
  // 计算属性
  const paginatedScores = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return scores.value.slice(start, end)
  })
  
  const paginatedFiles = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return resultFiles.value.slice(start, end)
  })
  
  // 加载任务历史
  const loadTaskHistory = async () => {
    try {
      isTasksLoading.value = true
      errorTaskMessage.value = ''
      
      console.log('加载 AutoDock 任务历史...')
      const response = await fetch('/api/tasks?task_type=autodock')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        tasks.value = result.tasks || []
        console.log(`加载了 ${tasks.value.length} 个任务`)
      } else {
        throw new Error(result.message || '加载任务失败')
      }
    } catch (error) {
      console.error('加载任务历史失败:', error)
      errorTaskMessage.value = '加载任务历史失败: ' + error.message
    } finally {
      isTasksLoading.value = false
    }
  }
  
  // 查看任务详情
  const viewTaskDetails = (task) => {
    selectedTask.value = task
    showTaskDetailsModal.value = true
  }
  
  // 关闭任务详情模态框
  const closeTaskDetailsModal = () => {
    showTaskDetailsModal.value = false
    selectedTask.value = null
  }
  
  // 显示任务结果
  const showTaskResults = async (task) => {
    try {
      isResultsLoading.value = true
      resultsStatusText.value = '加载结果中...'
      resultsVisible.value = true
      currentTask.value = task
      
      console.log('获取任务结果:', task.task_id)
      const response = await fetch(`/api/tasks/${task.task_id}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        displayResults(result.task)
      } else {
        throw new Error(result.message || '获取任务结果失败')
      }
    } catch (error) {
      console.error('获取任务结果失败:', error)
      resultsStatusText.value = '加载结果失败: ' + error.message
    } finally {
      isResultsLoading.value = false
    }
  }
  
  // 显示结果
  const displayResults = (taskData) => {
    try {
      console.log('显示任务结果:', taskData)
      
      // 更新任务信息
      currentTask.value = taskData
      
      // 解析结果数据
      if (taskData.result && taskData.result.results) {
        const results = taskData.result.results
        
        // 处理人类可读结果
        if (results.human_readable_results) {
          humanReadableResults.value = results.human_readable_results
        }
        
        // 处理分数数据
        if (results.scores) {
          processScoresData(results.scores)
        }
        
        // 处理文件数据
        if (results.files) {
          processFilesData(results.files)
        }
        
        resultsStatusText.value = '结果加载完成'
      } else {
        resultsStatusText.value = '暂无结果数据'
      }
    } catch (error) {
      console.error('显示结果时出错:', error)
      resultsStatusText.value = '显示结果失败: ' + error.message
    }
  }
  
  // 处理分数数据
  const processScoresData = (scoresData) => {
    try {
      const processedScores = []
      
      for (const [filename, scoresArray] of Object.entries(scoresData)) {
        if (Array.isArray(scoresArray) && scoresArray.length > 0) {
          processedScores.push({
            filename: filename,
            scoresArray: scoresArray,
            bestScore: Math.min(...scoresArray),
            bestPose: scoresArray.indexOf(Math.min(...scoresArray)) + 1
          })
        }
      }
      
      // 按最佳分数排序
      processedScores.sort((a, b) => a.bestScore - b.bestScore)
      
      scores.value = processedScores
      console.log('处理了分数数据:', processedScores.length, '个分子')
    } catch (error) {
      console.error('处理分数数据时出错:', error)
    }
  }
  
  // 处理文件数据
  const processFilesData = (filesData) => {
    try {
      const processedFiles = []
      
      for (const [filename, urlsArray] of Object.entries(filesData)) {
        if (Array.isArray(urlsArray) && urlsArray.length > 0) {
          processedFiles.push({
            filename: filename,
            urlsArray: urlsArray,
            downloadCount: urlsArray.length
          })
        }
      }
      
      resultFiles.value = processedFiles
      console.log('处理了文件数据:', processedFiles.length, '个文件')
    } catch (error) {
      console.error('处理文件数据时出错:', error)
    }
  }
  
  // 下载文件
  const downloadFileFromUrl = async (url, filename) => {
    try {
      console.log('下载文件:', url, filename)
      
      // 创建下载链接
      const link = document.createElement('a')
      link.href = url
      link.download = filename || 'download'
      link.target = '_blank'
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      console.log('文件下载已开始')
    } catch (error) {
      console.error('下载文件失败:', error)
      throw error
    }
  }
  
  // 隐藏结果区域
  const hideResults = () => {
    resultsVisible.value = false
    currentTask.value = null
    scores.value = []
    resultFiles.value = []
    humanReadableResults.value = ''
  }
  
  // 切换详细结果显示
  const toggleDetailsView = () => {
    detailsExpanded.value = !detailsExpanded.value
  }
  
  // 更新分页
  const updateScoresPagination = (page) => {
    if (page >= 1 && page <= Math.ceil(scores.value.length / pageSize.value)) {
      currentPage.value = page
    }
  }
  
  const updateFilesPagination = (page) => {
    if (page >= 1 && page <= Math.ceil(resultFiles.value.length / pageSize.value)) {
      currentPage.value = page
    }
  }
  
  // 获取项目排名
  const getItemRank = (index) => {
    return (currentPage.value - 1) * pageSize.value + index + 1
  }
  
  // 格式化状态文本
  const getStatusText = (status) => {
    const statusMap = {
      'pending': '等待中',
      'running': '运行中',
      'completed': '已完成',
      'failed': '失败'
    }
    return statusMap[status] || status
  }
  
  // 格式化日期时间
  const formatDateTime = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  }
  
  // 格式化任务参数
  const formatTaskParameters = (parameters) => {
    if (!parameters) return 'N/A'
    
    try {
      // 深拷贝参数对象
      const formattedParams = JSON.parse(JSON.stringify(parameters))
      
      // 隐藏文件内容，只显示文件名和大小
      if (formattedParams.receptor && formattedParams.receptor.content) {
        formattedParams.receptor = {
          name: formattedParams.receptor.name,
          type: formattedParams.receptor.type,
          size: formattedParams.receptor.content.length + ' 字符'
        }
      }
      
      if (formattedParams.ligands && Array.isArray(formattedParams.ligands)) {
        formattedParams.ligands = formattedParams.ligands.map(ligand => ({
          name: ligand.name,
          type: ligand.type,
          size: ligand.content ? ligand.content.length + ' 字符' : 'N/A'
        }))
      }
      
      return JSON.stringify(formattedParams, null, 2)
    } catch (error) {
      return 'N/A'
    }
  }
  
  // 开始定期刷新
  const startPeriodicRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }
    // 每30秒刷新一次任务状态
    refreshInterval.value = setInterval(() => {
      loadTaskHistory()
    }, 30000)
  }
  
  // 停止定期刷新
  const stopPeriodicRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
  }
  
  return {
    // 状态
    tasks,
    currentTask,
    isTasksLoading,
    errorTaskMessage,
    resultsVisible,
    isResultsLoading,
    resultsStatusText,
    humanReadableResults,
    scores,
    resultFiles,
    detailsExpanded,
    activeResultTab,
    currentPage,
    pageSize,
    totalPages,
    showTaskDetailsModal,
    selectedTask,
    
    // 计算属性
    paginatedScores,
    paginatedFiles,
    
    // 方法
    loadTaskHistory,
    viewTaskDetails,
    closeTaskDetailsModal,
    showTaskResults,
    displayResults,
    downloadFileFromUrl,
    hideResults,
    toggleDetailsView,
    updateScoresPagination,
    updateFilesPagination,
    getItemRank,
    getStatusText,
    formatDateTime,
    formatTaskParameters,
    startPeriodicRefresh,
    stopPeriodicRefresh
  }
}
