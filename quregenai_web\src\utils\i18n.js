// 多语言管理工具
import { ref, reactive } from 'vue'

// 当前语言
export const currentLanguage = ref(localStorage.getItem('language') || 'zh')

// 多语言文本配置 - 按页面/模块组织
export const translations = reactive({
  zh: {
    // 通用组件
    common: {
      'platform-name': 'QureGenAI 药物设计平台',
      'logout-btn': '退出登录',
      'login-btn': '登录',
      'register-btn': '注册',
      'password-label': '密码',
      'password-placeholder': '请输入密码',
      'mobile-label': '手机号码/邮箱',
      'mobile-placeholder': '请输入手机号码/邮箱',
      'network-error': '发生网络错误，请稍后重试'
    },

    // 首页
    home: {
      'sidebar-title': '功能模块',
      'welcome-title': '欢迎使用 QureGenAI 药物设计平台',
      'welcome-text': '欢迎来到 QureGenAI！\n\n我们是您AI与量子计算驱动的药物发现引擎。您在此可无缝调用顶尖工具：\n\n前沿AI模型：DiffDock（精确分子对接）、AlphaFold（蛋白结构预测）等，赋能分子设计、靶点识别。\n量子计算加速：突破传统计算瓶颈，探索更广阔药物空间。\n灵活使用：支持网页操作、API集成及MCP服务，无缝融入您的工作流。\n我们持续进化：模型库将不断加入尖端工具，助您始终掌握科技前沿。\n\n精准付费，绝不浪费：采用按秒计费模式，真正按用量付费，尤其适合大模型调用，高效灵活。\n\n即刻开始，让QureGenAI加速您的突破！',
      'just-logged-in': '刚刚登录',
      'minutes-ago': '分钟前登录',
      'hours-ago': '小时前登录',
      'days-ago': '天前登录'
    },

    // 功能模块
    modules: {
      'autodock-desc': '高通量筛选与分子对接',
      'autodock-detail': 'AutoDock是一套自动化的分子对接软件，用于快速大批量预测小分子配体与蛋白质受体的结合模式和结合亲和力。',
      'diffdock-desc': '口袋预测与分子对接',
      'diffdock-detail': 'DiffDock是基于扩散模型的新一代扩散分子对接方法，无需指定蛋白质口袋位置，更准确地预测蛋白质-配体复合物的三维结构。',
      'protenix-desc': '蛋白质结构预测',
      'protenix-detail': 'Protenix是先进的蛋白质结构预测平台，能够从氨基酸序列或者结构文件预测蛋白质的三维结构和相互作用等功能。',
      'quantum-tasks-name': '量子计算任务',
      'quantum-tasks-desc': '量子计算任务管理',
      'quantum-computing': 'TyxonQ 量子计算',
      'quantum-detail': '利用量子计算的强大能力加速药物发现过程，突破传统计算瓶颈，探索更广阔的分子空间和复杂的量子化学计算。',
      'molmap-desc': 'ADMET性质预测',
      'molmap-detail': 'MolMap是先进的分子性质预测平台，能够快速准确地预测小分子的ADMET性质，包括吸收、分布、代谢、排泄和毒性等关键药物性质。',
      'pocketvina-desc': '全自动口袋寻找与批量对接',
      'pocketvina-detail': 'PocketVina是新一代全自动口袋寻找与批量对接工具，无需手动指定结合位点，自动识别蛋白质表面的潜在结合口袋，支持多个分子和蛋白质文件的批量对接分析。',
      'raman-desc': '低成本光谱分析检测',
      'raman-detail': 'Raman光谱检测是一种低成本、高效的光谱分析技术，广泛应用于中草药成分含量检测、农作物农药残留检测、药物成分分析等领域，为食品安全和药物质量控制提供可靠的技术支持。',
      'userprofile-name': '我的（API KEY申请）',
      'userprofile-desc': '账户管理',
      'classic-algorithm': '经典算法',
      'ai-driven': 'AI驱动',
      'structure-prediction': '结构预测',
      'quantum-acceleration': '量子加速',
      'admet-prediction': 'ADMET预测',
      'auto-pocket-docking': '全自动口袋对接',
      'spectral-analysis': '光谱分析',
      'raman-spectral': 'Raman光谱',
      'molmap-name': 'MolMap',
      'pocketvina-name': 'PocketVina',
      'autodock-name': 'AutoDock',
      'diffdock-name': 'DiffDock',
      'protenix-name': 'Protenix'
    },

    // 登录页面
    login: {
      'title': 'QureGenAI 药物设计 & TyxonQ 量子计算平台',
      'subtitle': '请输入您的账户信息以继续',
      'reset-password': '忘记密码？',
      'error': '手机号码或密码错误，请重试',
      'no-account': '还没有账户？',
      'register-btn': '注册新账户',
      'features-title': '平台功能',
      'feature-hts': '高通量筛选',
      'feature-pocket': '口袋发现',
      'feature-folding': '蛋白质折叠',
      'feature-quantum': '量子计算',
      'feature-ai-mcp': 'AI模型与MCP服务',
      'mobile-invalid': '请输入有效的手机号码或邮箱地址',
      'failed': '登录失败，请重试'
    },

    // 注册页面
    register: {
      'page-title': '注册 - QureGenAI量子计算 & AI药物设计平台',
      'title': '注册账户',
      'subtitle': '创建您的QureGenAI药物设计平台账户',
      'contact-info-label': '手机号码/邮箱',
      'contact-info-placeholder': '请输入手机号码或邮箱',
      'send-code-btn': '获取验证码',
      'verification-code-label': '验证码',
      'verification-code-placeholder': '请输入验证码',
      'confirm-password-label': '确认密码',
      'confirm-password-placeholder': '请再次输入密码',
      'req-length': '至少6位字符',
      'req-uppercase': '包含大写字母',
      'req-lowercase': '包含小写字母',
      'req-number': '包含数字',
      'req-special': '包含特殊符号(!@#$%^&*等)',
      'success': '注册成功！正在跳转到首页...',
      'already-have-account': '已有账户？',
      'back-to-login': '返回登录',
      'send-code-retry': '秒后重试',
      'invalid-contact': '请输入有效的手机号码或邮箱',
      'code-sent': '验证码已发送',
      'code-send-failed': '验证码发送失败',
      'password-not-match': '两次输入的密码不一致',
      'password-invalid': '密码不符合要求',
      'enter-code': '请输入验证码',
      'failed': '注册失败，请重试',
      'agreement-text': '我已阅读并同意',
      'user-agreement': '用户协议',
      'agreement-required': '请先同意用户协议'
    },

    // 重置密码页面
    reset: {
      'page-title': '找回密码 - QureGenAI药物设计平台',
      'title': '找回密码',
      'subtitle': '请填写信息重置您的密码',
      'contact-info-label': '手机号码/邮箱',
      'contact-info-placeholder': '请输入手机号码/邮箱',
      'send-code-btn': '获取验证码',
      'verification-code-label': '验证码',
      'verification-code-placeholder': '请输入验证码',
      'new-password-label': '新密码',
      'new-password-placeholder': '请输入新密码',
      'confirm-new-password-label': '确认新密码',
      'confirm-new-password-placeholder': '请再次输入新密码',
      'req-length': '至少6位字符',
      'req-uppercase': '包含大写字母',
      'req-lowercase': '包含小写字母',
      'req-number': '包含数字',
      'req-special': '包含特殊符号(!@#$%^&*等)',
      'reset-password-btn': '重置密码',
      'remembered-password': '已想起密码？',
      'back-to-login': '返回登录',
      'reset-success': '您的密码已重置成功！即将跳转到登录页面...',
      'send-code-retry': '秒后重试',
      'invalid-contact': '请输入有效的手机号码或邮箱',
      'code-sent': '验证码已发送',
      'code-send-failed': '验证码发送失败',
      'network-error': '网络错误，请检查连接后重试',
      'enter-code': '请输入验证码',
      'password-invalid': '密码不符合要求',
      'password-not-match': '两次输入的密码不一致',
      'reset-failed': '密码重置失败'
    },

    // 用户协议页面
    terms: {
      'page-title': '医图生科（苏州）生命科学技术有限公司用户协议',
      'platform-name': 'QureGenAI智能药物设计与太玄量子（TyxonQ）计算平台',
      'platform-subtitle': '量子计算与AI药物设计服务平台',
      'agreement-title': '用户协议',
      'agreement-intro': '欢迎您注册并使用由医图生科（苏州）生命科学技术有限公司提供的QureGenAI智能药物设计平台与TyxonQ太玄量子计算服务。请仔细阅读以下条款。',
      'service-description-title': '一、服务描述',
      'service-description-intro': 'QureGenAI与太玄量子（TyxonQ）提供基于AI与量子计算的云计算、药物模拟与设计服务，包括但不限于：',
      'service-item-1': '• 量子计算资源访问与服务',
      'service-item-2': '• AI模型辅助药物设计与模拟',
      'service-item-3': '• 生物医药云计算解决方案',
      'service-item-4': '• 算法模型开发环境',
      'user-responsibility-title': '二、用户责任',
      'responsibility-item-1': '1. 您需确保提供的注册信息真实准确',
      'responsibility-item-2': '2. 不得使用平台进行任何非法活动或侵害第三方权益',
      'responsibility-item-3': '3. 不得干扰或破坏平台服务的正常运行',
      'responsibility-item-4': '4. 对账户安全和活动承担全部责任',
      'disclaimer-title': '⚠️ 重要免责声明',
      'disclaimer-item-1': '• 平台以"现状"提供所有服务，不承诺可用性、精确性或可靠性',
      'disclaimer-item-2': '• 所有计算结果仅供科研参考，不构成医疗建议或专业意见',
      'disclaimer-item-3': '• 您理解量子计算服务存在结果不确定性，平台不保证任何特定结果的准确性',
      'disclaimer-item-4': '• 对因使用服务导致的任何直接、间接损失，平台不承担责任',
      'disclaimer-item-5': '• 在适用法律允许的最大范围内排除所有担保责任',
      'data-processing-title': '三、数据处理与改进',
      'data-processing-item-1': '1. 平台将收集和使用<strong>非隐私的运维数据</strong>（如服务日志、性能指标、使用模式、硬件信息等）进行产品改进',
      'data-processing-item-2': '2. 您授权平台使用匿名化、聚合化的数据改进算法和服务质量',
      'intellectual-property-title': '四、知识产权',
      'intellectual-property-content': '平台的技术、软件、界面设计等均为医图生科（苏州）生命科学技术有限公司知识产权，涉及开源技术的除外。您保留输入数据和输出结果的所有权，但授权平台必要的使用权限以提供服务。',
      'agreement-update-title': '五、协议更新',
      'agreement-update-content': '医图生科（苏州）生命科学技术有限公司保留随时更新条款的权利。继续使用服务视为接受更新后的条款。',
      'footer-copyright': '© 2025 QureGenAI与太玄量子（TyxonQ）| 量子AI药物研发平台',
      'footer-contact': '联系邮箱: <EMAIL>'
    },

    // AutoDock页面
    autodock: {
      'page-title': 'AutoDock 高通量筛选与分子对接',
      'page-subtitle': 'AutoDock是一套应用广泛的开源分子对接软件套件，用于预测小分子配体（如候选药物）与已知三维结构的生物大分子（通常是蛋白质受体）的结合模式和亲和力。它通过模拟配体在受体活性位点的各种可能构象和朝向，并利用能量打分函数评估结合强度，从而帮助科研人员理解分子间的相互作用机制，并广泛应用于药物发现和虚拟筛选等领域。该套件主要包含AutoDock程序（执行对接计算，采用拉马克遗传算法和经验自由能打分函数）和AutoGrid程序（预先计算格点图，用于表示受体）。',
      'tutorial-btn': '平台教程',
      'guide-title': '🎯 对接口袋识别与分析指南',
      'step-one-title': '步骤一：蛋白质结构分析',
      'step-one-item1': '上传蛋白质文件：首先上传PDB或PDBQT格式的蛋白质结构文件',
      'step-one-item2': '观察整体结构：在3D查看器中旋转、缩放，熟悉蛋白质的整体折叠结构',
      'step-one-item3': '寻找潜在口袋：寻找蛋白质表面的凹陷区域，这些通常是配体结合位点',
      'step-one-item4': '关注活性位点：如果已知活性中心残基，重点观察这些区域',
      'step-two-title': '步骤二：口袋中心点确定',
      'step-two-item1': '点击原子选择：在3D查看器中点击口袋内的关键原子（如活性位点残基）',
      'step-two-item2': '查看原子信息：选中的原子信息会显示在下方，包括残基类型和坐标',
      'step-two-item3': '设置中心点：点击原子信息条目可直接将该坐标设为对接中心',
      'step-two-item4': '多原子平均：选择多个原子时，系统会自动计算几何中心',
      'step-three-title': '步骤三：对接盒子尺寸设置',
      'step-three-item1': '估算口袋大小：观察目标口袋的大致尺寸范围',
      'step-three-item2': '考虑配体尺寸：确保对接盒子足够容纳配体分子的各种构象',
      'step-three-item3': '推荐尺寸：',
      'size-small': '小分子配体：15-20 Å',
      'size-peptide': '肽段配体：20-25 Å',
      'size-large': '大分子配体：25-30 Å',
      'tips-title': '专业建议与注意事项',
      'pocket-types-title': '🎪 常见口袋类型：',
      'pocket-type1': '酶活性位点（通常较深且狭窄）',
      'pocket-type2': '蛋白质-蛋白质相互作用界面（通常较浅且宽阔）',
      'pocket-type3': '变构调节位点（远离活性中心）',
      'precautions-title': '⚠️ 注意事项：',
      'precaution1': '避免将对接盒子设置得过大，会增加计算时间',
      'precaution2': '确保盒子完全包含目标结合位点',
      'precaution3': '考虑蛋白质柔性，为侧链运动留出空间',
      'precaution4': '如有已知结合物结构，可参考其结合模式',
      'viewer-title': '分子结构查看器',
      'loading': '加载中...',
      'upload-btn': '点击上传结构文件',
      'supported-formats': '支持的文件格式：',
      'pdb-desc': '• PDB文件 - 蛋白质结构（卡通渲染）',
      'sdf-desc': '• SDF文件 - 小分子结构（球棍模型）',
      'pdbqt-desc': '• PDBQT文件 - 电荷结构文件',
      'uploaded-files': '已上传文件',
      'no-files': '暂无文件',
      'selected-atoms-info': '已选择的原子信息',
      'clear-all': '一键清除',
      'atom-selection-guide': '原子选择使用说明：',
      'atom-guide1': '在上方3D分子查看器中，点击蛋白质或小分子上的原子进行选择',
      'atom-guide2': '选中的原子信息会显示在下方列表中，包括原子类型、残基信息和坐标',
      'atom-guide3': '点击下方原子信息条目，可直接将该原子坐标设为对接口袋中心点',
      'atom-guide4': '选择多个原子时，系统会自动计算它们的几何中心作为口袋中心坐标',
      'no-atoms': '暂无选择的原子',
      'remove-atom': '移除此原子',
      'participating-calculation': '✓ 参与计算',
      'excluded': '✗ 已排除',
      'pocket-center-mode-title': '🎯 对接口袋中心选择模式',
      'single-atom-mode': '单个原子',
      'click-atom-to-set': '点击原子设置',
      'geometric-center-mode': '几何中心',
      'auto-calculate': '自动计算',
      'single-atom-mode-desc': '💡 <strong>单个原子模式</strong>：点击下方任意原子将其坐标设为口袋中心',
      'geometric-center-mode-desc': '💡 <strong>几何中心模式</strong>：使用所有选中原子的几何中心作为口袋中心',
      'delete-file': '删除文件',
      'uploading-files': '正在上传文件...',
      'input-params-title': 'AutoDock 输入参数设置',
      'docking-files-selection': '📁 对接文件选择',
      'files-selection-desc': '选择用于分子对接的蛋白质和小分子文件',
      'protein-receptor-file': '🧬 蛋白质受体文件 (PDB)',
      'uploaded-files-selection': '从已上传文件选择',
      'no-protein-files': '暂无已上传的蛋白质文件，请先在上方分子查看器中上传PDB或PDBQT文件',
      'or': '或',
      'upload-local-receptor': '上传本地受体文件',
      'click-select-pdb': '点击选择PDB/PDBQT文件',
      'or-drag': '或',
      'drag-files-here': '拖拽文件到此处',
      'supported-pdb-formats': '支持格式：PDB、PDBQT',
      'selected-protein-file': '已选择的蛋白质文件',
      'clear-selection': '清空选择',
      'protein-file-hint': '选择一个PDB或PDBQT格式的蛋白质文件作为对接受体',
      'ligand-file': '💊 小分子配体文件',
      'select-ligand-files': '选择配体分子结构文件',
      'batch-smiles-file': '批量SMILES文件',
      'batch-smiles-input': '批量SMILES输入',
      'select-from-uploaded-ligands': '从已上传的配体分子文件中选择',
      'no-ligand-files': '暂无已上传的分子文件，请先在上方分子查看器中上传文件',
      'upload-local-ligand': '上传本地配体文件',
      'click-select-files': '点击选择文件',
      'supported-ligand-formats': '支持格式：PDB、SDF、PDBQT、MOL2',
      'selected-files': '已选择的文件',
      'files-count': '个',
      'file-selection-hint': '点击已上传文件或上传本地文件进行选择',
      'single-file-mode': '选择1个文件：',
      'single-file-desc': '使用单分子对接模式',
      'multi-file-mode': '选择多个文件：',
      'multi-file-desc': '使用多分子批量对接模式',
      'upload-csv': '上传CSV文件',
      'csv-format-desc': 'CSV格式：第一列为name，第二列为smiles',
      'csv-example': '例如：aspirin,CC(=O)OC1=CC=CC=C1C(=O)O',
      'molecule-list': '分子列表',
      'molecules-count': '个',
      'batch-docking-info': '💊 所有分子将用于批量对接',
      'search-placeholder': '搜索分子名称...',
      '2d-structure': '2D结构',
      'csv-upload-hint': '上传包含分子名称和SMILES的CSV文件，所有分子将自动用于批量对接',
      'direct-smiles-input': '直接输入SMILES字符串',
      'smiles-placeholder': '每行输入一个SMILES字符串，例如：\nCCO\nCC(=O)OC1=CC=CC=C1C(=O)O\nCN1C=NC2=C1C(=O)N(C(=O)N2C)C',
      'parse-smiles': '解析SMILES',
      'clear-text': '清空文本',
      'smiles-input-hint': '每行输入一个SMILES字符串，系统将自动为每个分子生成名称',
      'docking-params-title': '⚙️ 对接参数',
      'docking-params-desc': '设置分子对接的空间范围和计算参数',
      'pocket-center-coords': '🎯 口袋中心坐标',
      'x-coord': 'X 坐标',
      'y-coord': 'Y 坐标',
      'z-coord': 'Z 坐标',
      'auto-fill-coords': '自动填入',
      'manual-input-hint': '手动输入坐标或点击按钮从已选择的原子计算中心坐标',
      'docking-box-size': '📦 对接盒子尺寸 (Å)',
      'x-size': 'X 尺寸',
      'y-size': 'Y 尺寸',
      'z-size': 'Z 尺寸',
      'box-size-hint': '定义搜索空间的尺寸，默认为15Å × 15Å × 15Å',
      'threads-label': '⚡ 计算线程数',
      'threads-unit': '线程',
      'threads-hint': '设置计算使用的线程数量，推荐值：2000-5000',
      'validate-inputs': '✅ 验证输入',
      'start-docking': '🚀 开始对接',
      'wait-submit': '提交中...',
      'ok-submit': '提交成功',
      'reset-params': '🔄 重置参数',
      'docking-results': '📊 对接结果',
      'waiting-results': '等待结果...',
      'hide-results-btn': '隐藏结果',
      'task-info': '任务信息',
      'task-id': '任务ID:',
      'task-status': '状态:',
      'results-details': '结果详情',
      'scores-tab': '结合能量',
      'files-tab': '文件下载',
      'model-extraction-title': '模型提取工具',
      'model-extraction-desc': '从对接结果文件中提取指定构象并转换为PDB格式',
      'select-pdbqt-file': '选择PDBQT对接结果文件:',
      'select-model-number': '选择构象编号:',
      'default-model-number': '(默认提取第1个构象)',
      'extract-and-convert': '⚡ 提取并转换为PDB',
      'task-history': '对接任务历史',
      'refresh-tasks': '刷新',
      'local': '本地',
      'file-added': '已添加文件',
      'file-exists': '文件已存在',
      'protein-file-selected': '已选择蛋白质文件',
      'waiting-results': '等待结果...',
      'processing': '处理中...',
      'completed': '对接计算已完成',
      'failed': '计算失败',
      'running': '计算中...',
      'pending': '等待中...',
      'error': '发生错误',
      'success': '成功完成',
      'cancelled': '已取消',
      'timeout': '计算超时',
      'no-results': '暂无详细结果信息',
      'hide-results': '收起',
      'show-results': '展开',
      'view-task-details': '查看详情',
      'view-task-results': '查看结果',
      'created-time': '创建时间',
      'updated-time': '更新时间',
      'completed-time': '完成时间',
      'error-message': '错误信息',
      'parameters': '参数',
      'task-completed-summary': '任务 {taskId} 共完成了 {count} 个分子的对接计算。',
      'task-summary-keyword1': '共完成了',
      'task-summary-keyword2': '个分子的对接计算',
      'task-general-keyword1': '任务',
      'task-general-keyword2': '完成',
      'no-autodock-tasks': '暂无AutoDock任务历史',
      'no-scores-data': '暂无分数数据',
      'no-files-available': '暂无文件可下载',
      'input-validation-failed': '输入验证失败：',
      'incomplete-pocket-coords': '请输入完整的口袋中心坐标',
      'validation-passed': '输入验证通过！',
      'batch-smiles-mode': '批量SMILES模式：将对接',
      'molecules': '个分子',
      'multi-file-mode-confirm': '多分子文件模式：将对接',
      'files': '个分子文件',
      'single-file-mode-confirm': '单分子文件模式',
      'single-smiles-mode': '单分子SMILES模式',
      'docking-task-submitted': '对接任务已提交！任务ID:',
      'login-expired': '登录状态已过期，请重新登录',
      'login-abnormal': '登录状态异常，请重新登录',
      'docking-request-failed': '对接请求失败:',
      'task-results-unavailable': '任务结果不可用',
      'get-task-results-error': '获取任务结果出错',
      'get-task-details-failed': '获取任务详情失败:',
      'get-task-details-error': '获取任务详情出错',
      'please-select-csv': '请选择 CSV 格式文件',
      'no-valid-molecules-in-csv': 'CSV 文件中没有找到有效的分子数据',
      'successfully-parsed-molecules': '成功解析',
      'csv-parse-failed': 'CSV 文件解析失败:',
      'text-input-area-not-found': '文本输入区域未找到',
      'please-input-smiles': '请输入 SMILES 字符串',
      'no-valid-smiles-found': '没有找到有效的 SMILES 字符串',
      'smiles-parse-failed': 'SMILES 文本解析失败:',
      'batch-docking-confirm-title': '您即将开始批量分子对接：',
      'molecule-count': '• 分子数量：',
      'docking-mode': '• 对接模式：',
      'batch-smiles-file-mode': '批量SMILES文件',
      'batch-smiles-input-mode': '批量SMILES输入',
      'all-molecules-will-dock': '• 所有分子将同时进行对接',
      'confirm-continue': '确定要继续吗？',
      'multi-file-docking-confirm-title': '您即将开始多分子文件对接：',
      'molecule-file-count': '• 分子文件数量：',
      'multi-molecule-files-mode': '多分子结构文件',
      'prev-page': '上一页',
      'next-page': '下一页',
      'showing-items': '显示',
      'of-total-items': '条，共',
      'total-tasks': '条任务',
      'single-atom-mode': '单个原子模式',
      'center-mode': '几何中心模式',
      'single-atom-desc': '点击原子设置',
      'center-desc': '自动计算',
      'single-atom-mode-desc': '点击下方任意原子将其坐标设为口袋中心',
      'center-mode-desc': '使用所有选中原子的几何中心作为口袋中心',
      'pocket-center-mode': '对接口袋中心选择模式',
      'participate-calculation': '参与计算',
      'excluded': '已排除'
    }
  },
  en: {
    // 通用组件
    common: {
      'platform-name': 'QureGenAI Drug Design Platform',
      'logout-btn': 'Logout',
      'login-btn': 'Login',
      'register-btn': 'Register',
      'password-label': 'Password',
      'password-placeholder': 'Enter your password',
      'mobile-label': 'Mobile Number/Email',
      'mobile-placeholder': 'Enter your mobile number/email',
      'network-error': 'Network error occurred, please try again later'
    },

    // 首页
    home: {
      'sidebar-title': 'Function Modules',
      'welcome-title': 'Welcome to QureGenAI Drug Design Platform',
      'welcome-text': 'Welcome to QureGenAI!\n\nWe are your AI and quantum computing-driven drug discovery engine. Here you can seamlessly access cutting-edge tools:\n\nFrontier AI Models: DiffDock (precise molecular docking), AlphaFold (protein structure prediction), and more, empowering molecular design and target identification.\nQuantum Computing Acceleration: Breaking through traditional computational bottlenecks to explore a broader drug space.\nFlexible Usage: Supporting web operations, API integration, and MCP services, seamlessly integrating into your workflow.\nWe Continuously Evolve: Our model library will continuously incorporate cutting-edge tools, helping you stay at the forefront of technology.\n\nPrecise Billing, No Waste: Adopting a per-second billing model, truly pay-as-you-use, especially suitable for large model calls, efficient and flexible.\n\nStart now and let QureGenAI accelerate your breakthroughs!',
      'just-logged-in': 'Just logged in',
      'minutes-ago': 'minutes ago',
      'hours-ago': 'hours ago',
      'days-ago': 'days ago'
    },

    // 功能模块
    modules: {
      'autodock-desc': 'High-throughput Screening and Molecular Docking',
      'autodock-detail': 'AutoDock is a suite of automated molecular docking software for rapid and high-throughput prediction of binding modes and binding affinities between small molecule ligands and protein receptors.',
      'diffdock-desc': 'Pocket Prediction and Molecular Docking',
      'diffdock-detail': 'DiffDock is a new generation of diffusion-based molecular docking method that can accurately predict the three-dimensional structure of protein-ligand complexes without specifying protein pocket locations.',
      'protenix-desc': 'Protein Structure Prediction',
      'protenix-detail': 'Protenix is an advanced protein structure prediction platform capable of predicting protein three-dimensional structures and interactions from amino acid sequences or structure files.',
      'quantum-tasks-name': 'Quantum Computing Tasks',
      'quantum-tasks-desc': 'Quantum Computing Task Management',
      'quantum-computing': 'TyxonQ Quantum Computing',
      'quantum-detail': 'Harness the power of quantum computing to accelerate drug discovery processes, breaking through traditional computational bottlenecks and exploring broader molecular spaces and complex quantum chemical calculations.',
      'molmap-desc': 'ADMET Property Prediction',
      'molmap-detail': 'MolMap is an advanced molecular property prediction platform that can quickly and accurately predict ADMET properties of small molecules, including absorption, distribution, metabolism, excretion and toxicity.',
      'pocketvina-desc': 'Auto Pocket Finding & Batch Docking',
      'pocketvina-detail': 'PocketVina is a new generation of fully automated pocket finding and batch docking tool that automatically identifies potential binding pockets on protein surfaces without manual specification of binding sites, supporting batch docking analysis of multiple molecules and protein files.',
      'raman-desc': 'Low-cost Spectral Analysis Detection',
      'raman-detail': 'Raman spectroscopy detection is a low-cost, efficient spectral analysis technology widely used in traditional Chinese medicine component content detection, crop pesticide residue detection, drug component analysis and other fields, providing reliable technical support for food safety and drug quality control.',
      'userprofile-name': 'My Profile(API Key Application)',
      'userprofile-desc': 'Account Management',
      'classic-algorithm': 'Classic Algorithm',
      'ai-driven': 'AI-Driven',
      'structure-prediction': 'Structure Prediction',
      'quantum-acceleration': 'Quantum Acceleration',
      'admet-prediction': 'ADMET Prediction',
      'auto-pocket-docking': 'Auto Pocket Docking',
      'spectral-analysis': 'Spectral Analysis',
      'raman-spectral': 'Raman Spectral',
      'molmap-name': 'MolMap',
      'pocketvina-name': 'PocketVina',
      'autodock-name': 'AutoDock',
      'diffdock-name': 'DiffDock',
      'protenix-name': 'Protenix'
    },

    // 登录页面
    login: {
      'title': 'QureGenAI Drug Design & TyxonQ Quantum Computing Platform',
      'subtitle': 'Please enter your account information to continue',
      'reset-password': 'Forgot your password?',
      'error': 'Mobile number or password is incorrect, please try again',
      'no-account': 'Don\'t have an account?',
      'register-btn': 'Register New Account',
      'features-title': 'Platform Features',
      'feature-hts': 'High-throughput Screening',
      'feature-pocket': 'Pocket Discovery',
      'feature-folding': 'Protein Folding',
      'feature-quantum': 'Quantum Computing',
      'feature-ai-mcp': 'AI Models & MCP Services',
      'mobile-invalid': 'Please enter a valid mobile number or email address',
      'failed': 'Login failed, please try again'
    },

    // 注册页面
    register: {
      'page-title': 'Register - QureGenAI Quantum Computing & AI Drug Design Platform',
      'title': 'Register Account',
      'subtitle': 'Create your QureGenAI drug design platform account',
      'contact-info-label': 'Mobile/Email',
      'contact-info-placeholder': 'Enter mobile number or email',
      'send-code-btn': 'Get Code',
      'verification-code-label': 'Verification Code',
      'verification-code-placeholder': 'Enter verification code',
      'confirm-password-label': 'Confirm Password',
      'confirm-password-placeholder': 'Enter password again',
      'req-length': 'At least 6 characters',
      'req-uppercase': 'Contains uppercase letter',
      'req-lowercase': 'Contains lowercase letter',
      'req-number': 'Contains number',
      'req-special': 'Contains special character (!@#$%^&* etc.)',
      'success': 'Registration successful! Redirecting to homepage...',
      'already-have-account': 'Already have an account?',
      'back-to-login': 'Back to Login',
      'send-code-retry': 'seconds to retry',
      'invalid-contact': 'Please enter a valid mobile number or email',
      'code-sent': 'Verification code sent',
      'code-send-failed': 'Failed to send verification code',
      'password-not-match': 'Passwords do not match',
      'password-invalid': 'Password does not meet requirements',
      'enter-code': 'Please enter verification code',
      'failed': 'Registration failed, please try again',
      'agreement-text': 'I have read and agree to the',
      'user-agreement': 'User Agreement',
      'agreement-required': 'Please agree to the User Agreement first'
    },

    // 重置密码页面
    reset: {
      'page-title': 'Reset Password - QureGenAI Drug Design Platform',
      'title': 'Reset Password',
      'subtitle': 'Please fill in the information to reset your password',
      'contact-info-label': 'Mobile/Email',
      'contact-info-placeholder': 'Enter mobile number/email',
      'send-code-btn': 'Get Code',
      'verification-code-label': 'Verification Code',
      'verification-code-placeholder': 'Enter verification code',
      'new-password-label': 'New Password',
      'new-password-placeholder': 'Enter new password',
      'confirm-new-password-label': 'Confirm New Password',
      'confirm-new-password-placeholder': 'Enter new password again',
      'req-length': 'At least 6 characters',
      'req-uppercase': 'Contains uppercase letter',
      'req-lowercase': 'Contains lowercase letter',
      'req-number': 'Contains number',
      'req-special': 'Contains special character (!@#$%^&* etc.)',
      'reset-password-btn': 'Reset Password',
      'remembered-password': 'Remembered your password?',
      'back-to-login': 'Back to Login',
      'reset-success': 'Your password has been reset successfully! Redirecting to login page...',
      'send-code-retry': 'seconds to retry',
      'invalid-contact': 'Please enter a valid mobile number or email',
      'code-sent': 'Verification code sent',
      'code-send-failed': 'Failed to send verification code',
      'network-error': 'Network error, please check connection and try again',
      'enter-code': 'Please enter verification code',
      'password-invalid': 'Password does not meet requirements',
      'password-not-match': 'Passwords do not match',
      'reset-failed': 'Password reset failed'
    },

    // 用户协议页面
    terms: {
      'page-title': 'QureGenAI & TyxonQ User Agreement',
      'platform-name': 'QureGenAI Intelligent Drug Design & TyxonQ Quantum Computing Platform',
      'platform-subtitle': 'Quantum Computing & AI Drug Design Service Platform',
      'agreement-title': 'User Agreement',
      'agreement-intro': 'Welcome to register and use the QureGenAI intelligent drug design platform and TyxonQ quantum computing services provided by Yitu Life Sciences (Suzhou) Life Science Technology Co., Ltd. Please read the following terms carefully.',
      'service-description-title': '1. Service Description',
      'service-description-intro': 'QureGenAI and TyxonQ provide cloud computing, drug simulation and design services based on AI and quantum computing, including but not limited to:',
      'service-item-1': '• Quantum computing resource access and services',
      'service-item-2': '• AI model-assisted drug design and simulation',
      'service-item-3': '• Biomedical cloud computing solutions',
      'service-item-4': '• Algorithm model development environment',
      'user-responsibility-title': '2. User Responsibilities',
      'responsibility-item-1': '1. You must ensure that the registration information provided is true and accurate',
      'responsibility-item-2': '2. You may not use the platform for any illegal activities or infringe upon the rights of third parties',
      'responsibility-item-3': '3. You may not interfere with or disrupt the normal operation of platform services',
      'responsibility-item-4': '4. You are fully responsible for account security and activities',
      'disclaimer-title': '⚠️ Important Disclaimer',
      'disclaimer-item-1': '• The platform provides all services "as is" without guaranteeing availability, accuracy or reliability',
      'disclaimer-item-2': '• All calculation results are for research reference only and do not constitute medical advice or professional opinions',
      'disclaimer-item-3': '• You understand that quantum computing services have result uncertainty, and the platform does not guarantee the accuracy of any specific results',
      'disclaimer-item-4': '• The platform is not responsible for any direct or indirect losses caused by using the services',
      'disclaimer-item-5': '• All warranty responsibilities are excluded to the maximum extent permitted by applicable law',
      'data-processing-title': '3. Data Processing and Improvement',
      'data-processing-item-1': '1. The platform will collect and use <strong>non-private operational data</strong> (such as service logs, performance metrics, usage patterns, hardware information, etc.) for product improvement',
      'data-processing-item-2': '2. You authorize the platform to use anonymized and aggregated data to improve algorithms and service quality',
      'intellectual-property-title': '4. Intellectual Property',
      'intellectual-property-content': 'The platform\'s technology, software, interface design, etc. are all intellectual property of Yitu Life Sciences (Suzhou) Life Science Technology Co., Ltd., except for open source technologies. You retain ownership of input data and output results, but authorize the platform with necessary usage rights to provide services.',
      'agreement-update-title': '5. Agreement Updates',
      'agreement-update-content': 'Yitu Life Sciences (Suzhou) Life Science Technology Co., Ltd. reserves the right to update the terms at any time. Continued use of the service is deemed acceptance of the updated terms.',
      'footer-copyright': '© 2025 QureGenAI & TyxonQ | Quantum AI Drug Development Platform',
      'footer-contact': 'Contact Email: <EMAIL>'
    },

    // AutoDock页面
    autodock: {
      'page-title': 'AutoDock High-throughput Screening and Molecular Docking',
      'page-subtitle': 'AutoDock is a widely used open-source molecular docking software suite for predicting the binding modes and affinities of small molecule ligands (such as drug candidates) with known three-dimensional structures of biological macromolecules (usually protein receptors). It helps researchers understand molecular interaction mechanisms and is widely used in drug discovery and virtual screening by simulating various possible conformations and orientations of ligands at receptor active sites and evaluating binding strength using energy scoring functions. The suite mainly includes the AutoDock program (performing docking calculations using Lamarckian genetic algorithms and empirical free energy scoring functions) and the AutoGrid program (pre-calculating grid maps to represent receptors).',
      'tutorial-btn': 'Platform Tutorial',
      'guide-title': '🎯 Docking Pocket Identification and Analysis Guide',
      'step-one-title': 'Step 1: Protein Structure Analysis',
      'step-one-item1': 'Upload protein file: First upload PDB or PDBQT format protein structure file',
      'step-one-item2': 'Observe overall structure: Rotate and zoom in the 3D viewer to familiarize with the overall protein folding structure',
      'step-one-item3': 'Find potential pockets: Look for concave areas on the protein surface, which are usually ligand binding sites',
      'step-one-item4': 'Focus on active sites: If active center residues are known, focus on observing these areas',
      'step-two-title': 'Step 2: Pocket Center Point Determination',
      'step-two-item1': 'Click atom selection: Click key atoms in the pocket (such as active site residues) in the 3D viewer',
      'step-two-item2': 'View atom information: Selected atom information will be displayed below, including residue type and coordinates',
      'step-two-item3': 'Set center point: Click atom information entries to directly set those coordinates as docking center',
      'step-two-item4': 'Multi-atom average: When multiple atoms are selected, the system will automatically calculate the geometric center',
      'step-three-title': 'Step 3: Docking Box Size Setting',
      'step-three-item1': 'Estimate pocket size: Observe the approximate size range of the target pocket',
      'step-three-item2': 'Consider ligand size: Ensure the docking box is large enough to accommodate various conformations of ligand molecules',
      'step-three-item3': 'Recommended sizes:',
      'size-small': 'Small molecule ligands: 15-20 Å',
      'size-peptide': 'Peptide ligands: 20-25 Å',
      'size-large': 'Large molecule ligands: 25-30 Å',
      'tips-title': 'Professional Advice and Precautions',
      'pocket-types-title': '🎪 Common Pocket Types:',
      'pocket-type1': 'Enzyme active sites (usually deep and narrow)',
      'pocket-type2': 'Protein-protein interaction interfaces (usually shallow and wide)',
      'pocket-type3': 'Allosteric regulatory sites (away from active center)',
      'precautions-title': '⚠️ Precautions:',
      'precaution1': 'Avoid setting the docking box too large, which will increase computation time',
      'precaution2': 'Ensure the box completely contains the target binding site',
      'precaution3': 'Consider protein flexibility, leaving space for side chain movement',
      'precaution4': 'If known complex structures are available, refer to their binding modes',
      'viewer-title': 'Molecular Structure Viewer',
      'loading': 'Loading...',
      'upload-btn': 'Click to Upload Structure Files',
      'supported-formats': 'Supported File Formats:',
      'pdb-desc': '• PDB files - Protein structures (cartoon rendering)',
      'sdf-desc': '• SDF files - Small molecule structures (ball-and-stick model)',
      'pdbqt-desc': '• PDBQT files - Charge structure files',
      'uploaded-files': 'Uploaded Files',
      'no-files': 'No files',
      'selected-atoms-info': 'Selected Atoms Information',
      'clear-all': 'Clear All',
      'atom-selection-guide': 'Atom Selection Instructions:',
      'atom-guide1': 'In the 3D molecular viewer above, click atoms on proteins or small molecules to select them',
      'atom-guide2': 'Selected atom information will be displayed in the list below, including atom type, residue information and coordinates',
      'atom-guide3': 'Click atom information entries below to directly set those atom coordinates as docking pocket center points',
      'atom-guide4': 'When multiple atoms are selected, the system will automatically calculate their geometric center as pocket center coordinates',
      'no-atoms': 'No atoms selected',
      'remove-atom': 'Remove this atom',
      'participating-calculation': '✓ Participating',
      'excluded': '✗ Excluded',
      'pocket-center-mode-title': '🎯 Docking Pocket Center Selection Mode',
      'single-atom-mode': 'Single Atom',
      'click-atom-to-set': 'Click atom to set',
      'geometric-center-mode': 'Geometric Center',
      'auto-calculate': 'Auto calculate',
      'single-atom-mode-desc': '💡 <strong>Single Atom Mode</strong>: Click any atom below to set its coordinates as pocket center',
      'geometric-center-mode-desc': '💡 <strong>Geometric Center Mode</strong>: Use the geometric center of all selected atoms as pocket center',
      'delete-file': 'Delete file',
      'uploading-files': 'Uploading files...',
      'input-params-title': 'AutoDock Input Parameter Settings',
      'docking-files-selection': '📁 Docking Files Selection',
      'files-selection-desc': 'Select protein and small molecule files for molecular docking',
      'protein-receptor-file': '🧬 Protein Receptor File (PDB)',
      'uploaded-files-selection': 'Select from uploaded files',
      'no-protein-files': 'No uploaded protein files, please first upload PDB or PDBQT files in the molecular viewer above',
      'or': 'or',
      'upload-local-receptor': 'Upload Local Receptor File',
      'click-select-pdb': 'Click to select PDB/PDBQT files',
      'or-drag': 'or',
      'drag-files-here': 'drag files here',
      'supported-pdb-formats': 'Supported formats: PDB, PDBQT',
      'selected-protein-file': 'Selected Protein File',
      'clear-selection': 'Clear Selection',
      'protein-file-hint': 'Select a PDB or PDBQT format protein file as docking receptor',
      'ligand-file': '💊 Small Molecule Ligand File',
      'select-ligand-files': 'Select Ligand Molecule Structure Files',
      'batch-smiles-file': 'Batch SMILES File',
      'batch-smiles-input': 'Batch SMILES Input',
      'select-from-uploaded-ligands': 'Select from uploaded ligand molecule files',
      'no-ligand-files': 'No uploaded molecule files, please first upload files in the molecular viewer above',
      'upload-local-ligand': 'Upload Local Ligand Files',
      'click-select-files': 'Click to select files',
      'supported-ligand-formats': 'Supported formats: PDB, SDF, PDBQT, MOL2',
      'selected-files': 'Selected Files',
      'files-count': '',
      'file-selection-hint': 'Click uploaded files or upload local files to select',
      'single-file-mode': 'Select 1 file:',
      'single-file-desc': 'Use single molecule docking mode',
      'multi-file-mode': 'Select multiple files:',
      'multi-file-desc': 'Use multi-molecule batch docking mode',
      'upload-csv': 'Upload CSV File',
      'csv-format-desc': 'CSV format: first column for name, second column for smiles',
      'csv-example': 'Example: aspirin,CC(=O)OC1=CC=CC=C1C(=O)O',
      'molecule-list': 'Molecule List',
      'molecules-count': '',
      'batch-docking-info': '💊 All molecules will be used for batch docking',
      'search-placeholder': 'Search molecule names...',
      '2d-structure': '2D Structure',
      'csv-upload-hint': 'Upload CSV file containing molecule names and SMILES, all molecules will be automatically used for batch docking',
      'direct-smiles-input': 'Direct SMILES String Input',
      'smiles-placeholder': 'Enter one SMILES string per line, for example:\nCCO\nCC(=O)OC1=CC=CC=C1C(=O)O\nCN1C=NC2=C1C(=O)N(C(=O)N2C)C',
      'parse-smiles': 'Parse SMILES',
      'clear-text': 'Clear Text',
      'smiles-input-hint': 'Enter one SMILES string per line, the system will automatically generate names for each molecule',
      'docking-params-title': '⚙️ Docking Parameters',
      'docking-params-desc': 'Set the spatial range and calculation parameters for molecular docking',
      'pocket-center-coords': '🎯 Pocket Center Coordinates',
      'x-coord': 'X Coordinate',
      'y-coord': 'Y Coordinate',
      'z-coord': 'Z Coordinate',
      'auto-fill-coords': 'Auto Fill',
      'manual-input-hint': 'Manually input coordinates or click button to calculate center coordinates from selected atoms',
      'docking-box-size': '📦 Docking Box Size (Å)',
      'x-size': 'X size',
      'y-size': 'Y size',
      'z-size': 'Z size',
      'box-size-hint': 'Define the size of the search space, default is 15Å × 15Å × 15Å',
      'threads-label': '⚡ Threads for computation',
      'threads-unit': 'threads',
      'threads-hint': 'Set the number of threads for computation, recommended: 2000-5000',
      'validate-inputs': '✅ Validate Inputs',
      'start-docking': '🚀 Start Docking',
      'wait-submit': 'Submitting...',
      'ok-submit': 'Submitted',
      'reset-params': '🔄 Reset Parameters',
      'docking-results': '📊 Docking Results',
      'waiting-results': 'Waiting Results...',
      'hide-results-btn': 'Hide Results',
      'task-info': 'Task Information',
      'task-id': 'Task ID:',
      'task-status': 'Status:',
      'results-details': 'Results Details',
      'scores-tab': 'Scores',
      'files-tab': 'Files',
      'model-extraction-title': 'Model Extraction Tool',
      'model-extraction-desc': 'Extract specified conformations from docking results and convert to PDB format',
      'select-pdbqt-file': 'Select PDBQT docking results file:',
      'select-model-number': 'Select Conformation Number:',
      'default-model-number': '(Default extract the 1st conformation)',
      'extract-and-convert': '⚡ Extract and Convert to PDB',
      'task-history': 'Docking Task History',
      'refresh-tasks': 'Refresh',
      'local': 'Local',
      'file-added': 'File added',
      'file-exists': 'File already exists',
      'protein-file-selected': 'Protein file selected',
      'waiting-results': 'Waiting Results...',
      'processing': 'Processing...',
      'completed': 'Docking calculation completed',
      'failed': 'Calculation failed',
      'running': 'Running...',
      'pending': 'Queued...',
      'error': 'Error occurred',
      'success': 'Successfully completed',
      'cancelled': 'Cancelled',
      'timeout': 'Calculation timeout',
      'no-results': 'No detailed result information',
      'hide-results': 'Hide',
      'show-results': 'Show',
      'view-task-details': 'View Task Details',
      'view-task-results': 'View Task Results',
      'created-time': 'Created Time',
      'updated-time': 'Updated Time',
      'completed-time': 'Completed Time',
      'error-message': 'Error Message',
      'parameters': 'Parameters',
      'task-completed-summary': 'Task {taskId} completed {count} docking calculations.',
      'task-summary-keyword1': 'completed',
      'task-summary-keyword2': 'docking calculations',
      'task-general-keyword1': 'Task',
      'task-general-keyword2': 'completed',
      'no-autodock-tasks': 'No AutoDock task history',
      'no-scores-data': 'No scores data',
      'no-files-available': 'No files available',
      'input-validation-failed': 'Input validation failed:',
      'incomplete-pocket-coords': 'Please enter complete pocket center coordinates',
      'validation-passed': 'Input validation passed!',
      'batch-smiles-mode': 'Batch SMILES mode: will dock',
      'molecules': 'molecules',
      'multi-file-mode-confirm': 'Multi-molecule file mode: will dock',
      'files': 'molecule files',
      'single-file-mode-confirm': 'Single molecule file mode',
      'single-smiles-mode': 'Single molecule SMILES mode',
      'docking-task-submitted': 'Docking task submitted! Task ID:',
      'login-expired': 'Login status expired, please log in again',
      'login-abnormal': 'Login status abnormal, please log in again',
      'docking-request-failed': 'Docking request failed:',
      'task-results-unavailable': 'Task results unavailable',
      'get-task-results-error': 'Error getting task results',
      'get-task-details-failed': 'Failed to get task details:',
      'get-task-details-error': 'Error getting task details',
      'please-select-csv': 'Please select CSV format file',
      'no-valid-molecules-in-csv': 'No valid molecule data found in CSV file',
      'successfully-parsed-molecules': 'Successfully parsed',
      'csv-parse-failed': 'CSV file parsing failed:',
      'text-input-area-not-found': 'Text input area not found',
      'please-input-smiles': 'Please input SMILES strings',
      'no-valid-smiles-found': 'No valid SMILES strings found',
      'smiles-parse-failed': 'SMILES text parsing failed:',
      'batch-docking-confirm-title': 'You are about to start batch molecular docking:',
      'molecule-count': '• Number of molecules:',
      'docking-mode': '• Docking mode:',
      'batch-smiles-file-mode': 'Batch SMILES file',
      'batch-smiles-input-mode': 'Batch SMILES input',
      'all-molecules-will-dock': '• All molecules will be docked simultaneously',
      'confirm-continue': 'Are you sure you want to continue?',
      'multi-file-docking-confirm-title': 'You are about to start multi-molecule file docking:',
      'molecule-file-count': '• Number of molecule files:',
      'multi-molecule-files-mode': 'Multi-molecule structure files',
      'prev-page': 'Previous',
      'next-page': 'Next',
      'showing-items': 'Showing',
      'of-total-items': 'of',
      'total-tasks': 'tasks',
      'single-atom-mode': 'Single Atom Mode',
      'center-mode': 'Geometric Center Mode',
      'single-atom-desc': 'Click atom to set',
      'center-desc': 'Auto calculate',
      'single-atom-mode-desc': 'Click any atom below to set its coordinates as pocket center',
      'center-mode-desc': 'Use geometric center of all selected atoms as pocket center',
      'pocket-center-mode': 'Docking Pocket Center Selection Mode',
      'participate-calculation': 'Participate in calculation',
      'excluded': 'Excluded'
    }
  }
})

// 切换语言函数
export const switchLanguage = (lang) => {
  currentLanguage.value = lang
  localStorage.setItem('language', lang)

  // 更新页面语言
  document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en'

  // 更新文档标题
  document.title = lang === 'zh' ? 'QureGenAI 药物设计平台 - 主页' : 'QureGenAI Drug Design Platform - Home'

  // 触发存储事件，通知其他组件
  window.dispatchEvent(new StorageEvent('storage', {
    key: 'language',
    newValue: lang
  }))
}

// 获取翻译文本 - 支持嵌套路径
export const t = (key) => {
  const keys = key.split('.')
  let result = translations[currentLanguage.value]

  for (const k of keys) {
    if (result && typeof result === 'object') {
      result = result[k]
    } else {
      return key // 如果找不到翻译，返回原key
    }
  }

  return result || key
}

// 初始化语言设置
export const initLanguage = () => {
  const lang = currentLanguage.value
  document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en'
  document.title = lang === 'zh' ? 'QureGenAI 药物设计平台 - 主页' : 'QureGenAI Drug Design Platform - Home'
}
