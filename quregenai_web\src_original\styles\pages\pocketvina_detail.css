* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f7fa;
    height: 100vh;
    overflow-x: hidden;
}

.header {
    background: white;
    height: 60px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.back-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.back-btn:hover {
    background: #3730a3;
}

.content {
    margin-top: 60px;
    padding: 2rem;
    min-height: calc(100vh - 60px);
}

.page-title {
    font-size: 0.8rem;
    color: #333;
    margin-bottom: 0.6rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: normal;  /* 明确设置为正常字体粗细 */
}

.page-subtitle {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.task-info-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.info-value {
    font-size: 1rem;
    color: #333;
    font-weight: 500;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    width: fit-content;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.status-running {
    background: #fef3c7;
    color: #92400e;
}

.status-failed {
    background: #fee2e2;
    color: #991b1b;
}

.files-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
}

.section-title {
    font-size: 1rem;
    color: #333;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.file-item {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.2s ease;
}

.file-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
}

.file-name {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #333;
    margin-bottom: 0.5rem;
    word-break: break-all;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #666;
}

.file-type {
    background: #e1e5e9;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
}

.download-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.75rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.download-btn:hover {
    background: #3730a3;
}

.download-all-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.download-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.download-all-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.loading {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.error {
    text-align: center;
    padding: 3rem;
    color: #ef4444;
}

.files-categories {
    display: grid;
    grid-template-columns: 1fr; /* 第一行：汇总文件独占一行 */
    grid-template-rows: auto auto; /* 两行布局 */
    gap: 2rem;
    margin-bottom: 2rem;
}

/* 汇总文件独占第一行 */
.file-category:nth-child(1) { 
    grid-column: 1;
    grid-row: 1;
}

/* 其他文件分类在第二行，使用子网格 */
.files-categories-row2 {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr; /* 蛋白质(窄) 对接结果(宽) 日志(窄) */
    gap: 2rem;
    grid-column: 1;
    grid-row: 2;
}

@media (max-width: 1200px) {
    .files-categories-row2 {
        grid-template-columns: 1fr 1fr; /* 中等屏幕：2列 */
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .files-categories-row2 {
        grid-template-columns: 1fr; /* 小屏幕：1列 */
        gap: 1rem;
    }
}


.file-category {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.category-title {
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 0.5rem;
}

.category-files {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    /* 添加滚动支持 */
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
.category-files::-webkit-scrollbar {
    width: 6px;
}

.category-files::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.category-files::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.category-files::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Firefox 滚动条样式 */
.category-files {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}


.file-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.2s ease;
}

.file-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
}

.file-name {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #333;
    margin-bottom: 0.5rem;
    word-break: break-all;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #666;
}

.file-type {
    background: #e1e5e9;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
    font-weight: 500;
}

.file-type.csv {
    background: #d1fae5;
    color: #065f46;
}

.file-type.pdbqt {
    background: #dbeafe;
    color: #1e40af;
}

.file-type.json {
    background: #fef3c7;
    color: #92400e;
}

.file-type.log {
    background: #f3e8ff;
    color: #7c2d12;
}

.download-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.75rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s ease;
}

.download-btn:hover {
    background: #3730a3;
    transform: scale(1.05);
}

.no-files-category {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 1rem;
}

@media (max-width: 768px) {
    .files-categories {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* 3D渲染区域样式 */
.molecule-rendering-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.rendering-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    align-items: start;
}

.viewer-container {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-radius: 0.75rem;
    min-height: 400px;
    position: relative;
    overflow: hidden;
}

.molecule-viewer {
    width: 100% !important;
    height: 400px;
    position: relative;
    min-width: 100% !important;
    overflow: hidden;
}

/* 强制3Dmol查看器使用完整宽度 */
.molecule-viewer canvas {
    width: 100% !important;
    max-width: none !important;
    min-width: 100% !important;
}

/* 更具体的3Dmol容器样式 */
.molecule-viewer > div {
    width: 100% !important;
    height: 100% !important;
}

/* 针对3Dmol生成的所有子元素 */
.molecule-viewer * {
    max-width: none !important;
}

.file-selector {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 0.75rem;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.file-selector h4 {
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 0.5rem;
}

.file-list-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.file-list-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
}

.file-list-item.selected {
    border-color: #4f46e5;
    background: #eef2ff;
}

.file-list-item input[type="checkbox"] {
    margin-right: 0.75rem;
}

.file-info {
    flex: 1;
}

.file-info-name {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #333;
    margin-bottom: 0.25rem;
}

.file-info-type {
    font-size: 0.75rem;
    color: #666;
}

.viewer-controls {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.control-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: #3730a3;
}

.control-btn.danger {
    background: #ef4444;
}

.control-btn.danger:hover {
    background: #dc2626;
}

.viewer-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #9ca3af;
    text-align: center;
}

.viewer-placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 滚动条样式 */
.file-selector::-webkit-scrollbar {
    width: 6px;
}

.file-selector::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.file-selector::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.file-selector::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@media (max-width: 768px) {
    .rendering-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* 添加标签页样式 */
.file-tabs {
    display: flex;
    border-bottom: 2px solid #e1e5e9;
    margin-bottom: 1rem;
    flex-wrap: wrap; /* 允许换行显示 */
    gap: 0.25rem; /* 标签之间的间距 */
}

.file-tab {
    padding: 0.75rem 1rem;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-bottom: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 0.875rem;
    font-weight: 500;
    color: #666;
    flex: 0 0 auto; /* 不允许缩放，保持原始大小 */
    min-width: fit-content; /* 确保内容完全显示 */
    border-radius: 0.5rem 0.5rem 0 0; /* 统一圆角样式 */

}

.file-tab:first-child {
    border-top-left-radius: 0.5rem;
}

.file-tab:last-child {
    border-top-right-radius: 0.5rem;
}

.file-tab:hover {
    background: #e9ecef;
    color: #333;
}

.file-tab.active {
    background: white;
    color: #4f46e5;
    border-color: #4f46e5;
    border-bottom: 2px solid white;
    margin-bottom: -2px;
    z-index: 1;
    position: relative;
}

.file-tab-content {
    display: none;
}

.file-tab-content.active {
    display: block;
}

/* 滚动条样式
.file-tabs::-webkit-scrollbar {
    height: 4px;
} */

/* 响应式设计：在小屏幕上调整标签样式 */
@media (max-width: 768px) {
    .file-tab {
        font-size: 0.8rem;
        padding: 0.6rem 0.8rem;
    }
}


.file-tabs::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.file-tabs::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.file-tabs::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}


/* CSV预览样式 */
.csv-preview {
    margin-top: 1rem;
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    background: #f8f9fa;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #e9ecef;
    border-bottom: 1px solid #e1e5e9;
    font-weight: 500;
    color: #333;
}

.close-preview-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.close-preview-btn:hover {
    background: #dc3545;
    color: white;
}

.preview-content {
    padding: 1rem;
    max-height: 500px;
    overflow: auto;
}

.csv-info {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: white;
    border-radius: 0.375rem;
    border: 1px solid #e1e5e9;
}

.csv-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.stat-item {
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 500;
    color: #495057;
}

.csv-message {
    font-size: 0.875rem;
    color: #666;
    font-style: italic;
}

.csv-table-container {
    overflow-x: auto;
    border-radius: 0.375rem;
    border: 1px solid #e1e5e9;
    background: white;
}

.csv-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.csv-table th,
.csv-table td {
    padding: 0.5rem 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
    border-right: 1px solid #e1e5e9;
}

.csv-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.csv-table th:last-child,
.csv-table td:last-child {
    border-right: none;
}

.csv-table tbody tr:hover {
    background: #f8f9fa;
}

.csv-table td {
    color: #666;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.preview-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    margin-right: 0.5rem;
}

.preview-btn:hover {
    background: #218838;
    transform: scale(1.05);
}

.file-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.loading-preview {
    text-align: center;
    color: #666;
    padding: 1rem;
}

.preview-error {
    text-align: center;
    color: #dc3545;
    padding: 1rem;
}

.no-preview-data {
    text-align: center;
    color: #6c757d;
    padding: 1rem;
}

/* 预览容器滚动条样式 */
.preview-content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.preview-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.preview-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
