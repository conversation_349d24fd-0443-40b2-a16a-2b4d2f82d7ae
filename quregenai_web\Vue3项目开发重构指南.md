# Vue3 项目重构指南

## 项目概述
本文档为 QureGenAI 药物设计平台的 Vue3 项目重构提供标准化指导，确保代码结构清晰、可维护性强、符合现代前端开发最佳实践。

## 1. CSS 样式管理规范

### 1.1 样式文件组织
- **强制要求**：每个 Vue 页面必须创建对应的 CSS 文件
- **文件位置**：`src/assets/styles/` 目录下
- **命名规范**：与 Vue 文件同名，使用小写字母和连字符
- **引用方式**：
```vue
<style scoped>
@import '../assets/styles/login.css';
</style>
```

### 1.2 样式文件结构
```
src/assets/styles/
├── login.css          # 登录页面样式
├── home.css           # 首页样式
├── reset-password.css # 重置密码页面样式
├── register.css       # 注册页面样式
└── autodock.css       # AutoDock 页面样式
```

### 1.3 样式编写规范
- **强制使用** `scoped` 避免样式污染
- **优先使用** CSS 变量定义颜色、间距、字体等
- **推荐使用** 通用样式类减少重复代码
- **遵循** BEM 命名规范进行自定义样式命名
- **响应式设计优先**，使用媒体查询适配移动端

## 2. Vue3 开发规范

### 2.1 组合式 API (Composition API)
- **强制使用** `<script setup>` 语法
- 使用 `ref()` 和 `reactive()` 管理响应式数据
- 使用 `computed()` 处理计算属性
- 使用 `watch()` 和 `watchEffect()` 监听数据变化

### 2.2 标准模板结构
```vue
<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 响应式数据
const formData = reactive({
  username: '',
  password: ''
})

// 计算属性
const isFormValid = computed(() => {
  return formData.username && formData.password
})

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<template>
  <!-- 模板内容 -->
</template>

<style scoped>
@import '../assets/styles/component-name.css';
</style>
```

### 2.3 组件通信
- Props 使用 `defineProps()`
- Events 使用 `defineEmits()`
- 复杂状态使用 Pinia 或 provide/inject

## 3. 身份验证与权限管理

### 3.1 统一认证策略
- **核心原则**：只在 `App.vue` 中进行全局登录校验
- **禁止**：在各个页面组件中重复进行登录验证
- **认证工具**：使用 `src/utils/auth.js` 中的函数

### 3.2 认证函数使用
```javascript
import { 
  isAuthenticated, 
  getUserInfo, 
  checkAuthStatus, 
  doLogout 
} from '@/utils/auth.js'

// 检查登录状态
if (isAuthenticated()) {
  // 已登录逻辑
}

// 获取用户信息
const userInfo = getUserInfo()

// 检查服务器端认证状态
checkAuthStatus().then(result => {
  // 处理认证结果
})
```

### 3.3 App.vue 中的全局认证
- 在 `App.vue` 的 `mounted` 钩子中进行登录状态检查
- 设置心跳检测机制（当前为5分钟间隔）
- 自动处理登录过期和重定向

## 4. 国际化 (i18n) 管理

### 4.1 多语言配置
- **配置文件**：`src/utils/i18n.js`
- **支持语言**：中文 (zh)、英文 (en)
- **存储方式**：localStorage 持久化
- **组织结构**：按页面/模块分组管理翻译文本

### 4.2 使用方式
```javascript
import { t, switchLanguage, currentLanguage } from '@/utils/i18n.js'

// 获取翻译文本 - 支持嵌套路径
const title = t('common.platform-name')
const loginTitle = t('login.title')
const homeWelcome = t('home.welcome-title')

// 切换语言
switchLanguage('en')

// 监听语言变化
watch(currentLanguage, (newLang) => {
  // 语言切换后的处理逻辑
})
```

### 4.3 翻译文本组织结构
翻译文本按页面/功能模块进行分组管理：

```javascript
export const translations = reactive({
  zh: {
    // 通用组件
    common: {
      'platform-name': 'QureGenAI 药物设计平台',
      'logout-btn': '退出登录',
      'network-error': '发生网络错误，请稍后重试'
    },
    
    // 首页
    home: {
      'welcome-title': '欢迎使用 QureGenAI 药物设计平台',
      'welcome-text': '欢迎来到 QureGenAI！...'
    },
    
    // 登录页面
    login: {
      'title': 'QureGenAI 药物设计 & TyxonQ 量子计算平台',
      'subtitle': '请输入您的账户信息以继续'
    },
    
    // 注册页面
    register: {
      'title': '注册账户',
      'subtitle': '创建您的QureGenAI药物设计平台账户'
    },
    
    // 重置密码页面
    reset: {
      'title': '找回密码',
      'subtitle': '请填写信息重置您的密码'
    },
    
    // 功能模块
    modules: {
      'autodock-desc': '高通量筛选与分子对接',
      'diffdock-desc': '口袋预测与分子对接'
    }
  },
  en: {
    // 对应的英文翻译...
  }
})
```

### 4.4 翻译文本管理规范
- **强制要求**：所有用户可见文本必须通过 `t()` 函数获取
- **命名规范**：使用 `页面/模块.功能-描述` 的格式
- **新增流程**：
  1. 在对应页面/模块分组下添加中文翻译
  2. 同时添加对应的英文翻译
  3. 在组件中使用 `t('页面.key')` 获取文本
- **嵌套支持**：支持多层嵌套路径，如 `t('home.features.autodock')`

### 4.5 语言切换组件
在需要语言切换的页面添加语言切换按钮：

```vue
<template>
  <div class="language-switcher">
    <button 
      class="language-btn" 
      :class="{ active: currentLanguage === 'zh' }" 
      @click="switchLanguage('zh')"
    >
      🇨🇳 中文
    </button>
    <button 
      class="language-btn" 
      :class="{ active: currentLanguage === 'en' }" 
      @click="switchLanguage('en')"
    >
      🇺🇸 EN
    </button>
  </div>
</template>

<script setup>
import { switchLanguage, currentLanguage } from '@/utils/i18n.js'
</script>
```

### 4.6 最佳实践
- **响应式更新**：使用 `currentLanguage` 响应式变量监听语言变化
- **页面标题**：语言切换时自动更新页面标题
- **持久化存储**：语言选择自动保存到 localStorage
- **初始化**：页面加载时自动应用用户上次选择的语言
- **事件通知**：语言切换时触发 storage 事件，通知其他组件更新

## 5. 组件职责分离

### 5.1 页面组件职责
- **专注**：页面特定的业务逻辑和UI
- **避免**：通用功能的重复实现
- **原则**：单一职责，高内聚低耦合

### 5.2 通用功能抽象
- **工具函数**：放置在 `src/utils/` 目录
- **可复用组件**：放置在 `src/components/` 目录
- **业务逻辑**：使用 Composables (`src/composables/`)

### 5.3 Composables 使用
```javascript
// src/composables/useAuth.js
import { ref } from 'vue'
import { isAuthenticated, getUserInfo } from '@/utils/auth.js'

export function useAuth() {
  const user = ref(null)
  const isLoggedIn = ref(false)

  const checkAuth = () => {
    isLoggedIn.value = isAuthenticated()
    if (isLoggedIn.value) {
      user.value = getUserInfo()
    }
  }

  return {
    user,
    isLoggedIn,
    checkAuth
  }
}
```

## 6. API 请求管理

### 6.1 API 文件组织
- **位置**：`src/api/` 目录
- **结构**：按功能模块分文件
- **示例**：参考 `src/api/login.js`

### 6.2 API 函数规范
```javascript
// src/api/user.js
import api from './index'

// 获取用户信息
export function getUserProfile() {
  return api.get('/user/profile')
}

// 更新用户信息
export function updateUserProfile(data) {
  return api.put('/user/profile', data)
}
```

### 6.3 请求拦截器
- 统一错误处理：`src/api/interceptors.js`
- 自动添加认证头
- 响应数据格式化

## 7. 项目结构最佳实践

### 7.1 目录结构
```
src/
├── api/                 # API 请求函数
├── assets/             
│   ├── images/         # 图片资源
│   └── styles/         # CSS 样式文件
├── components/         # 可复用组件
│   └── layout/         # 布局组件
├── composables/        # 组合式函数
├── router/             # 路由配置
├── utils/              # 工具函数
├── views/              # 页面组件
├── App.vue             # 根组件
└── main.js             # 入口文件
```

### 7.2 命名规范
- **文件名**：使用 PascalCase (组件) 或 kebab-case (工具)
- **组件名**：使用 PascalCase
- **函数名**：使用 camelCase
- **常量**：使用 UPPER_SNAKE_CASE

## 8. 性能优化建议

### 8.1 代码分割
- 使用动态导入进行路由懒加载
- 大型组件按需加载

### 8.2 响应式优化
- 合理使用 `ref` vs `reactive`
- 避免不必要的响应式包装
- 使用 `shallowRef` 和 `shallowReactive` 优化深层对象

### 8.3 组件优化
- 使用 `v-memo` 缓存复杂列表项
- 合理使用 `keep-alive`
- 避免在模板中使用复杂表达式

## 9. 开发工具配置

### 9.1 必需依赖
- Vue 3.5.13+
- Vue Router 4.5.1+
- Element Plus 2.10.1+
- Axios 1.9.0+

## 10. 常见问题解决

### 10.1 样式问题
- 样式不生效：检查 `scoped` 和导入路径
- 样式冲突：使用更具体的选择器或 CSS Modules

### 10.2 响应式问题
- 数据不更新：检查是否正确使用 `ref` 或 `reactive`
- 性能问题：避免过度响应式包装

### 10.3 路由问题
- 页面不跳转：检查路由配置和导航守卫
- 认证失效：确保在 `App.vue` 中正确处理

---

**注意**：本指南应作为重构过程中的参考文档，建议在开始重构前仔细阅读，并在开发过程中持续参考。如有疑问，请及时与团队沟通确认。