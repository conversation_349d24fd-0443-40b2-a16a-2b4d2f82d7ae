* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
  /* 确保可以垂直滚动 */
}

button:focus {
  outline: none;
}

/* 语言切换按钮 */
.language-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.language-btn {
  padding: 8px 16px;
  border: 2px solid #2c5f5f;
  background: white;
  color: #2c5f5f;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.language-btn:hover {
  background: #f0f8f8;
}

.language-btn.active {
  background: #2c5f5f;
  color: white;
}

.terms-page {
  background: #f5f5f5 !important;
  min-height: 100vh;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  width: 100%;
  overflow-y: auto;
  /* 确保可以滚动 */
}

.agreement-container {
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #2c5f5f 0%, #1a4a4a 100%);
  color: white;
  padding: 40px 30px;
  text-align: center;
  position: relative;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 10px;
  line-height: 1.2;
}

.logo-icon {
  width: 36px;
  height: 36px;
  background: radial-gradient(circle, #fff 30%, transparent 70%);
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.logo-icon::after {
  content: "Ψ";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
  font-weight: bold;
}

.logo span {
  font-size: 1.8rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 500px;
  margin: 0 auto;
}

.content {
  padding: 40px;
  max-height: none;
  overflow: visible;
}

.section {
  margin-bottom: 35px;
  border-left: 4px solid #2c5f5f;
  padding-left: 15px;
  transition: all 0.3s ease;
}

.section:hover {
  border-left: 4px solid #1a4a4a;
}

.section-title {
  font-size: 1.4rem;
  color: #2c5f5f;
  margin-bottom: 15px;
  font-weight: 600;
}

.section>p {
  margin-bottom: 15px;
  color: #333;
  line-height: 1.6;
}

.subsection {
  margin-left: 20px;
  margin-bottom: 15px;
}

.subsection p {
  margin-bottom: 10px;
  color: #333;
  line-height: 1.6;
}

.highlight-box {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 25px 0;
  border: 1px solid #e9ecef;
}

.highlight-title {
  font-weight: 600;
  color: #2c5f5f;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.highlight-title i {
  font-size: 1.2rem;
}

.footer {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 0.9rem;
  border-top: 1px solid #e9ecef;
  margin-top: 20px;
  background-color: #f8f9fa;
}

.footer p {
  margin-bottom: 5px;
}

.footer p:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .terms-page {
    padding: 10px;
  }

  .agreement-container {
    margin: 0;
    border-radius: 8px;
  }

  .content {
    padding: 30px 20px;
  }

  .header {
    padding: 30px 20px;
  }

  .language-switcher {
    top: 0.5rem;
    right: 0.5rem;
  }

  .logo {
    font-size: 1.5rem;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    text-align: center;
  }

  .logo span {
    font-size: 1.5rem;
  }

  .logo-icon {
    margin-top: 0;
  }
}