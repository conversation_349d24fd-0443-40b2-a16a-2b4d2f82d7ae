/* Reset Password Page Styles */

/* CSS Variables - 内联定义 */
:root {
  /* 主色调 */
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-hover: #5a6fd8;
  
  /* 状态颜色 */
  --success-color: #27ae60;
  --success-bg: #f0fff4;
  --success-border: #9ae6b4;
  
  --error-color: #e74c3c;
  --error-bg: #fdf2f2;
  --error-border: #fbb6ce;
  
  --warning-color: #f57c00;
  --warning-bg: #fff3e0;
  
  --info-color: #1976d2;
  --info-bg: #e3f2fd;
  
  /* 中性色 */
  --text-primary: #333;
  --text-secondary: #666;
  --text-muted: #999;
  
  --border-color: #e1e5e9;
  --border-light: #f0f0f0;
  
  --bg-white: #ffffff;
  --bg-light: #f8f9fa;
  --bg-disabled: #bdc3c7;
  
  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* 圆角 */
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
  --border-radius-full: 50%;
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 25px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 25px rgba(102, 126, 234, 0.3);
  --shadow-xl: 0 15px 35px rgba(0, 0, 0, 0.1);
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

.reset-password-page {
  font-family: var(--font-family);
  background: var(--primary-gradient);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* 语言切换按钮样式 */
.language-switcher {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: var(--spacing-sm);
  z-index: var(--z-dropdown);
}

.language-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.language-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.language-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-primary);
  border-color: rgba(255, 255, 255, 0.9);
}

.reset-password-container {
  background: var(--bg-white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 400px;
  margin: var(--spacing-md);
  overflow-y: auto;
  max-height: calc(100vh - 2rem);
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.reset-password-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.reset-password-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.reset-password-header h1 {
  color: var(--text-primary);
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-sm);
}

.reset-password-header p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 表单样式使用通用样式 */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 500;
  text-align: left;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  transition: border-color var(--transition-slow);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group input.valid {
  border-color: var(--success-color);
}

.form-group input.invalid {
  border-color: var(--error-color);
}

.input-with-button {
  display: flex;
  gap: var(--spacing-sm);
}

.input-with-button input {
  flex: 1;
}

.verification-btn {
  white-space: nowrap;
  padding: 0 var(--spacing-md);
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  height: auto;
  align-self: stretch;
}

.verification-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.verification-btn:active {
  transform: translateY(0);
}

.verification-btn:disabled {
  background: var(--bg-disabled);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.password-requirements {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-sm);
  line-height: 1.4;
}

.requirement {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.requirement.neutral .requirement-icon {
  background: var(--bg-disabled);
  color: white;
}

.requirement-icon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-sm);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

.requirement.valid .requirement-icon {
  background: var(--success-color);
  color: white;
}

.requirement.invalid .requirement-icon {
  background: var(--error-color);
  color: white;
}

.reset-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: 600;
  cursor: pointer;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  margin-bottom: var(--spacing-md);
}

.reset-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.reset-btn:active {
  transform: translateY(0);
}

.reset-btn:disabled {
  background: var(--bg-disabled);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.back-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--bg-white);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.back-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.back-btn:active {
  transform: translateY(0);
}

.error-message, .success-message {
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-sm);
  padding: 0.75rem;
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-md);
}

.error-message {
  background: var(--error-bg);
  color: var(--error-color);
  border: 1px solid var(--error-border);
}

.success-message {
  background: var(--success-bg);
  color: var(--success-color);
  border: 1px solid var(--success-border);
  text-align: center;
}

.auth-divider {
  text-align: center;
  margin: var(--spacing-lg) 0;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
}

.auth-divider span {
  background: var(--bg-white);
  padding: 0 var(--spacing-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .reset-password-container {
    margin: var(--spacing-sm);
    padding: var(--spacing-lg);
  }
  
  .reset-password-header h1 {
    font-size: var(--font-size-2xl);
  }
}